#include <lvgl.h>
#include "ui.h"
#include "esp_heap_caps.h"
#include "esp_timer.h"
#include "FT6336U.h"
#include <Arduino_GFX_Library.h>
#include <WiFi.h>
#include <WiFiManager.h>
#include <ArduinoOTA.h>
#include <HTTPClient.h>
#include <Update.h>
#include <ArduinoJson.h>
#include "esp_ota_ops.h"
#include "EEPROM.h"
#include <FreeRTOS.h>
#include <Arduino.h>
#include <SPI.h>
#include "build_timestamp.h"
#include "nvs_flash.h"
#include "nvs.h"

// 外部字體聲明
extern const lv_font_t ui_font_NS_19;

// 定義腳位
const int ADC_PIN = 14;

// 編譯日期時間
const char* build_date = __DATE__ " " __TIME__;
static char firmware_timestamp[16] = {0};  // 用於存儲從NVS讀取的時間戳
const char* build_timestamp = firmware_timestamp;  // 指向實際使用的時間戳

const int OD_PINS[] = {47, 48, 45, 38, 39, 40, 41};
const int OD_COUNT = sizeof(OD_PINS) / sizeof(OD_PINS[0]);
const int HEATER = OD_PINS[6];
const int Pump = OD_PINS[5];

/* ADC Raw 對應「補償後顯示溫度」(°C)。
 * 索引 = °C (20~140)，值 = ADC Raw (uint16_t)。
 * 由 g_ntc_adc_tbl[] 與 ntc_delta_tbl_20_140[] 線性反推計算。
 * 注意：>115°C 為補償外推區 (推估)。
 */
static const uint16_t g_ntc_adc_tbl_smooth[121] = {  // 20~140°C，共121個元素
    3642, 3624, 3606, 3588, 3569, 3549, 3529, 3509, 3488, 3467,
    3445, 3423, 3400, 3377, 3353, 3329, 3305, 3280, 3254, 3229,
    3202, 3176, 3149, 3122, 3094, 3066, 3037, 3008, 2979, 2950,
    2920, 2890, 2860, 2830, 2799, 2768, 2737, 2706, 2674, 2643,
    2611, 2579, 2548, 2516, 2484, 2452, 2419, 2387, 2355, 2323,
    2291, 2259, 2228, 2196, 2164, 2133, 2101, 2070, 2039, 2008,
    1977, 1947, 1916, 1886, 1856, 1827, 1797, 1768, 1739, 1711,
    1683, 1655, 1627, 1600, 1573, 1546, 1519, 1493, 1468, 1442,
    1417, 1392, 1368, 1344, 1320, 1297, 1274, 1251, 1228, 1206,
    1185, 1163, 1142, 1122, 1101, 1081, 1062, 1042, 1023, 1005,
     986,  968,  951,  933,  916,  899,  883,  867,  851,  835,
     820,  805,  790,  776,  761,  747,  734,  720,  707,  694,
     682
};

// 2. 實作反查函式：給定 raw_adc，回傳對應的溫度值（20~140°C）
static int16_t lookup_temp_from_adc(uint32_t raw_adc) {
    // 邊界處理：低於20°C的都顯示20°C，高於140°C的都顯示140°C
    if (raw_adc >= g_ntc_adc_tbl_smooth[0])   return 20;     // ADC值太高，溫度低於20°C，返回20°C
    if (raw_adc <= g_ntc_adc_tbl_smooth[120]) return 140;    // ADC值太低，溫度高於140°C，返回140°C

    // 線性搜尋（表格長度121，效能足夠）
    // 注意：ADC值隨溫度上升而下降，索引0對應20°C，索引120對應140°C
    for (int i = 1; i <= 120; i++) {
        uint16_t prev = g_ntc_adc_tbl_smooth[i - 1];  // 前一個溫度的ADC值（較低溫度）
        uint16_t cur  = g_ntc_adc_tbl_smooth[i];      // 當前溫度的ADC值（較高溫度）
        if (raw_adc >= cur) {
            // 決定取前一格或這一格，依哪個差值較小
            int temp_index = ( (prev - raw_adc) < (raw_adc - cur) ) ? (i - 1) : i;
            return 20 + temp_index;  // 索引轉換為實際溫度值（20°C起始）
        }
    }
    return 140;  // 預設返回最高溫度
}


// 全域中斷旗標
volatile bool cancel_flag = false;
// 全域狀態旗標：蒸氣狀態 on/off
static volatile bool steam_active = false;
static volatile bool steam_cancel = false;
static bool heater_on = false;
static bool wait_for_temp = false;
// 移除舊的溫度採樣變數，統一使用NTC處理器
// static int temp_sum = 0;
// static int temp_count = 0;
static volatile int last_avg_temp = 0;
static volatile bool active = false;   // 是否進入恆溫模式
static bool screen_transitioned = false;
static lv_timer_t * temp_timer;
static lv_timer_t * steam_temp_timer = NULL;  // 蒸氣畫面溫度更新定時器

// 統一溫度控制系統
typedef enum {
    TEMP_MODE_STANDBY,      // 待機模式：80°C
    TEMP_MODE_COFFEE,       // 咖啡模式：85°C
    TEMP_MODE_TEA,          // 茶品模式：95°C
    TEMP_MODE_WATER,        // 熱水模式：85°C
    TEMP_MODE_STEAM,        // 蒸氣模式：115°C
    TEMP_MODE_SLEEP,        // 休眠模式：60°C
    TEMP_MODE_DEEP_SLEEP    // 深熱休眠模式：完全關閉加熱器
} temp_mode_t;

// 統一溫度管理變數
static temp_mode_t current_temp_mode = TEMP_MODE_SLEEP;
static int target_temp = 80;           // 目標溫度
static bool temp_ready = false;        // 溫度是否達到目標
static bool pump_on = false;           // 水泵狀態

// 深度休眠控制變數
static unsigned long sleep_mode_start_time = 0;  // 進入休眠模式的時間
static bool deep_sleep_active = false;          // 是否處於深度休眠狀態

// 統一控制函數聲明
static void set_heater(bool state);
static void set_pump(bool state);
static void set_target_temperature(int temp);
static void unified_temp_control_task(void *pvParameters);

// OTA相關變數
static bool ota_mode = false;
static unsigned long ota1_press_start = 0;
static unsigned long ota2_press_start = 0;
static unsigned long ota3_press_start = 0;
static bool ota1_long_pressed = false;
static bool ota2_long_pressed = false;
static bool ota3_long_pressed = false;

// OTA進度條相關變數
static lv_obj_t * ota_bar = NULL;
static bool ota_updating = false;

// 蒸氣畫面溫度顯示label
static lv_obj_t * ui_Lab_Steam_Temp = NULL;
static int ota_progress_value = 0;
static bool ota_is_flashing = false;
static unsigned long last_progress_update = 0;
static const char* ota_server_url = "https://www.fusiontech.com.tw/OTA/Teaffic/";

// 函數聲明
static void set_temp_mode(temp_mode_t mode);
static void check_temp_timer_cb(lv_timer_t *timer);

// 統一控制函數實現
static void set_heater(bool state) {
    // 主加熱器使用正常邏輯（HIGH=開啟，LOW=關閉）
    digitalWrite(HEATER, state ? HIGH : LOW);
    Serial.printf("主加熱器: %s (GPIO %d) - 正常模式\n", state ? "ON" : "OFF", HEATER);
    heater_on = state;
}

static void set_pump(bool state) {
    // 水泵使用正常邏輯（HIGH=開啟，LOW=關閉）
    digitalWrite(Pump, state ? HIGH : LOW);
    pump_on = state;
    Serial.printf("水泵: %s (GPIO %d) - 正常模式\n", state ? "ON" : "OFF", Pump);
}

static void set_target_temperature(int temp) {
    target_temp = temp;
    temp_ready = false;
    Serial.printf("設定目標溫度: %d°C\n", temp);
}

// 統一溫度管理任務
static void unified_temp_control_task(void *pvParameters) {
    while (true) {
        int current_temp = last_avg_temp;

        // 深度休眠模式：完全關閉加熱器
        if (current_temp_mode == TEMP_MODE_DEEP_SLEEP) {
            if (heater_on) {
                set_heater(false);
                Serial.println("深度休眠模式：加熱器完全關閉");
            }
            vTaskDelay(pdMS_TO_TICKS(1000));  // 1秒檢查一次
            continue;
        }

        // 統一溫度控制邏輯：目標溫度-1開啟，目標溫度+5關閉
        if (heater_on) {
            // 加熱器開啟時，達到目標溫度+5就關閉
            if (current_temp >= target_temp + 5) {
                set_heater(false);
                Serial.printf("平均溫度: %d°C (ADC查找表) : 加熱器 (OFF) - 達到目標+5°C (%d°C)\n",
                             current_temp, target_temp + 5);
            }
        } else {
            // 加熱器關閉時，低於目標溫度-1就開啟
            if (current_temp <= target_temp - 1) {
                set_heater(true);
                Serial.printf("平均溫度: %d°C (ADC查找表) : 加熱器 (ON) - 低於目標-1°C (%d°C)\n",
                             current_temp, target_temp - 1);
            }
        }

        // 檢查是否達到目標溫度
        if (!temp_ready && current_temp >= target_temp) {
            temp_ready = true;
            const char* heater_status = heater_on ? "ON" : "OFF";
            Serial.printf("平均溫度: %d°C (ADC查找表) : 加熱器 (%s) - 達到目標溫度 %d°C\n",
                         current_temp, heater_status, target_temp);
        }

        vTaskDelay(pdMS_TO_TICKS(200));  // 200ms檢查一次
    }
}

// 分區診斷函數
static void print_partitions(const char* tag) {
    const esp_partition_t* boot = esp_ota_get_boot_partition();
    const esp_partition_t* run  = esp_ota_get_running_partition();
    const esp_partition_t* next = esp_ota_get_next_update_partition(NULL);

    Serial.printf("[%s] boot : %s @0x%08lx size=%lu\n", tag, boot->label, boot->address, (unsigned long)boot->size);
    Serial.printf("[%s] run  : %s @0x%08lx size=%lu\n", tag, run->label,  run->address,  (unsigned long)run->size);
    if (next) {
        Serial.printf("[%s] next : %s @0x%08lx size=%lu\n", tag, next->label, next->address, (unsigned long)next->size);
    } else {
        Serial.printf("[%s] next : (NULL) -- 沒有 OTA 分割區!\n", tag);
    }

    // 顯示編譯時間戳（我們的版本信息）
    Serial.printf("[%s] running app build=%s\n", tag, BUILD_TIMESTAMP);
}



// 1. 定義語言型別與全域變數
typedef enum { LANG_EN = 0, LANG_ZH = 1 } language_t;
static language_t current_language;

// 2. EEPROM 儲存位址
#define EEPROM_LANG_ADDR 0

static void save_language() {
  EEPROM.write(EEPROM_LANG_ADDR, (uint8_t)current_language);
  EEPROM.commit();
}

// 3. 載入／儲存語言
static void load_language() {
  uint8_t v = EEPROM.read(EEPROM_LANG_ADDR);
  if (v <= LANG_ZH) {
    // EEPROM 有有效值 (0=EN, 1=ZH) 就照存入的取
    current_language = (language_t)v;
  } else {
    // 首次啟動或 EEPROM 值異常，預設為中文
    current_language = LANG_ZH;
    // 同步寫回 EEPROM，讓下次也還是中文
    save_language();
  }
}

// 4. 更新所有 Label、Button 的文字
static void update_language() {
  if (current_language == LANG_EN) {
    // 範例，請對照 ui.h 中你的物件名稱自行補足
    lv_label_set_text(ui_Label2,    "Menu");
    lv_label_set_text(ui_Lab_Tea,    "Tea");
    lv_label_set_text(ui_Lab_Tea1,    "Tea");
    lv_label_set_text(ui_Lab_Tea2,    "Tea");
    lv_label_set_text(ui_Lab_Tea3,    "Tea");
    lv_label_set_text(ui_Lab_Coffee,    "Coffee");
    lv_label_set_text(ui_LAB_Coffee1,    "Coffee");
    lv_label_set_text(ui_LAB_Coffee2,    "Coffee");
    lv_label_set_text(ui_LAB_Coffee3,    "Coffee");
    lv_label_set_text(ui_Lab_Setting,    "Setting");
    lv_label_set_text(ui_Lab_Water,    "Hot Water");
    lv_label_set_text(ui_Lab_Steam,    "Steam");
    lv_label_set_text(ui_Lab_Clean,    "Clean");
    lv_label_set_text(ui_LAB_BACK_MENU,    "<Menu");
    lv_label_set_text(ui_LAB_BACK_MENU1,    "<Menu");
    lv_label_set_text(ui_LAB_BACK_MENU2,    "<Menu");
    lv_label_set_text(ui_LAB_BACK_MENU3,    "<Menu");
    lv_label_set_text(ui_LAB_BACK_MENU4,    "<Menu");
    lv_label_set_text(ui_Lab_Cancel1,    "Cancel");
    lv_label_set_text(ui_Lab_Cancel2,    "Cancel");
    lv_label_set_text(ui_Lab_Cancel3,    "Cancel");
    lv_label_set_text(ui_Lab_Cancel4,    "Cancel");
    lv_label_set_text(ui_Lab_Cancel5,    "Cancel");
    lv_label_set_text(ui_Lab_Stop,    "STOP");
        lv_label_set_text(ui_Lab_Stop2,    "STOP");
    lv_label_set_text(ui_Lab_Small_Tea,    "Small");
    lv_label_set_text(ui_Lab_Small_Tea1,    "Small");
    lv_label_set_text(ui_LAB_Big_Tea,    "Big");
    lv_label_set_text(ui_LAB_Big_Tea1,    "Big");
    lv_label_set_text(ui_Lab_Brewing,    "Brewing...");
    lv_label_set_text(ui_Lab_Brewing1,    "Brewing...");
    lv_label_set_text(ui_Lab_Brewing2,    "Brewing...");
    lv_label_set_text(ui_Lab_Brewing3,    "Brewing...");
    lv_label_set_text(ui_Lab_Wait1,    "wait moment");
    lv_label_set_text(ui_Lab_Wait2,    "wait moment");
    lv_label_set_text(ui_Lab_Wait3,    "wait moment");
    lv_label_set_text(ui_Lab_Wait4,    "wait moment");
    lv_label_set_text(ui_Lab_Wait5,    "wait moment");
    lv_label_set_text(ui_Lab_Wait6,    "wait moment");
    lv_label_set_text(ui_Lab_Small_Coffee,    "Espresso");
    lv_label_set_text(ui_Lab_Small_Coffee1,    "Espresso");
    lv_label_set_text(ui_Lab_Big_Coffee,    "Americano");
    lv_label_set_text(ui_Lab_Big_Coffee1,    "Americano");
    lv_label_set_text(ui_Lab_Langauge,    "Langauge");
    lv_label_set_text(ui_Lab_Langauge1,    "Langauge");
    lv_label_set_text(ui_Lab_Service,    "Service");
    lv_label_set_text(ui_Lab_Service1,    "Service");
    lv_label_set_text(ui_Lab_Service_Tel,    "Service Tel:");
    lv_label_set_text(ui_Lab_Water,    "Water");
    lv_label_set_text(ui_Lab_Water1,    "Water");
    lv_label_set_text(ui_Lab_Water2,    "Water");
    lv_label_set_text(ui_Lab_Water_ing,    "Dispensing");
    lv_label_set_text(ui_Lab_Steam,    "Steam");
    lv_label_set_text(ui_Lab_Steam1,    "Steam");
    lv_label_set_text(ui_Label40,    "，Caution: Hot");
    lv_label_set_text(ui_Lab_Cleaning,    "Cleaning...");
    lv_label_set_text(ui_Lab_Clean,    "Clean");
    lv_label_set_text(ui_Lab_Clean1,    "Clean");
    lv_label_set_text(ui_Lab_DeCaing,    "Descaling...");
    lv_label_set_text(ui_Lab_DeCa,    "Descale");
    lv_label_set_text(ui_Lab_DeCa1,    "Descale");
    lv_label_set_text(ui_Lab_Setting,    "Setting");
    lv_label_set_text(ui_Lab_Setting1,    "Setting");

    
    
    // … (針對所有 ui_Lab_xxx 與 ui_Labelxx 做相同處理)
  } else {
    lv_label_set_text(ui_Label2,    "主選單");
    lv_label_set_text(ui_Lab_Tea,    "沖茶");
    lv_label_set_text(ui_Lab_Tea1,    "沖茶");
    lv_label_set_text(ui_Lab_Tea2,    "沖茶");
    lv_label_set_text(ui_Lab_Tea3,    "沖茶");
    lv_label_set_text(ui_Lab_Coffee,    "咖啡");
    lv_label_set_text(ui_LAB_Coffee1,    "咖啡");
    lv_label_set_text(ui_LAB_Coffee2,    "咖啡");
    lv_label_set_text(ui_LAB_Coffee3,    "咖啡");
    lv_label_set_text(ui_Lab_Setting,    "設定");
    lv_label_set_text(ui_Lab_Water,    "熱水");
    lv_label_set_text(ui_Lab_Steam,    "奶泡蒸氣");
    lv_label_set_text(ui_Lab_Clean,    "清潔");
    lv_label_set_text(ui_LAB_BACK_MENU,    "<主選單");
    lv_label_set_text(ui_LAB_BACK_MENU1,    "<主選單");
    lv_label_set_text(ui_LAB_BACK_MENU2,    "<主選單");
    lv_label_set_text(ui_LAB_BACK_MENU3,    "<主選單");
    lv_label_set_text(ui_LAB_BACK_MENU4,    "<主選單");
    lv_label_set_text(ui_Lab_Cancel1,    "取消");
    lv_label_set_text(ui_Lab_Cancel2,    "取消");
    lv_label_set_text(ui_Lab_Cancel3,    "取消");
    lv_label_set_text(ui_Lab_Cancel4,    "取消");
    lv_label_set_text(ui_Lab_Cancel5,    "取消");
    lv_label_set_text(ui_Lab_Stop,    "停止");
    lv_label_set_text(ui_Lab_Stop2,    "停止");
    lv_label_set_text(ui_Lab_Small_Tea,    "小杯");
    lv_label_set_text(ui_Lab_Small_Tea1,    "小杯");
    lv_label_set_text(ui_LAB_Big_Tea,    "大杯");
    lv_label_set_text(ui_LAB_Big_Tea1,    "大杯");
    lv_label_set_text(ui_Lab_Brewing,    "沖泡中...");
    lv_label_set_text(ui_Lab_Brewing1,    "沖泡中...");
    lv_label_set_text(ui_Lab_Brewing2,    "沖泡中...");
    lv_label_set_text(ui_Lab_Brewing3,    "沖泡中...");
    lv_label_set_text(ui_Lab_Wait1,    "請稍候");
    lv_label_set_text(ui_Lab_Wait2,    "請稍候");
    lv_label_set_text(ui_Lab_Wait3,    "請稍候");
    lv_label_set_text(ui_Lab_Wait4,    "請稍候");
    lv_label_set_text(ui_Lab_Wait5,    "請稍候");
    lv_label_set_text(ui_Lab_Wait6,    "請稍候");
    lv_label_set_text(ui_Lab_Small_Coffee,    "濃縮");
    lv_label_set_text(ui_Lab_Small_Coffee1,    "濃縮");
    lv_label_set_text(ui_Lab_Big_Coffee,    "美式");
    lv_label_set_text(ui_Lab_Big_Coffee1,    "美式");
    lv_label_set_text(ui_Lab_Langauge,    "語言");
    lv_label_set_text(ui_Lab_Langauge1,    "語言");
    lv_label_set_text(ui_Lab_Service,    "聯絡客服");
    lv_label_set_text(ui_Lab_Service1,    "聯絡客服");
    lv_label_set_text(ui_Lab_Service_Tel,    "客服電話 :");
    lv_label_set_text(ui_Lab_Water,    "熱水");
    lv_label_set_text(ui_Lab_Water1,    "熱水");
    lv_label_set_text(ui_Lab_Water2,    "熱水");
    lv_label_set_text(ui_Lab_Water_ing,    "出水中...");
    lv_label_set_text(ui_Lab_Steam,    "蒸氣");
    lv_label_set_text(ui_Lab_Steam1,    "蒸氣");
    lv_label_set_text(ui_Label40,    "，小心燙手");
    lv_label_set_text(ui_Lab_Cleaning,    "清潔中...");
    lv_label_set_text(ui_Lab_Clean,    "清潔");
    lv_label_set_text(ui_Lab_Clean1,    "清潔");
    lv_label_set_text(ui_Lab_DeCaing,    "除鈣中...");
    lv_label_set_text(ui_Lab_DeCa,    "除鈣");
    lv_label_set_text(ui_Lab_DeCa1,    "除鈣");
    lv_label_set_text(ui_Lab_Setting,    "設定");
    lv_label_set_text(ui_Lab_Setting1,    "設定");
        

    // …
  }
}

// 5. 兩顆切換語言按鈕的事件處理函式
static void evt_set_chinese(lv_event_t * e) {
  if (lv_event_get_code(e) == LV_EVENT_CLICKED) {
    current_language = LANG_ZH;
    save_language();
    update_language();
    // 可選：切回主畫面
    //_ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_FADE_ON, 200, 0, &ui_SC_MAIN_MENU_screen_init);
  }
}
static void evt_set_english(lv_event_t * e) {
  if (lv_event_get_code(e) == LV_EVENT_CLICKED) {
    current_language = LANG_EN;
    save_language();
    update_language();
    //_ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_FADE_ON, 200, 0, &ui_SC_MAIN_MENU_screen_init);
  }
}


//#define LV_DISP_BUF_MAX_NUM   2  /* 註解掉這行 */
SemaphoreHandle_t lvglMutex; // LVGL 互斥鎖

TaskHandle_t GUI_TaskHandle = NULL;

void GUI_Task(void *parameter) {
  int counter = 0; // 添加計數器
  while (1) {
    lv_timer_handler();
    vTaskDelay(pdMS_TO_TICKS(20));  // 延時20ms

    if (++counter >= 200) { // 每100次迭代
      //UBaseType_t uxHighWaterMark = uxTaskGetStackHighWaterMark(NULL);
      //Serial.printf("LVGL stack: %d\n", uxHighWaterMark);

      //size_t free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
      //Serial.printf("Free internal SRAM: %u bytes\n", free_sram);

      //uint32_t free_size = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
      //uint32_t total_size = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
      //Serial.printf("PSRAM Free Size: %u bytes\n", free_size);
      //Serial.printf("PSRAM Total Size: %u bytes\n", total_size);
      // 獲取當前 CPU 頻率
      //uint32_t currentFrequency = getCpuFrequencyMhz();
      //Serial.println("當前 CPU 頻率: " + String(currentFrequency) +"MHz");
      counter = 0; // 重置計數器
    }
  }
}

// 設定 FT6336U 各腳位，並初始化FT6336U
#define I2C_SDA 10
#define I2C_SCL 9
#define RST_N_PIN 18
#define INT_N_PIN 8
FT6336U ft6336u(I2C_SDA, I2C_SCL, RST_N_PIN, INT_N_PIN); 

///  宣告全域變數
volatile bool scanCompleted = false;
volatile bool shouldInterrupt = false;
unsigned long lastTouchTime = 0;  // 最後一次觸摸的時間
bool isBacklightDimmed = false;   // 背光是否已經降低
unsigned long touchStartTime = 0; // 觸摸開始的時間


#define GFX_BL 4  // default backlight pin, you may replace DF_GFX_BL to actual backlight pin

Arduino_DataBus *bus = new Arduino_ESP32SPI(15 /* DC */, 7 /* CS */, 6 /* SCK */, 5 /* MOSI */, GFX_NOT_DEFINED /* MISO */, HSPI /* spi_num */);

/* More display class: https://github.com/moononournation/Arduino_GFX/wiki/Display-Class */
Arduino_GFX *gfx = new Arduino_ST7796(bus, 16 /* RST */, 1 /* rotation */, true /* IPS */);

/* Change to your screen resolution */
static uint32_t screenWidth = 320;
static uint32_t screenHeight = 480;
static lv_disp_draw_buf_t draw_buf;
static lv_color_t *disp_draw_buf1;  // 第一個緩衝區
//static lv_color_t *disp_draw_buf2;  // 刪除或註解掉第二個緩衝區

static lv_disp_drv_t disp_drv;

/* Display flushing */
void my_disp_flush(lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p) {
  uint32_t w = (area->x2 - area->x1 + 1);
  uint32_t h = (area->y2 - area->y1 + 1);

#if (LV_COLOR_16_SWAP != 0)
  gfx->draw16bitBeRGBBitmap(area->x1, area->y1, (uint16_t *)&color_p->full, w, h);
#else
  gfx->draw16bitRGBBitmap(area->x1, area->y1, (uint16_t *)&color_p->full, w, h);
#endif

  lv_disp_flush_ready(disp);
}

///////////////////////Touch //////////////////////////////////////////////////

void my_touchpad_read(lv_indev_drv_t *indev_driver, lv_indev_data_t *data) {
    uint8_t touchStatus = ft6336u.read_td_status();

    if (touchStatus > 0) {
        lastTouchTime = millis();  // 更新最後一次觸摸的時間
        if (isBacklightDimmed) {
            // 如果背光已經降低，則恢復背光亮度和溫度控制
            analogWrite(GFX_BL, 255);
            isBacklightDimmed = false;

            // 重新啟動溫度檢測定時器（如果已被刪除）
            if (temp_timer == NULL) {
                temp_timer = lv_timer_create(check_temp_timer_cb, 200, NULL);
                Serial.println("觸摸恢復：重新啟動溫度檢測定時器");
            }
        }

        data->state = LV_INDEV_STATE_PR;
        data->point.x = ft6336u.read_touch1_y();
        data->point.y = 320- ft6336u.read_touch1_x();

        //Serial.print("X: ");
        //Serial.println(data->point.x);  // 列印 X 座標
        //Serial.print("Y: ");
        //Serial.println(data->point.y);  // 列印 Y 座標
    } else {
        data->state = LV_INDEV_STATE_REL;
    }
}

///////////讀取温度//////////////////////////////////
static void adc_sample_timer_cb(lv_timer_t *timer) {
    // 由 Arduino API 讀取原始 ADC 值（0~4095）
    uint32_t raw = analogRead(ADC_PIN);

    // 32點移動平均濾波
    static int32_t avg = 0;
    static bool initialized = false;

    if (!initialized) {
        // 第一次初始化，直接設定為當前值的32倍
        avg = raw * 32;
        initialized = true;
    } else {
        // 移動平均：avg = avg - avg/32 + raw
        avg = avg - avg/32 + raw;
    }

    // 計算平均值並反查對應溫度
    uint32_t filtered_raw = avg / 32;
    int16_t temp_c = lookup_temp_from_adc(filtered_raw);
    last_avg_temp = temp_c;  // 更新全域溫度

    // 5秒一次印出詳細調試資訊
    static uint32_t last_debug_time = 0;
    uint32_t now = millis();
    if (now - last_debug_time > 5000) {
        // 所有模式都使用主加熱器狀態
        const char* heater_status = heater_on ? "ON" : "OFF";

        Serial.printf("ADC原始: %u → 濾波後: %u → 平均溫度: %d°C (ADC查找表) : 加熱器 (%s)\n",
                     raw, filtered_raw, temp_c, heater_status);
        last_debug_time = now;
    }
}

// 蒸氣畫面溫度監控和按鈕控制
static void steam_temp_update_cb(lv_timer_t *timer) {
    // 檢查是否在蒸氣畫面
    if (lv_scr_act() == ui_SC_Steam) {
        // 更新左下角溫度顯示
        if (ui_Lab_Steam_Temp != NULL) {
            lv_label_set_text_fmt(ui_Lab_Steam_Temp, "%d", last_avg_temp);
            // 每10次更新輸出一次調試信息（避免過多輸出）
            static int debug_counter = 0;
            if (++debug_counter >= 10) {
                Serial.printf("蒸氣溫度label更新: %d°C\n", last_avg_temp);
                debug_counter = 0;
            }
        } else {
            Serial.println("警告：蒸氣溫度label為NULL");
        }

        // 溫度控制現在由統一溫度管理任務處理，這裡只處理按鈕啟用邏輯
        // 如果溫度達到115°C且按鈕還是禁用狀態，則啟用按鈕
        if (ui_BTN_Steam_COM != NULL && last_avg_temp >= 115 && lv_obj_has_state(ui_BTN_Steam_COM, LV_STATE_DISABLED)) {
            // 啟用按鈕
            lv_obj_clear_state(ui_BTN_Steam_COM, LV_STATE_DISABLED);

            // 更新按鈕文字（不變更Lab_Steam_des內容）
            lv_label_set_text(
                ui_Lab_Steam_COM,
                current_language == LANG_ZH
                    ? "下一步"
                    : "Next"
            );

            Serial.printf("平均溫度: %d°C (ADC查找表) : 主加熱器 (ON) - 達到115°C，啟用按鈕\n", last_avg_temp);
        }
    }
}

// 清理蒸氣畫面溫度定時器並恢復待機溫度
static void cleanup_steam_temp_timer() {
    if (steam_temp_timer != NULL) {
        lv_timer_del(steam_temp_timer);
        steam_temp_timer = NULL;
    }

    // 強制關閉pump
    set_pump(false);

    // 恢復到待機溫度模式（統一溫度管理任務會自動處理加熱器切換）
    set_temp_mode(TEMP_MODE_STANDBY);

    Serial.println("退出蒸氣模式：強制關閉pump，恢復待機溫度模式");
}

// 設定溫度控制模式
static void set_temp_mode(temp_mode_t mode) {
    current_temp_mode = mode;
    deep_sleep_active = false;

    switch (mode) {
        case TEMP_MODE_STANDBY:
            set_target_temperature(80);
            Serial.println("溫度模式：待機 (目標80°C, 控制範圍79~85°C)");
            break;

        case TEMP_MODE_COFFEE:
            set_target_temperature(85);
            Serial.println("溫度模式：咖啡 (目標85°C, 控制範圍84~90°C)");
            break;

        case TEMP_MODE_TEA:
            set_target_temperature(95);
            Serial.println("溫度模式：茶品 (目標95°C, 控制範圍94~100°C)");
            break;

        case TEMP_MODE_WATER:
            set_target_temperature(85);
            Serial.println("溫度模式：熱水 (目標85°C, 控制範圍84~90°C)");
            break;

        case TEMP_MODE_STEAM:
            set_target_temperature(115);
            Serial.println("溫度模式：蒸氣 (目標115°C, 控制範圍114~120°C)");
            break;

        case TEMP_MODE_SLEEP:
            set_target_temperature(60);
            sleep_mode_start_time = millis();  // 記錄進入休眠模式的時間
            Serial.println("溫度模式：休眠 (目標60°C, 控制範圍59~65°C)");
            break;

        case TEMP_MODE_DEEP_SLEEP:
            target_temp = 0;  // 不需要目標溫度
            deep_sleep_active = true;
            Serial.println("溫度模式：深熱休眠 (加熱器完全關閉)");
            break;
    }
}

// 舊的溫度控制邏輯已被統一溫度管理任務取代
// 此函數保留為空，避免其他地方的調用出錯
static void temp_control_logic() {
    // 溫度控制現在由 unified_temp_control_task 統一處理
}

// 檢查是否應該進入休眠模式或深度休眠模式
static void check_sleep_mode() {
    unsigned long current_time = millis();

    // 檢查背光狀態，如果背光降低且不在休眠相關模式，則切換到休眠模式
    if (isBacklightDimmed && current_temp_mode != TEMP_MODE_SLEEP && current_temp_mode != TEMP_MODE_DEEP_SLEEP) {
        set_temp_mode(TEMP_MODE_SLEEP);
    }
    // 如果背光恢復且在休眠相關模式，則切換回待機模式
    else if (!isBacklightDimmed && (current_temp_mode == TEMP_MODE_SLEEP || current_temp_mode == TEMP_MODE_DEEP_SLEEP)) {
        // 確保溫度檢測定時器正在運行（從休眠恢復時）
        if (temp_timer == NULL) {
            temp_timer = lv_timer_create(check_temp_timer_cb, 200, NULL);
            Serial.println("從休眠恢復：重新啟動溫度檢測定時器");
        }
        set_temp_mode(TEMP_MODE_STANDBY);
    }

    // 檢查是否應該進入深度休眠模式（在休眠模式下30分鐘無操作）
    if (current_temp_mode == TEMP_MODE_SLEEP && !deep_sleep_active) {
        if (current_time - sleep_mode_start_time >= 30 * 60 * 1000) {  // 30分鐘 = 30 * 60 * 1000 毫秒
            set_temp_mode(TEMP_MODE_DEEP_SLEEP);
            Serial.println("進入深度休眠模式：30分鐘無操作，完全關閉加熱器");
        }
    }
}

// NVS 時間戳操作函數
static void load_firmware_timestamp_from_nvs() {
    Serial.println("開始從NVS載入韌體時間戳...");

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("firmware", NVS_READONLY, &nvs_handle);

    if (err == ESP_OK) {
        Serial.println("NVS開啟成功");
        size_t required_size = sizeof(firmware_timestamp);
        err = nvs_get_str(nvs_handle, "timestamp", firmware_timestamp, &required_size);

        if (err == ESP_OK) {
            Serial.printf("✓ 從NVS成功讀取時間戳: %s\n", firmware_timestamp);
        } else {
            // NVS中沒有時間戳，使用預設值
            strcpy(firmware_timestamp, "20250718000000");
            Serial.printf("⚠ NVS中無時間戳 (%s)，使用預設值: %s\n", esp_err_to_name(err), firmware_timestamp);
        }
        nvs_close(nvs_handle);
    } else {
        // 無法開啟NVS，使用預設值
        strcpy(firmware_timestamp, "20250718000000");
        Serial.printf("✗ 無法開啟NVS (%s)，使用預設值: %s\n", esp_err_to_name(err), firmware_timestamp);
    }
}

static void save_firmware_timestamp_to_nvs(const char* timestamp) {
    Serial.printf("準備保存時間戳到NVS: %s\n", timestamp);

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("firmware", NVS_READWRITE, &nvs_handle);

    if (err == ESP_OK) {
        Serial.println("NVS開啟成功");
        err = nvs_set_str(nvs_handle, "timestamp", timestamp);
        if (err == ESP_OK) {
            Serial.println("NVS字串寫入成功");
            err = nvs_commit(nvs_handle);
            if (err == ESP_OK) {
                Serial.printf("✓ 時間戳已成功保存到NVS: %s\n", timestamp);
            } else {
                Serial.printf("✗ NVS提交失敗: %s\n", esp_err_to_name(err));
            }
        } else {
            Serial.printf("✗ NVS寫入失敗: %s\n", esp_err_to_name(err));
        }
        nvs_close(nvs_handle);
    } else {
        Serial.printf("✗ 無法開啟NVS進行寫入: %s\n", esp_err_to_name(err));
    }
}

// 從檔名中提取時間戳
static String extract_timestamp_from_filename(const String& filename) {
    // 檔名格式: Teaffic_20250718123456.bin
    Serial.printf("嘗試從檔名提取時間戳: %s\n", filename.c_str());

    int start = filename.indexOf('_');
    int end = filename.indexOf('.');

    Serial.printf("找到 '_' 位置: %d, '.' 位置: %d\n", start, end);

    if (start != -1 && end != -1 && end > start) {
        String timestamp = filename.substring(start + 1, end);
        Serial.printf("提取的時間戳: '%s', 長度: %d\n", timestamp.c_str(), timestamp.length());

        if (timestamp.length() == 14) {  // yyyymmddhhmmss
            Serial.printf("時間戳格式正確: %s\n", timestamp.c_str());
            return timestamp;
        } else {
            Serial.printf("時間戳長度不正確，預期14位，實際%d位\n", timestamp.length());
        }
    } else {
        Serial.println("檔名格式不符合預期");
    }

    return "";  // 無法提取時間戳
}



// 全域動畫物件
static lv_anim_t pwr_arc_anim;

// 動畫執行 callback：用於更新 arc 的值
static void arc_anim_cb(void * var, int32_t v) { lv_arc_set_value((lv_obj_t*)var, v); }

// 動畫結束 callback：長按滿 2 秒，進行加熱
static void arc_anim_ready_cb(lv_anim_t * a) {
    // 隱藏圓弧與按鈕
    lv_obj_add_flag(ui_PWR_ARC, LV_OBJ_FLAG_HIDDEN);
    lv_obj_add_flag(ui_BTN_PWR, LV_OBJ_FLAG_HIDDEN);

    // 顯示等待加熱提示與進度條，並歸零
    lv_obj_clear_flag(ui_Lab_wait_heater, LV_OBJ_FLAG_HIDDEN);
    lv_obj_clear_flag(ui_Bar_Wait_Heater, LV_OBJ_FLAG_HIDDEN);
    lv_bar_set_value(ui_Bar_Wait_Heater, 0, LV_ANIM_OFF);

    // 確保溫度檢測定時器正在運行（從待機/深度休眠恢復時可能被刪除）
    if (temp_timer == NULL) {
        temp_timer = lv_timer_create(check_temp_timer_cb, 200, NULL);
        Serial.println("PWR長按：重新啟動溫度檢測定時器");
    }

    // 設定為待機溫度模式並啟動溫控
    set_temp_mode(TEMP_MODE_STANDBY);
    wait_for_temp       = true;
    screen_transitioned = false;    // 重置轉場旗標

    // 確認GPIO HEATER為ON，進入待機模式
    set_heater(true);
    Serial.printf("PWR長按完成，確認GPIO HEATER為ON，進入待機模式，當前溫度: %d°C，目標: %d°C\n", last_avg_temp, target_temp);
}

static void check_temp_timer_cb(lv_timer_t *timer) {
    int avg = last_avg_temp;

    // 如果在等待溫度模式，更新進度條
    if (wait_for_temp) {
        lv_bar_set_value(ui_Bar_Wait_Heater, avg, LV_ANIM_OFF);

        // 檢查是否達到待機模式目標溫度（80°C），才可以進入主畫面
        if (avg >= 80 && !screen_transitioned) {
            screen_transitioned = true;
            const char* heater_status = heater_on ? "ON" : "OFF";
            Serial.printf("平均溫度: %d°C (ADC查找表) : 加熱器 (%s) - 達到待機目標80°C，切換到主畫面\n", avg, heater_status);
            _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
        }
    }

    // 檢查休眠模式
    check_sleep_mode();

    // 執行溫度控制邏輯
    temp_control_logic();
}

// Power Key btn 事件處理
static void evt_btn_pwr(lv_event_t * e) {
    lv_event_code_t code = lv_event_get_code(e);
    if(code == LV_EVENT_PRESSED) {
        // 按下時啟動 0→100 動畫，持續 500ms
        lv_anim_init(&pwr_arc_anim);
        lv_anim_set_var(&pwr_arc_anim, ui_PWR_ARC);
        lv_anim_set_values(&pwr_arc_anim, 0, 100);
        lv_anim_set_time(&pwr_arc_anim, 800);
        lv_anim_set_exec_cb(&pwr_arc_anim, arc_anim_cb);
        lv_anim_set_ready_cb(&pwr_arc_anim, arc_anim_ready_cb);
        lv_anim_start(&pwr_arc_anim);

    } else if(code == LV_EVENT_RELEASED || code == LV_EVENT_PRESS_LOST) {
        // 放開（或失去按壓）時，刪除動畫並歸零
        lv_anim_del(ui_PWR_ARC, arc_anim_cb);
        lv_arc_set_value(ui_PWR_ARC, 0);
    }
}



// 共用的 Cancel 按鈕事件處理函式
static void btn_cancel_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;
  cancel_flag = true;
  // 如果從蒸氣畫面離開，清理溫度定時器
  if (lv_scr_act() == ui_SC_Steam) {
      cleanup_steam_temp_timer();
  }
  _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init); // 臨時用於返回
}
void bind_cancel_buttons() {

  lv_obj_add_event_cb(ui_BTN_Cancel1, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel2, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel3, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel4, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel5, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel6, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  //lv_obj_add_event_cb(ui_BTN_Cancel7, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_BTN_Cancel8, btn_cancel_handler, LV_EVENT_CLICKED, NULL);
}

// 小杯茶處理程序
static void SmallTeaPumpTask(void * pvParameters) {
    const int on_step_ms        = 100;                       // 更新間隔
    const int off_step_ms       = 100;
    const int on_steps_per_cycle  = 4000 / on_step_ms;       // 4 秒 ÷ 100 ms = 40 步
    const int off_steps_per_cycle = 3000 / off_step_ms;      // 3 秒 ÷ 100 ms = 30 步
    const int total_on_steps      = 40000 / on_step_ms;      // 總共 40 秒 ÷ 100 ms = 400 步
    int       on_steps_completed = 0;

    cancel_flag = false;

    // 先清空舊畫面，設定初始值
    lv_bar_set_value(ui_BAR_Small_tea, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Small_Tea_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));  // 等待界面切換完成

    // 設定茶品溫度模式
    set_temp_mode(TEMP_MODE_TEA);

    // 檢查當前溫度，如果低於最高標準(110°C)，需要等待溫度上升
    if (last_avg_temp < 110) {
        // 設定等待加熱的顯示
        lv_label_set_text(ui_LAB__Small_Tea_progress, "");  // 進度顯示為空白

        // 根據語言設定加熱中顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing, "加熱中");
        } else {
            lv_label_set_text(ui_Lab_Brewing, "Wait heating");
        }

        // 等待溫度達標
        while (!temp_ready && !cancel_flag) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        if (cancel_flag) goto CANCEL;

        // 溫度達標後恢復正常顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing, "沖泡中...");
        } else {
            lv_label_set_text(ui_Lab_Brewing, "Brewing...");
        }
    } else {
        // 溫度已經高於目標，直接開始
        Serial.printf("溫度已達標，直接開始沖泡 - 當前溫度: %d°C\n", last_avg_temp);

        // 設定正常的沖泡顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing, "沖泡中...");
        } else {
            lv_label_set_text(ui_Lab_Brewing, "Brewing...");
        }
    }

    for (int cycle = 0; cycle < 10; cycle++) {
        // HI 段：持續 4 秒
        digitalWrite(Pump, HIGH);
        for (int i = 0; i < on_steps_per_cycle; i++) {
            if (cancel_flag) goto CANCEL;
            on_steps_completed++;
            int value = (on_steps_completed * 100) / total_on_steps;
            lv_bar_set_value(ui_BAR_Small_tea, value, LV_ANIM_OFF);
            lv_label_set_text_fmt(ui_LAB__Small_Tea_progress, "%d%%", value);
            vTaskDelay(pdMS_TO_TICKS(on_step_ms));
        }
        // LOW 段：持續 3 秒
        digitalWrite(Pump, LOW);
        for (int i = 0; i < off_steps_per_cycle; i++) {
            if (cancel_flag) goto CANCEL;
            vTaskDelay(pdMS_TO_TICKS(off_step_ms));
        }
    }

    // 正常完成：關閉水泵、恢復待機溫度、單行畫面切換、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU,
                      LV_SCR_LOAD_ANIM_NONE,
                      0, 0,
                      &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
    return;

CANCEL:
    // 取消時：關閉水泵、恢復待機溫度、單行畫面切換、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU,
                      LV_SCR_LOAD_ANIM_NONE,
                      0, 0,
                      &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
}
static void btn_small_tea_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Small Tea」畫面
  _ui_screen_change(&ui_SC_Small_Tea, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Small_Tea_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(SmallTeaPumpTask,"SmallTeaPumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 SmallTeaPumpTask!");
  }
}

// 大杯茶處理程序
static void BigTeaPumpTask(void * pvParameters) {
    const int on_step_ms         = 100;                          // 更新間隔
    const int off_step_ms        = 100;
    const int on_steps_per_cycle  = 4000 / on_step_ms;           // 4 秒 ÷ 100 ms = 40 步
    const int off_steps_per_cycle = 3000 / off_step_ms;          // 3 秒 ÷ 100 ms = 30 步
    const int total_on_steps      = 80000 / on_step_ms;          // 總共 80 秒 ÷ 100 ms = 800 步
    int       on_steps_completed  = 0;

    cancel_flag = false;

    // 先清空舊畫面，設定初始值
    lv_bar_set_value(ui_BAR_Big_tea, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Big_Tea_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));  // 等待畫面切換完成

    // 設定茶品溫度模式
    set_temp_mode(TEMP_MODE_TEA);

    // 檢查當前溫度，如果低於最高標準(110°C)，需要等待溫度上升
    if (last_avg_temp < 110) {
        // 設定等待加熱的顯示
        lv_label_set_text(ui_LAB__Big_Tea_progress, "");  // 進度顯示為空白

        // 根據語言設定加熱中顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing1, "加熱中");
        } else {
            lv_label_set_text(ui_Lab_Brewing1, "Wait heating");
        }

        // 等待溫度達標
        while (!temp_ready && !cancel_flag) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        if (cancel_flag) goto CANCEL;

        // 溫度達標後恢復正常顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing1, "沖泡中...");
        } else {
            lv_label_set_text(ui_Lab_Brewing1, "Brewing...");
        }
    } else {
        // 溫度已經高於目標，直接開始
        Serial.printf("溫度已達標，直接開始沖泡 - 當前溫度: %d°C\n", last_avg_temp);

        // 設定正常的沖泡顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing1, "沖泡中...");
        } else {
            lv_label_set_text(ui_Lab_Brewing1, "Brewing...");
        }
    }

    // 進度條歸零，Label 顯示 “0%”
    lv_bar_set_value(ui_BAR_Big_tea, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Big_Tea_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));  // 等待畫面切換完成

    for (int cycle = 0; cycle < 20; cycle++) {
        // HI 段：持續 4 秒
        digitalWrite(Pump, HIGH);
        for (int i = 0; i < on_steps_per_cycle; i++) {
            if (cancel_flag) goto CANCEL;
            on_steps_completed++;
            int value = (on_steps_completed * 100) / total_on_steps;
            lv_bar_set_value(ui_BAR_Big_tea, value, LV_ANIM_OFF);
            lv_label_set_text_fmt(ui_LAB__Big_Tea_progress, "%d%%", value);
            vTaskDelay(pdMS_TO_TICKS(on_step_ms));
        }

        // LOW 段：持續 3 秒
        digitalWrite(Pump, LOW);
        for (int i = 0; i < off_steps_per_cycle; i++) {
            if (cancel_flag) goto CANCEL;
            vTaskDelay(pdMS_TO_TICKS(off_step_ms));
        }
    }

    // 正常完成：關閉水泵、恢復待機溫度、單行畫面切換、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
    return;

CANCEL:
    // 取消時：關閉水泵、恢復待機溫度、單行畫面切換、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
}
static void btn_big_tea_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Big Tea」畫面
  _ui_screen_change(&ui_SC_Big_Tea, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Big_Tea_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(BigTeaPumpTask,"BigTeaPumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 BigTeaPumpTask!");
  }
}

// 小杯咖啡處理程序
static void SmallCoffeePumpTask(void * pvParameters) {
    const int total_ms = 8000;           // 總共 8 000 ms
    const int step_ms  = 100;             // 每 100 ms 更新一次
    const int steps    = total_ms / step_ms;

    cancel_flag = false;

    // 先清空舊畫面，設定初始值
    lv_bar_set_value(ui_BAR_Small_Coffee, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Small_Coffee_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));

    // 設定咖啡溫度模式
    set_temp_mode(TEMP_MODE_COFFEE);

    // 咖啡類特殊溫度控制：先加熱到100°C，關閉加熱器，開始沖泡
    if (last_avg_temp < 100) {
        // 設定等待加熱的顯示
        lv_label_set_text(ui_LAB__Small_Coffee_progress, "");  // 進度顯示為空白

        // 根據語言設定加熱中顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing2, "加熱中");
        } else {
            lv_label_set_text(ui_Lab_Brewing2, "Wait heating");
        }

        // 開啟加熱器並等待溫度達到100°C
        digitalWrite(HEATER, HIGH);
        while (last_avg_temp < 100 && !cancel_flag) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        if (cancel_flag) goto CANCEL;

        Serial.printf("咖啡加熱完成，達到100°C，關閉加熱器開始沖泡\n");
    }

    // 關閉加熱器，開始沖泡
    digitalWrite(HEATER, LOW);

    // 設定沖泡顯示
    if (current_language == LANG_ZH) {
        lv_label_set_text(ui_Lab_Brewing2, "沖泡中...");
    } else {
        lv_label_set_text(ui_Lab_Brewing2, "Brewing...");
    }

    // 拉高水泵
    digitalWrite(Pump, HIGH);

    for (int i = 1; i <= steps; i++) {
        // 檢查是否被取消
        if (cancel_flag) {
            goto CANCEL;
        }

        // 咖啡類溫度監控：溫度低於85°C時重新開啟加熱器
        if (last_avg_temp < 85) {
            digitalWrite(HEATER, HIGH);
            Serial.printf("沖泡中溫度過低(%d°C)，重新開啟加熱器\n", last_avg_temp);
        }

        // 計算並更新進度百分比
        int value = (i * 100) / steps;
        lv_bar_set_value(ui_BAR_Small_Coffee, value, LV_ANIM_OFF);
        lv_label_set_text_fmt(ui_LAB__Small_Coffee_progress, "%d%%", value);

        vTaskDelay(pdMS_TO_TICKS(step_ms));
    }

CANCEL:
    // 結束時關閉水泵
    digitalWrite(Pump, LOW);

    // 恢復到待機溫度模式
    set_temp_mode(TEMP_MODE_STANDBY);

    // 正常完成：關閉水泵、切回主畫面、刪除任務
    _ui_screen_change(&ui_SC_MAIN_MENU,
                      LV_SCR_LOAD_ANIM_NONE,
                      0, 0,
                      &ui_SC_MAIN_MENU_screen_init);

    // （可選）在這裡做任何後續清理
    vTaskDelete(NULL);
}
static void btn_small_coffee_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Small Cofee」畫面
  _ui_screen_change(&ui_SC_Small_Coffee, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Small_Coffee_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(SmallCoffeePumpTask,"SmallCoffeePumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 SmallCoffeePumpTask!");
  }
}

// 大杯咖啡處理程序
static void BigCoffeePumpTask(void * pvParameters) {
    const int total_ms = 24000;      // 總共 24 000 ms
    const int step_ms  = 100;        // 每 100 ms 更新一次
    const int steps    = total_ms / step_ms;

    cancel_flag = false;

    // 先清空舊畫面，設定初始值
    lv_bar_set_value(ui_BAR_Big_Coffee, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Big_Coffee_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));  // 等待畫面切換完成

    // 設定咖啡溫度模式
    set_temp_mode(TEMP_MODE_COFFEE);

    // 咖啡類特殊溫度控制：先加熱到100°C，關閉加熱器，開始沖泡
    if (last_avg_temp < 100) {
        // 設定等待加熱的顯示
        lv_label_set_text(ui_LAB__Big_Coffee_progress, "");  // 進度顯示為空白

        // 根據語言設定加熱中顯示
        if (current_language == LANG_ZH) {
            lv_label_set_text(ui_Lab_Brewing3, "加熱中");
        } else {
            lv_label_set_text(ui_Lab_Brewing3, "Wait heating");
        }

        // 開啟加熱器並等待溫度達到100°C
        digitalWrite(HEATER, HIGH);
        while (last_avg_temp < 100 && !cancel_flag) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        if (cancel_flag) goto CANCEL;

        Serial.printf("咖啡加熱完成，達到100°C，關閉加熱器開始沖泡\n");
    }

    // 關閉加熱器，開始沖泡
    digitalWrite(HEATER, LOW);

    // 設定沖泡顯示
    if (current_language == LANG_ZH) {
        lv_label_set_text(ui_Lab_Brewing3, "沖泡中...");
    } else {
        lv_label_set_text(ui_Lab_Brewing3, "Brewing...");
    }

    // 進度條歸零，Label 顯示 “0%”
    lv_bar_set_value(ui_BAR_Big_Coffee, 0, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Big_Coffee_progress, "%d%%", 0);
    vTaskDelay(pdMS_TO_TICKS(100));  // 等待畫面切換完成

    // 持續拉高水泵
    digitalWrite(Pump, HIGH);

    for (int i = 1; i <= steps; i++) {
        // 檢查是否被取消
        if (cancel_flag) {
            goto CANCEL;
        }

        // 咖啡類溫度監控：溫度低於85°C時重新開啟加熱器
        if (last_avg_temp < 85) {
            digitalWrite(HEATER, HIGH);
            Serial.printf("沖泡中溫度過低(%d°C)，重新開啟加熱器\n", last_avg_temp);
        }

        // 計算並更新進度百分比
        int value = (i * 100) / steps;
        lv_bar_set_value(ui_BAR_Big_Coffee, value, LV_ANIM_OFF);
        lv_label_set_text_fmt(ui_LAB__Big_Coffee_progress, "%d%%", value);

        vTaskDelay(pdMS_TO_TICKS(step_ms));
    }

    // 正常完成：關閉水泵、恢復待機溫度、切回主畫面、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU,
                      LV_SCR_LOAD_ANIM_NONE,
                      0, 0,
                      &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
    return;

CANCEL:
    // 取消時：關泵、恢復待機溫度、切回主畫面、刪除任務
    digitalWrite(Pump, LOW);
    set_temp_mode(TEMP_MODE_STANDBY);
    _ui_screen_change(&ui_SC_MAIN_MENU,
                      LV_SCR_LOAD_ANIM_NONE,
                      0, 0,
                      &ui_SC_MAIN_MENU_screen_init);
    vTaskDelete(NULL);
}
static void btn_big_coffee_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Big Cofee」畫面
  _ui_screen_change(&ui_SC_Big_Coffee, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Big_Coffee_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(BigCoffeePumpTask,"BigCoffeePumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 BigCoffeePumpTask!");
  }
}

// 熱水處理程序
static void WaterPumpTask(void * pvParameters) {
  const int totalSegments = 300;    // 30 000 ms / 100 ms
  const TickType_t segmentDelay = pdMS_TO_TICKS(100);

  cancel_flag = false;

  // 設定熱水溫度模式
  set_temp_mode(TEMP_MODE_WATER);

  // 檢查當前溫度，如果低於最低標準(95°C)，需要等待溫度上升
  if (last_avg_temp < 95) {
      // 等待溫度達標
      while (!temp_ready && !cancel_flag) {
          vTaskDelay(pdMS_TO_TICKS(100));
      }

      if (cancel_flag) goto CANCEL_RELEASE;
  } else {
      // 溫度已經高於目標，直接開始
      Serial.printf("溫度已達標，直接開始熱水 - 當前溫度: %d°C\n", last_avg_temp);
  }

  vTaskDelay(pdMS_TO_TICKS(100));


  // 先拉高 Pump，開始計時
  digitalWrite(Pump, HIGH);
  for (int i = 0; i < totalSegments; i++) {
    if (cancel_flag) {
      goto CANCEL_RELEASE;
    }
    vTaskDelay(segmentDelay);
  }
  // 30 秒到，結束 HI，準備釋放
  digitalWrite(Pump, LOW);
  goto FINISH;

CANCEL_RELEASE:
  // 取消時也要先把 Pump 拉到 LOW
  digitalWrite(Pump, LOW);

FINISH:
  // 恢復待機溫度模式
  set_temp_mode(TEMP_MODE_STANDBY);
  // 不論是正常 60 s 到或取消，都跳回主畫面
  _ui_screen_change(&ui_SC_MAIN_MENU,
                    LV_SCR_LOAD_ANIM_NONE,
                    0, 0,
                    &ui_SC_MAIN_MENU_screen_init);
  vTaskDelete(NULL);
}
static void BTN_WATER_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Water」畫面
  _ui_screen_change(&ui_SC_Water, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Water_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(WaterPumpTask,"WaterPumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 WaterPumpTask!");
  }
}

// 清潔處理程序
static void CleanPumpTask(void * pvParameters) {
  cancel_flag = false;

  // 初始化進度條與 Label
  lv_bar_set_value(ui_BAR_Clean, 0, LV_ANIM_OFF);
  lv_label_set_text_fmt(ui_LAB__Clean_progress, "%d%%", 0);

  vTaskDelay(pdMS_TO_TICKS(100));

  // 等待最多 60 秒，或按下取消鍵
  const int totalSegments = 50;        // 60 000 ms / 100 ms
  const TickType_t segmentDelay = pdMS_TO_TICKS(100);

  // 先拉高 Pump，開始計時
  digitalWrite(Pump, HIGH);
  for (int i = 0; i <= totalSegments; i++) {
    // 更新進度：i 從 0 到 totalSegments 時，value 從 0 到 100
    int value = (i * 100) / totalSegments;
    lv_bar_set_value(ui_BAR_Clean, value, LV_ANIM_OFF);
    lv_label_set_text_fmt(ui_LAB__Clean_progress, "%d%%", value);

    if (cancel_flag) {
      goto CANCEL_RELEASE;
    }
    vTaskDelay(segmentDelay);
  }

  // 60 秒到，結束 HI，準備釋放
  digitalWrite(Pump, LOW);
  goto FINISH;

CANCEL_RELEASE:
  // 取消時也要先把 Pump 拉到 LOW
  digitalWrite(Pump, LOW);

FINISH:
  // 不論是正常 60 s 到或取消，都跳回主畫面
  _ui_screen_change(&ui_SC_MAIN_MENU,LV_SCR_LOAD_ANIM_NONE,0, 0,&ui_SC_MAIN_MENU_screen_init);
  vTaskDelete(NULL);
}
static void BTN_Clean_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  // 1. 先切到「Water」畫面
  _ui_screen_change(&ui_SC_Clean, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Clean_screen_init);

  // 2. 啟動一個任務去做泵浦切換
  BaseType_t ret = xTaskCreate(CleanPumpTask,"CleanPumpTask",2048,NULL,tskIDLE_PRIORITY + 1,NULL);
  if (ret != pdPASS) {
    Serial.println("無法建立 CleanPumpTask!");
  }
}

// 蒸氣處理程序
static void SteamPumpTask(void * pvParameters) {
  steam_cancel = false;

  Serial.printf("蒸氣任務啟動，開始循環模式：每5秒啟動pump 1秒\n");

  // 蒸氣循環：每隔5秒啟動pump 1秒
  while (!steam_cancel) {
      // 啟動pump 1秒
      set_pump(true);
      Serial.printf("蒸氣循環：pump開啟 1秒\n");

      // 等待1秒，每100ms檢查一次取消狀態
      for (int i = 0; i < 10 && !steam_cancel; i++) {
          vTaskDelay(pdMS_TO_TICKS(100));
      }

      // 關閉pump
      set_pump(false);
      if (!steam_cancel) {
          Serial.printf("蒸氣循環：pump關閉，等待4秒後下次循環\n");
      }

      // 等待4秒，每100ms檢查一次取消狀態
      for (int i = 0; i < 40 && !steam_cancel; i++) {
          vTaskDelay(pdMS_TO_TICKS(100));
      }
  }

  // 收尾：立即停止泵浦
  set_pump(false);
  Serial.printf("蒸氣任務結束，pump已立即關閉\n");

  // 清理蒸氣畫面溫度定時器和溫度顯示label（會自動恢復待機溫度）
  cleanup_steam_temp_timer();
  if (ui_Lab_Steam_Temp != NULL) {
      lv_obj_del(ui_Lab_Steam_Temp);
      ui_Lab_Steam_Temp = NULL;
  }

  // 跳回主畫面
  _ui_screen_change(&ui_SC_MAIN_MENU,LV_SCR_LOAD_ANIM_NONE,0, 0,&ui_SC_MAIN_MENU_screen_init);
  vTaskDelay(100);
vTaskDelete(NULL);
}
static void btn_Steam_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

    // 預設介面文字（保持原本內容）
    lv_label_set_text(
      ui_Lab_Steam_des,
      current_language == LANG_ZH
          ? "請先將右側旋鈕調至蒸氣位置"
          : "Please turn the right knob to \nthe steam position first."
  );
  lv_label_set_text(
      ui_Lab_Steam_COM,
      current_language == LANG_ZH
          ? "加熱中"
          : "Heating"
  );

  _ui_screen_change(&ui_SC_Steam, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_Steam_screen_init);

  // 創建左下角溫度顯示label
  ui_Lab_Steam_Temp = lv_label_create(ui_SC_Steam);
  if (ui_Lab_Steam_Temp != NULL) {
      lv_obj_set_width(ui_Lab_Steam_Temp, LV_SIZE_CONTENT);
      lv_obj_set_height(ui_Lab_Steam_Temp, LV_SIZE_CONTENT);
      lv_obj_set_x(ui_Lab_Steam_Temp, 20);  // 左下角位置
      lv_obj_set_y(ui_Lab_Steam_Temp, 420); // 左下角位置
      lv_obj_set_align(ui_Lab_Steam_Temp, LV_ALIGN_TOP_LEFT);
      lv_label_set_text_fmt(ui_Lab_Steam_Temp, "%d", last_avg_temp);
      lv_obj_set_style_text_color(ui_Lab_Steam_Temp, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_DEFAULT); // 紅色
      lv_obj_set_style_text_opa(ui_Lab_Steam_Temp, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
      lv_obj_set_style_text_font(ui_Lab_Steam_Temp, &lv_font_montserrat_20, LV_PART_MAIN | LV_STATE_DEFAULT);

      Serial.printf("蒸氣溫度label已創建，位置(20,420)，當前溫度: %d°C\n", last_avg_temp);
  } else {
      Serial.println("錯誤：無法創建蒸氣溫度label");
  }

  // 禁用BTN_Steam_COM按鈕，等待溫度達到115°C
  lv_obj_add_state(ui_BTN_Steam_COM, LV_STATE_DISABLED);

  // 重置蒸氣狀態
  steam_active = false;
  steam_cancel = false;

  // 切換到蒸氣溫度模式，統一溫度管理任務會自動開始加熱
  set_temp_mode(TEMP_MODE_STEAM);

  // 啟動蒸氣畫面溫度監控定時器
  if (steam_temp_timer == NULL) {
      steam_temp_timer = lv_timer_create(steam_temp_update_cb, 500, NULL);
  }

  vTaskDelay(100);
}

static void btn_Steam_COM_handler(lv_event_t * e) {
  if (lv_event_get_code(e) != LV_EVENT_CLICKED) return;

  if (!steam_active) {
      // 第一次按下：從「下一步」進入「運轉中」
      steam_active = true;
      steam_cancel = false;

      // 更新介面文字（依 current_language 顯示中／英）
      lv_label_set_text(
          ui_Lab_Steam_des,
          current_language == LANG_ZH
              ? "蒸氣將從噴氣口噴出,小心燙手"
              : "Steam will emit from the nozzle.\nBeware of burns."
      );
      lv_label_set_text(
          ui_Lab_Steam_COM,
          current_language == LANG_ZH
              ? "停止"
              : "Stop"
      );

      // 啟動 SteamPumpTask（將開啟pump）
      Serial.printf("平均溫度: %d°C (ADC查找表) : 蒸氣加熱器 (ON) - 蒸氣按鈕按下，開啟pump\n", last_avg_temp);
      BaseType_t ret = xTaskCreate(
          SteamPumpTask,
          "SteamPumpTask",
          2048,
          NULL,
          tskIDLE_PRIORITY + 1,
          NULL
      );
      if (ret != pdPASS) {
          Serial.println(
              current_language == LANG_ZH
                  ? "無法建立 SteamPumpTask!"
                  : "Failed to create SteamPumpTask!"
          );
      }
  } else {
      // 第二次按下：設定取消旗標，讓任務自行收尾
      steam_cancel = true;
      steam_active = false;

  }
}

// OTA進度條函數
void create_ota_progress_bar() {
    if (ota_bar == NULL) {
        ota_bar = lv_bar_create(ui_SC_Service);
        lv_obj_set_size(ota_bar, 446, 53);
        lv_obj_set_pos(ota_bar, 1, -4);
        lv_obj_set_align(ota_bar, LV_ALIGN_CENTER);
        lv_bar_set_range(ota_bar, 0, 100);
        lv_bar_set_value(ota_bar, 0, LV_ANIM_OFF);
        // 設置綠色背景（下載進度）
        lv_obj_set_style_bg_color(ota_bar, lv_color_hex(0x00FF00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(ota_bar, lv_color_hex(0x333333), LV_PART_MAIN | LV_STATE_DEFAULT);
    }
}

// 進度條更新任務 - 更安全的實作
void ota_progress_task(void *parameter) {
    Serial.println("OTA進度任務啟動");
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(100);  // 100ms
    int last_progress = -1;

    while (ota_updating) {
        // 使用vTaskDelayUntil確保精確的時間間隔
        vTaskDelayUntil(&xLastWakeTime, xFrequency);

        if (ota_bar != NULL && ota_updating) {
            // 只在進度變化時更新UI和顯示調試信息
            if (ota_progress_value != last_progress) {
                Serial.printf("進度條更新: %d%%\n", ota_progress_value);
                lv_bar_set_value(ota_bar, ota_progress_value, LV_ANIM_OFF);
                if (ota_is_flashing) {
                    // 燒錄時改為橘紅色
                    lv_obj_set_style_bg_color(ota_bar, lv_color_hex(0xFF4500), LV_PART_INDICATOR | LV_STATE_DEFAULT);
                } else {
                    // 下載時為綠色
                    lv_obj_set_style_bg_color(ota_bar, lv_color_hex(0x00FF00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
                }
                lv_refr_now(NULL);  // 強制刷新顯示
                last_progress = ota_progress_value;
            }
        }
    }
    Serial.println("OTA進度任務結束");
    vTaskDelete(NULL);
}

void update_ota_progress(int progress, bool is_flashing) {
    ota_progress_value = progress;
    ota_is_flashing = is_flashing;
}

void remove_ota_progress_bar() {
    if (ota_bar != NULL) {
        lv_obj_del(ota_bar);
        ota_bar = NULL;
    }
}

// 比較版本函數
bool is_newer_version(const String& remote_timestamp) {
    String current_timestamp = BUILD_TIMESTAMP;
    return remote_timestamp > current_timestamp;
}

// 檢查遠端OTA檔案
String check_remote_ota_file() {
    String list_url = "https://www.fusiontech.com.tw/OTA/Teaffic/list.php";
    HTTPClient http;
    http.begin(list_url);
    http.setFollowRedirects(HTTPC_STRICT_FOLLOW_REDIRECTS);

    int httpCode = http.GET();
    String newest_file = "";

    if (httpCode == HTTP_CODE_OK) {
        String payload = http.getString();
        Serial.println("檢查遠端檔案列表...");
        Serial.println("list.php回應: " + payload);

        // 使用ArduinoJson解析JSON陣列
        DynamicJsonDocument doc(2048);
        DeserializationError error = deserializeJson(doc, payload);

        if (error) {
            Serial.print("JSON解析失敗: ");
            Serial.println(error.c_str());
            http.end();
            return "";
        }

        JsonArray arr = doc.as<JsonArray>();
        String newest_timestamp = "";

        // 遍歷所有檔案
        for (JsonVariant value : arr) {
            String filename = value.as<String>();
            Serial.println("找到檔案: " + filename);

            // 檢查是否為Teaffic_*.bin檔案
            if (filename.startsWith("Teaffic_") && filename.endsWith(".bin")) {
                // 提取時間戳 (Teaffic_yyyymmddhhmmss.bin)
                int timestamp_start = filename.indexOf("_") + 1;
                int timestamp_end = filename.lastIndexOf(".");

                if (timestamp_start > 0 && timestamp_end > timestamp_start) {
                    String timestamp = filename.substring(timestamp_start, timestamp_end);

                    if (timestamp.length() == 14 && timestamp > newest_timestamp) {
                        newest_timestamp = timestamp;
                        newest_file = filename;
                        Serial.println("更新最新版本: " + newest_file + " (時間戳: " + timestamp + ")");
                    }
                }
            }
        }

        if (newest_file != "" && is_newer_version(newest_timestamp)) {
            Serial.println("發現新版本: " + newest_file);
            Serial.println("當前版本: " + String(BUILD_TIMESTAMP));
            Serial.println("遠端版本: " + newest_timestamp);
            return newest_file;
        } else {
            Serial.println("沒有發現新版本");
            if (newest_file != "") {
                Serial.println("最新遠端版本: " + newest_timestamp);
                Serial.println("當前版本: " + String(BUILD_TIMESTAMP));
            }
        }
    } else {
        Serial.printf("HTTP請求失敗，錯誤碼: %d\n", httpCode);
    }

    http.end();
    return "";
}

// 執行OTA更新 - 使用ESP-IDF原生API
void perform_ota_update(const String& filename) {
    String download_url = String(ota_server_url) + filename;
    Serial.println("開始下載: " + download_url);

    create_ota_progress_bar();
    ota_updating = true;
    ota_progress_value = 0;
    ota_is_flashing = false;

    // 啟動進度條更新任務 - 增加堆疊大小
    xTaskCreate(ota_progress_task, "ota_progress", 8192, NULL, 1, NULL);

    HTTPClient http;
    if (!http.begin(download_url)) {
        Serial.println("HTTP begin failed");
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    http.setTimeout(30000);  // 30秒超時
    http.setConnectTimeout(10000);  // 10秒連接超時

    int httpCode = http.GET();
    if (httpCode != HTTP_CODE_OK) {
        Serial.printf("HTTP GET fail %d\n", httpCode);
        http.end();
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    int contentLength = http.getSize();
    Serial.printf("檔案大小: %d bytes\n", contentLength);
    Serial.printf("可用Flash空間: %d bytes\n", ESP.getFreeSketchSpace());
    Serial.printf("可用PSRAM: %d bytes\n", ESP.getFreePsram());

    WiFiClient *stream = http.getStreamPtr();

    // 使用ESP-IDF原生OTA API
    const esp_partition_t* update_partition = esp_ota_get_next_update_partition(NULL);
    if (update_partition == NULL) {
        Serial.println("無法找到OTA更新分區");
        http.end();
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    Serial.printf("更新分區: %s, 大小: %d bytes\n", update_partition->label, update_partition->size);

    // 檢查檔案大小是否超出分區大小
    if (contentLength > update_partition->size) {
        Serial.printf("錯誤：檔案大小(%d bytes)超出分區大小(%d bytes)！\n",
                     contentLength, update_partition->size);
        http.end();
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    esp_ota_handle_t ota_handle = 0;
    esp_err_t err = esp_ota_begin(update_partition, OTA_SIZE_UNKNOWN, &ota_handle);
    if (err != ESP_OK) {
        Serial.printf("esp_ota_begin failed: %s\n", esp_err_to_name(err));
        http.end();
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    // 使用較小的緩衝區避免與LVGL衝突
    const size_t buffer_size = 8192;  // 8KB緩衝區，避免PSRAM衝突
    uint8_t* buff = (uint8_t*)malloc(buffer_size);
    if (buff == NULL) {
        Serial.println("記憶體分配失敗");
        esp_ota_end(ota_handle);
        http.end();
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }
    Serial.printf("使用 %d KB緩衝區進行下載\n", buffer_size / 1024);

    size_t total_written = 0;
    bool download_success = true;

    while (http.connected() && total_written < contentLength) {
        size_t available = stream->available();
        if (!available) {
            delay(1);
            continue;
        }

        size_t to_read = (available > buffer_size) ? buffer_size : available;
        int bytes_read = stream->readBytes(buff, to_read);
        if (bytes_read <= 0) break;

        err = esp_ota_write(ota_handle, buff, bytes_read);
        if (err != ESP_OK) {
            Serial.printf("esp_ota_write failed: %s\n", esp_err_to_name(err));
            download_success = false;
            break;
        }

        total_written += bytes_read;

        // 更新進度 - 減少更新頻率
        int progress = (total_written * 100) / contentLength;

        // 每32KB更新一次進度，減少對LVGL的影響
        if (total_written % 32768 == 0 || progress != ota_progress_value) {
            update_ota_progress(progress, false);
            Serial.printf("下載進度: %d%% (%d/%d bytes)\n", progress, total_written, contentLength);
        }

        yield();
    }

    free(buff);
    http.end();

    if (!download_success || total_written != contentLength) {
        Serial.printf("下載失敗或不完整！預期: %d bytes，實際: %d bytes\n", contentLength, total_written);
        esp_ota_end(ota_handle);
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    Serial.printf("下載完成: %d bytes\n", total_written);

    // 結束OTA寫入
    err = esp_ota_end(ota_handle);
    if (err != ESP_OK) {
        Serial.printf("esp_ota_end failed: %s\n", esp_err_to_name(err));
        ota_updating = false;
        remove_ota_progress_bar();
        return;
    }

    Serial.println("韌體寫入完成，準備切換分區");

    // 在設定開機分區前，先從檔名提取時間戳並寫入NVS
    String timestamp = extract_timestamp_from_filename(filename);
    if (timestamp.length() == 14) {
        Serial.printf("從檔名提取時間戳: %s\n", timestamp.c_str());
        save_firmware_timestamp_to_nvs(timestamp.c_str());
        Serial.println("時間戳已寫入NVS，重啟後將使用新時間戳");
    } else {
        Serial.printf("無法從檔名提取時間戳: %s\n", filename.c_str());
    }

    // 診斷：顯示更新後的分區信息
    print_partitions("POST-UPDATE");

    // 切換到燒錄進度顯示
    ota_is_flashing = true;
    for (int i = 0; i <= 100; i += 20) {
        update_ota_progress(i, true);
        delay(100);
    }

    // 設定新的開機分區
    Serial.printf("當前開機分區: %s\n", esp_ota_get_boot_partition()->label);
    Serial.printf("準備切換到分區: %s\n", update_partition->label);

    err = esp_ota_set_boot_partition(update_partition);
    if (err == ESP_OK) {
        Serial.println("設定開機分區成功");

        // 驗證開機分區是否正確設定
        const esp_partition_t* boot_partition = esp_ota_get_boot_partition();
        if (boot_partition == update_partition) {
            Serial.println("開機分區驗證成功");
        } else {
            Serial.printf("開機分區驗證失敗: 預期 %s，實際 %s\n",
                         update_partition->label, boot_partition->label);
        }

        Serial.println("3秒後重新啟動...");
        delay(3000);
        ota_updating = false;
        esp_restart();
    } else {
        Serial.printf("設定開機分區失敗: %s\n", esp_err_to_name(err));
        ota_updating = false;
        remove_ota_progress_bar();
    }
}

// OTA功能函數
void start_ota_mode() {
    ota_mode = true;

    Serial.println("進入OTA模式");

    // 先更改Lab_Service內容為OTA，並強制刷新顯示
    lv_label_set_text(ui_Lab_Service, "OTA");
    lv_obj_invalidate(ui_Lab_Service);  // 強制重繪
    lv_refr_now(NULL);  // 立即刷新顯示

    // 短暫延遲確保顯示更新
    delay(100);

    // 獲取MAC地址後6碼
    String mac = WiFi.macAddress();
    mac.replace(":", "");
    String ssid = "Teaffic_" + mac.substring(6);

    Serial.println("SSID: " + ssid);
    Serial.println("密碼: Teaffic1234");

    // 啟動WiFiManager
    WiFiManager wm;
    wm.resetSettings(); // 清除之前的設定

    // 設定AP參數 - 使用***********網段
    wm.setAPStaticIPConfig(IPAddress(10,10,10,10), IPAddress(10,10,10,10), IPAddress(255,255,255,0));

    // 自定義WiFiManager網頁
    wm.setTitle("Teaffic OTA");  // 設定標題
    wm.setShowInfoErase(false);  // 隱藏Info按鈕
    wm.setShowInfoUpdate(false); // 隱藏Update按鈕

    // 設定自定義的保存頁面HTML，將"ESP"改為"Teaffic"
    wm.setCustomHeadElement(
        "<style>"
        "body{font-family:Arial,sans-serif;}"
        "</style>"
        "<script>"
        "document.addEventListener('DOMContentLoaded', function() {"
        "  setTimeout(function() {"
        "    var elements = document.querySelectorAll('*');"
        "    for(var i = 0; i < elements.length; i++) {"
        "      if(elements[i].innerHTML && elements[i].innerHTML.includes('ESP')) {"
        "        elements[i].innerHTML = elements[i].innerHTML.replace(/ESP/g, 'Teaffic');"
        "      }"
        "    }"
        "  }, 100);"
        "});"
        "</script>"
    );

    // 只顯示Configure WiFi選項
    std::vector<const char*> menu = {"wifi"};
    wm.setMenu(menu);

    // 啟動配置入口網站
    if (!wm.startConfigPortal(ssid.c_str(), "Teaffic1234")) {
        Serial.println("WiFiManager配置失敗");
        return;
    }

    Serial.println("WiFi連接成功");
    Serial.print("IP地址: ");
    Serial.println(WiFi.localIP());

    // 更新顯示：顯示已連接的SSID
    String connected_ssid = WiFi.SSID();
    String display_text = "Connected (" + connected_ssid + ")";
    lv_label_set_text(ui_Lab_Service, display_text.c_str());
    lv_obj_invalidate(ui_Lab_Service);
    lv_refr_now(NULL);
    delay(100);

    // 啟動ArduinoOTA
    ArduinoOTA.setHostname("Teaffic_OTA");
    ArduinoOTA.setPassword("Teaffic1234");

    ArduinoOTA.onStart([]() {
        String type;
        if (ArduinoOTA.getCommand() == U_FLASH) {
            type = "sketch";
        } else { // U_SPIFFS
            type = "filesystem";
        }
        Serial.println("開始OTA更新: " + type);
    });

    ArduinoOTA.onEnd([]() {
        Serial.println("\nOTA更新完成");
    });

    ArduinoOTA.onProgress([](unsigned int progress, unsigned int total) {
        Serial.printf("進度: %u%%\r", (progress / (total / 100)));
    });

    ArduinoOTA.onError([](ota_error_t error) {
        Serial.printf("錯誤[%u]: ", error);
        if (error == OTA_AUTH_ERROR) {
            Serial.println("認證失敗");
        } else if (error == OTA_BEGIN_ERROR) {
            Serial.println("開始失敗");
        } else if (error == OTA_CONNECT_ERROR) {
            Serial.println("連接失敗");
        } else if (error == OTA_RECEIVE_ERROR) {
            Serial.println("接收失敗");
        } else if (error == OTA_END_ERROR) {
            Serial.println("結束失敗");
        }
    });

    ArduinoOTA.begin();
    Serial.println("OTA服務已啟動");

    // 檢查遠端OTA檔案
    Serial.println("檢查遠端OTA更新...");
    Serial.printf("當前韌體版本: %s\n", BUILD_TIMESTAMP);

    String remote_file = check_remote_ota_file();
    if (remote_file != "") {
        Serial.println("發現新版本，開始自動更新...");
        // 更新顯示：顯示下載中
        lv_label_set_text(ui_Lab_Service, "Downloading");
        lv_obj_invalidate(ui_Lab_Service);
        lv_refr_now(NULL);
        delay(100);
        perform_ota_update(remote_file);
    } else {
        Serial.println("沒有發現新版本，保持當前版本");
        Serial.println("OTA檢查完成，進入正常模式");
        // 更新顯示：顯示無更新
        lv_label_set_text(ui_Lab_Service, "No updates");
        lv_obj_invalidate(ui_Lab_Service);
        lv_refr_now(NULL);
    }
}

// OTA按鍵事件處理函數
void ui_event_BTN_OTA1(lv_event_t * e) {
    lv_event_code_t event_code = lv_event_get_code(e);
    static unsigned long last_output_time = 0;

    if (event_code == LV_EVENT_PRESSED) {
        ota1_press_start = millis();
        ota1_long_pressed = false;
        last_output_time = 0; // 重置輸出時間
    } else if (event_code == LV_EVENT_PRESSING) {
        // 每500ms輸出一次狀態
        unsigned long now = millis();
        if (now - last_output_time >= 500) {
            bool ota1_enabled = true; // BTN_OTA1 開機時就啟用
            bool ota2_enabled = !lv_obj_has_state(ui_BTN_OTA2, LV_STATE_DISABLED);
            bool ota3_enabled = !lv_obj_has_state(ui_BTN_OTA3, LV_STATE_DISABLED);
            Serial.printf("BTN_OTA1: %s, BTN_OTA2: %s, BTN_OTA3: %s\n",
                         ota1_enabled ? "enabled" : "disabled",
                         ota2_enabled ? "enabled" : "disabled",
                         ota3_enabled ? "enabled" : "disabled");
            last_output_time = now;
        }

        if (!ota1_long_pressed && (millis() - ota1_press_start >= 3000)) {
            ota1_long_pressed = true;
            // 啟用BTN_OTA1並設定為白色透明度30
            lv_obj_set_style_bg_opa(ui_BTN_OTA1, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_BTN_OTA1, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
            // 啟用BTN_OTA2
            lv_obj_clear_state(ui_BTN_OTA2, LV_STATE_DISABLED);
            Serial.println("OTA1長按3秒，BTN_OTA1變為可見，BTN_OTA2已啟用");
        }
    } else if (event_code == LV_EVENT_RELEASED) {
        ota1_press_start = 0;
    }
}

void ui_event_BTN_OTA2(lv_event_t * e) {
    lv_event_code_t event_code = lv_event_get_code(e);
    static unsigned long last_output_time = 0;

    if (event_code == LV_EVENT_PRESSED) {
        ota2_press_start = millis();
        ota2_long_pressed = false;
        last_output_time = 0; // 重置輸出時間
    } else if (event_code == LV_EVENT_PRESSING) {
        // 每500ms輸出一次狀態
        unsigned long now = millis();
        if (now - last_output_time >= 500) {
            bool ota1_enabled = true; // BTN_OTA1 開機時就啟用
            bool ota2_enabled = ota2_long_pressed; // BTN_OTA2 長按後才啟用
            bool ota3_enabled = !lv_obj_has_state(ui_BTN_OTA3, LV_STATE_DISABLED);
            Serial.printf("BTN_OTA1: %s, BTN_OTA2: %s, BTN_OTA3: %s\n",
                         ota1_enabled ? "enabled" : "disabled",
                         ota2_enabled ? "enabled" : "disabled",
                         ota3_enabled ? "enabled" : "disabled");
            last_output_time = now;
        }

        if (!ota2_long_pressed && (millis() - ota2_press_start >= 3000)) {
            ota2_long_pressed = true;
            // 啟用BTN_OTA2並設定為白色透明度30
            lv_obj_set_style_bg_opa(ui_BTN_OTA2, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_BTN_OTA2, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
            // 啟用BTN_OTA3
            lv_obj_clear_state(ui_BTN_OTA3, LV_STATE_DISABLED);
            Serial.println("OTA2長按3秒，BTN_OTA2變為可見，BTN_OTA3已啟用");
        }
    } else if (event_code == LV_EVENT_RELEASED) {
        ota2_press_start = 0;
    }
}

void ui_event_BTN_OTA3(lv_event_t * e) {
    lv_event_code_t event_code = lv_event_get_code(e);
    static unsigned long last_output_time = 0;

    if (event_code == LV_EVENT_PRESSED) {
        ota3_press_start = millis();
        ota3_long_pressed = false;
        last_output_time = 0; // 重置輸出時間
    } else if (event_code == LV_EVENT_PRESSING) {
        // 每500ms輸出一次狀態
        unsigned long now = millis();
        if (now - last_output_time >= 500) {
            bool ota1_enabled = true; // BTN_OTA1 開機時就啟用
            bool ota2_enabled = ota2_long_pressed; // BTN_OTA2 長按後才啟用
            bool ota3_enabled = ota3_long_pressed; // BTN_OTA3 長按後才啟用
            Serial.printf("BTN_OTA1: %s, BTN_OTA2: %s, BTN_OTA3: %s\n",
                         ota1_enabled ? "enabled" : "disabled",
                         ota2_enabled ? "enabled" : "disabled",
                         ota3_enabled ? "enabled" : "disabled");
            last_output_time = now;
        }

        if (!ota3_long_pressed && (millis() - ota3_press_start >= 3000)) {
            ota3_long_pressed = true;
            // 啟用BTN_OTA3並設定為白色透明度30
            lv_obj_set_style_bg_opa(ui_BTN_OTA3, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_BTN_OTA3, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
            // 進入OTA模式
            Serial.println("OTA3長按3秒，BTN_OTA3變為可見，進入OTA模式");
            start_ota_mode();
        }
    } else if (event_code == LV_EVENT_RELEASED) {
        ota3_press_start = 0;
    }
}

void setup() {
  lvglMutex = xSemaphoreCreateMutex();
  Serial.begin(115200);

  // 初始化NVS
  esp_err_t err = nvs_flash_init();
  if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
      ESP_ERROR_CHECK(nvs_flash_erase());
      err = nvs_flash_init();
  }
  ESP_ERROR_CHECK(err);

  // 從NVS載入韌體時間戳
  load_firmware_timestamp_from_nvs();

  // 測試檔名提取功能
  Serial.println("=== 測試檔名提取功能 ===");
  String test_filename = "Teaffic_20250718132926.bin";
  String extracted = extract_timestamp_from_filename(test_filename);
  Serial.printf("測試檔名: %s\n", test_filename.c_str());
  Serial.printf("提取結果: %s\n", extracted.c_str());
  Serial.println("=== 測試完成 ===");

  // 輸出編譯日期時間
  Serial.println("=== Teaffic PK200 系統啟動 ===");
  Serial.print("編譯日期時間: ");
  Serial.println(build_date);
  Serial.print("編譯時間戳: ");
  Serial.println(build_timestamp);

  // 診斷：顯示開機分區信息
  print_partitions("BOOT");

  // 檢查是否為OTA更新後的首次啟動，如果是則標記韌體有效
  const esp_partition_t* boot = esp_ota_get_boot_partition();
  const esp_partition_t* run  = esp_ota_get_running_partition();
  if (boot == run) {
      // 正常啟動，標記當前韌體有效
      esp_ota_mark_app_valid_cancel_rollback();
      Serial.println("韌體已標記為有效（防止rollback）");
  } else {
      Serial.printf("警告：開機分區(%s)與運行分區(%s)不同！\n", boot->label, run->label);
  }


  // 2. 設定 Arduino API 的讀值解析度與衰減
  analogReadResolution(12);                  // 直接給 12 位元
  analogSetPinAttenuation(ADC_PIN, ADC_11db);// Arduino attenuation enum adc_attenuation_t

  
  EEPROM.begin(16);            // 啟動 EEPROM
  load_language();             // 讀出上次選擇
  ft6336u.begin();

  pinMode(ADC_PIN, ANALOG);  // ESP32‑S3 Arduino 定義 ANALOG 為 ADC 模式
  Serial.println("IO14 已設定為 ADC 模式");

  //設定指定 GPIO 為開漏輸出，並預設拉低
  for (int i = 0; i < OD_COUNT; i++) {
    int pin = OD_PINS[i];
    // pinMode 支援 OPEN_DRAIN 模式
    pinMode(pin, OUTPUT_OPEN_DRAIN);
    // 明確拉低輸出
    digitalWrite(pin, LOW);
    Serial.printf("GPIO %d 設為 開漏輸出 並拉低\n", pin);
  }  

  size_t free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
  Serial.printf("Free internal SRAM: %u bytes\n", free_sram);
  Serial.print("PSRAM Size: ");

#ifdef GFX_EXTRA_PRE_INIT
  GFX_EXTRA_PRE_INIT();
#endif
  //Init Display
  gfx->begin();
#ifdef GFX_BL
  pinMode(GFX_BL, OUTPUT);
  digitalWrite(GFX_BL, HIGH);
#endif
  lv_init();
  screenWidth = gfx->width();
  screenHeight = gfx->height();

#ifdef ESP32
  disp_draw_buf1 = (lv_color_t *)heap_caps_malloc(screenWidth * screenHeight * sizeof(lv_color_t), MALLOC_CAP_SPIRAM);
#else
  disp_draw_buf1 = (lv_color_t *)malloc(sizeof(lv_color_t) * screenWidth * 32);
#endif
  if (!disp_draw_buf1) {
    Serial.println("LVGL disp_draw_buf allocate failed!");
  } else {
    lv_disp_draw_buf_init(&draw_buf, disp_draw_buf1, NULL, screenWidth * screenHeight);

    /* Initialize the display */
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = screenWidth;
    disp_drv.ver_res = screenHeight;
    disp_drv.flush_cb = my_disp_flush;
    disp_drv.draw_buf = &draw_buf;
    lv_disp_drv_register(&disp_drv);

    /* Initialize the (dummy) input device driver */
    static lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = my_touchpad_read;
    indev_drv.long_press_time = 50;  /*長按時間設為100毫秒*/
    lv_indev_drv_register(&indev_drv);
    ui_init();
    update_language();           // 第一次將文字顯示為當前語言
    bind_cancel_buttons();

    // 初始化溫度控制系統為待機模式
    set_temp_mode(TEMP_MODE_STANDBY);
        
    // 綁定 ui_BTN_PWR 的長按行為
    lv_obj_add_event_cb(ui_BTN_PWR, evt_btn_pwr, LV_EVENT_ALL, NULL);

      // 6. 綁定語言切換按鈕事件
    lv_obj_add_event_cb(ui_BTN_set_Chinese, evt_set_chinese, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_English, evt_set_english, LV_EVENT_ALL, NULL);

    // 小杯茶按鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_SMALL_TEA, btn_small_tea_handler, LV_EVENT_CLICKED, NULL);
    // 大杯茶按鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_BIG_TEA, btn_big_tea_handler, LV_EVENT_CLICKED, NULL);  
    // 小杯咖啡按鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_SMALL_Coffee, btn_small_coffee_handler, LV_EVENT_CLICKED, NULL); 
    // 大杯咖啡按鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_BIG_Coffee, btn_big_coffee_handler, LV_EVENT_CLICKED, NULL);           
    // 熱水鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_WATER, BTN_WATER_handler, LV_EVENT_CLICKED, NULL);   
    // 清潔鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_Clean, BTN_Clean_handler, LV_EVENT_CLICKED, NULL);
    // 蒸氣鈕物件註冊事件回呼
    lv_obj_add_event_cb(ui_BTN_Steam, btn_Steam_handler, LV_EVENT_CLICKED, NULL);
    lv_obj_add_event_cb(ui_BTN_Steam_COM, btn_Steam_COM_handler, LV_EVENT_CLICKED, NULL);
    //温度量測
        // 每 500 ms 取樣一次 ADC (每秒2次)
    lv_timer_create(adc_sample_timer_cb,   500, NULL);
    // 每 200 ms（或你想要的頻率）執行 UI 更新＋滯後控制
    temp_timer = lv_timer_create(check_temp_timer_cb,  200, NULL);
   
    // LVGL task
    xTaskCreatePinnedToCore(GUI_Task, "GUI_Task", 1024 * 64, NULL, 20, &GUI_TaskHandle, 1);

    // 統一溫度管理任務
    xTaskCreatePinnedToCore(unified_temp_control_task, "TempControl", 4096, NULL, 15, NULL, 0);
    Serial.println("統一溫度管理任務已啟動");


  }

}

void loop() {
    // OTA模式處理
    if (ota_mode) {
        ArduinoOTA.handle();
        vTaskDelay(pdMS_TO_TICKS(10));
        return;
    }

    // 背光、畫面與加熱管理
    uint32_t now = millis();
    if (!isBacklightDimmed && now - lastTouchTime > 10 * 60 * 1000) {
        // 1. 調暗背光
        analogWrite(GFX_BL, 10);
        isBacklightDimmed = true;

        // 2. 暫停溫度檢測定時器
        if (temp_timer) {
            lv_timer_del(temp_timer);
            temp_timer = NULL;
            Serial.println("進入待機模式：暫停溫度檢測定時器");
        }

        // 3. 重置相關狀態變數
        wait_for_temp = false;
        heater_on = false;
        screen_transitioned = false;

        // 4. 顯示圓弧與按鈕
        lv_obj_clear_flag(ui_PWR_ARC, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(ui_BTN_PWR, LV_OBJ_FLAG_HIDDEN);

        // 5. 隱藏等待加熱提示與進度條，並歸零
        lv_obj_add_flag(ui_Lab_wait_heater, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(ui_Bar_Wait_Heater, LV_OBJ_FLAG_HIDDEN);
        lv_bar_set_value(ui_Bar_Wait_Heater, 0, LV_ANIM_OFF);

        vTaskDelay (50);

        // 6. 切回電源開啟畫面
        _ui_screen_change(&ui_SC_PWR_ON,LV_SCR_LOAD_ANIM_NONE,0, 0,NULL);

        // 7. 關閉加熱元件
        digitalWrite(HEATER, LOW);

        Serial.println("進入待機模式：背光調暗，加熱器關閉，切換到電源開啟畫面");
    }
    // 每 1000 ms 印出一次由 timer 更新的平均溫度
    static uint32_t lastPrintTime = 0;
    if (now - lastPrintTime >= 1000) {
        int temp = last_avg_temp;  // 直接讀取 timer callback 的結果
        printf("平均温度: %d°C (ADC查找表)\n", temp);
        lastPrintTime = now;
    }



    vTaskDelay(pdMS_TO_TICKS(10));  // 讓其他任務有機會跑
}



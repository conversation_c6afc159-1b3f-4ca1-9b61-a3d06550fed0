{"version": "0.2.0", "configurations": [{"name": "ESP-IDF GDB OpenOCD Debug", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/<your_app>.elf", "miDebuggerPath": "xtensa-esp32-elf-gdb", "miDebuggerServerAddress": "localhost:3333", "miDebuggerArgs": "-ex \"set remote hardware-watchpoint-limit 2\"", "preLaunchTask": "idf: <PERSON>", "cwd": "${workspaceFolder}", "setupCommands": [{"text": "mon reset halt"}, {"text": "maintenance flush register-cache"}]}]}
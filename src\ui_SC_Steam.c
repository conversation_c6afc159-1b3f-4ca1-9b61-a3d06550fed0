// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Steam_screen_init(void)
{
    ui_SC_Steam = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Steam, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Steam, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Steam1 = lv_label_create(ui_SC_Steam);
    lv_obj_set_width(ui_Lab_Steam1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Steam1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Steam1, 0);
    lv_obj_set_y(ui_Lab_Steam1, -134);
    lv_obj_set_align(ui_Lab_Steam1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Steam1, "蒸氣");
    lv_obj_set_style_text_color(ui_Lab_Steam1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Steam1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Steam1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label47 = lv_label_create(ui_SC_Steam);
    lv_obj_set_width(ui_Label47, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label47, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label47, 1);
    lv_obj_set_y(ui_Label47, 3);
    lv_obj_set_align(ui_Label47, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label47, "熱水");
    lv_obj_set_style_text_color(ui_Label47, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label47, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label47, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image10 = lv_img_create(ui_SC_Steam);
    lv_img_set_src(ui_Image10, &ui_img_coffee_machine_icon_png);
    lv_obj_set_width(ui_Image10, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Image10, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Image10, 0);
    lv_obj_set_y(ui_Image10, -46);
    lv_obj_set_align(ui_Image10, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image10, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image10, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Lab_Steam_des = lv_label_create(ui_SC_Steam);
    lv_obj_set_width(ui_Lab_Steam_des, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Steam_des, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Steam_des, 0);
    lv_obj_set_y(ui_Lab_Steam_des, 56);
    lv_obj_set_align(ui_Lab_Steam_des, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Steam_des, "請先將右側旋鈕調至蒸氣位置");
    lv_obj_set_style_text_color(ui_Lab_Steam_des, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Steam_des, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Steam_des, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_Steam_COM = lv_btn_create(ui_SC_Steam);
    lv_obj_set_width(ui_BTN_Steam_COM, 100);
    lv_obj_set_height(ui_BTN_Steam_COM, 45);
    lv_obj_set_x(ui_BTN_Steam_COM, 0);
    lv_obj_set_y(ui_BTN_Steam_COM, 119);
    lv_obj_set_align(ui_BTN_Steam_COM, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Steam_COM, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_Steam_COM, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BTN_Steam_COM, lv_color_hex(0xFDBA25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_Steam_COM, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_Steam_COM, lv_color_hex(0xFD7C00), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_Steam_COM, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Steam_COM = lv_label_create(ui_BTN_Steam_COM);
    lv_obj_set_width(ui_Lab_Steam_COM, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Steam_COM, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_Steam_COM, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Steam_COM, "下一步");
    lv_obj_set_style_text_color(ui_Lab_Steam_COM, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Steam_COM, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Steam_COM, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_SC_Steam = ui_SC_Steam;
    uic_Lab_Steam1 = ui_Lab_Steam1;
    uic_Lab_Steam_des = ui_Lab_Steam_des;
    uic_BTN_Steam_COM = ui_BTN_Steam_COM;
    uic_Lab_Steam_COM = ui_Lab_Steam_COM;

}

// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_TEA_screen_init(void)
{
    ui_SC_TEA = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_TEA, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_TEA, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Tea1 = lv_label_create(ui_SC_TEA);
    lv_obj_set_width(ui_Lab_Tea1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Tea1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Tea1, 0);
    lv_obj_set_y(ui_Lab_Tea1, -134);
    lv_obj_set_align(ui_Lab_Tea1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Tea1, "沖茶");
    lv_obj_set_style_text_color(ui_Lab_Tea1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Tea1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Tea1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_BACK_MENU = lv_label_create(ui_SC_TEA);
    lv_obj_set_width(ui_LAB_BACK_MENU, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_BACK_MENU, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_BACK_MENU, -172);
    lv_obj_set_y(ui_LAB_BACK_MENU, -133);
    lv_obj_set_align(ui_LAB_BACK_MENU, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_BACK_MENU, "＜主選單");
    lv_obj_add_flag(ui_LAB_BACK_MENU, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_clear_flag(ui_LAB_BACK_MENU, LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE);     /// Flags
    lv_obj_set_style_text_color(ui_LAB_BACK_MENU, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_BACK_MENU, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_BACK_MENU, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel2 = lv_obj_create(ui_SC_TEA);
    lv_obj_set_width(ui_Panel2, 480);
    lv_obj_set_height(ui_Panel2, 240);
    lv_obj_set_x(ui_Panel2, 0);
    lv_obj_set_y(ui_Panel2, 80);
    lv_obj_set_flex_flow(ui_Panel2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_Panel2, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_Panel2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Panel2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel2, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_SMALL_TEA = lv_imgbtn_create(ui_Panel2);
    lv_imgbtn_set_src(ui_BTN_SMALL_TEA, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_tea_s_btn_png, NULL);
    lv_imgbtn_set_src(ui_BTN_SMALL_TEA, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_tea_s_btn_active_png, NULL);
    lv_obj_set_width(ui_BTN_SMALL_TEA, 189);
    lv_obj_set_height(ui_BTN_SMALL_TEA, 189);
    lv_obj_set_align(ui_BTN_SMALL_TEA, LV_ALIGN_CENTER);

    ui_Lab_Small_Tea = lv_label_create(ui_BTN_SMALL_TEA);
    lv_obj_set_width(ui_Lab_Small_Tea, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Small_Tea, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Small_Tea, 0);
    lv_obj_set_y(ui_Lab_Small_Tea, 75);
    lv_obj_set_align(ui_Lab_Small_Tea, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Small_Tea, "小杯");
    lv_obj_set_style_text_color(ui_Lab_Small_Tea, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Small_Tea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Small_Tea, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_BIG_TEA = lv_imgbtn_create(ui_Panel2);
    lv_imgbtn_set_src(ui_BTN_BIG_TEA, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_tea_btn_png, NULL);
    lv_imgbtn_set_src(ui_BTN_BIG_TEA, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_tea_btn_active_png, NULL);
    lv_obj_set_width(ui_BTN_BIG_TEA, 189);
    lv_obj_set_height(ui_BTN_BIG_TEA, 189);
    lv_obj_set_align(ui_BTN_BIG_TEA, LV_ALIGN_CENTER);

    ui_LAB_Big_Tea = lv_label_create(ui_BTN_BIG_TEA);
    lv_obj_set_width(ui_LAB_Big_Tea, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_Big_Tea, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_Big_Tea, 0);
    lv_obj_set_y(ui_LAB_Big_Tea, 75);
    lv_obj_set_align(ui_LAB_Big_Tea, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_Big_Tea, "大杯");
    lv_obj_set_style_text_color(ui_LAB_Big_Tea, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_Big_Tea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_Big_Tea, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_LAB_BACK_MENU, ui_event_LAB_BACK_MENU, LV_EVENT_ALL, NULL);
    uic_LAB_BACK_MENU = ui_LAB_BACK_MENU;
    uic_BTN_SMALL_TEA = ui_BTN_SMALL_TEA;
    uic_Lab_Small_Tea = ui_Lab_Small_Tea;

}

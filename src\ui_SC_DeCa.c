// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_DeCa_screen_init(void)
{
    ui_SC_DeCa = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_DeCa, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_DeCa, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_DeCa = lv_label_create(ui_SC_DeCa);
    lv_obj_set_width(ui_Lab_DeCa, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_DeCa, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_DeCa, 0);
    lv_obj_set_y(ui_Lab_DeCa, -134);
    lv_obj_set_align(ui_Lab_DeCa, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_DeCa, "除鈣");
    lv_obj_set_style_text_color(ui_Lab_DeCa, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_DeCa, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_DeCa, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image7 = lv_img_create(ui_SC_DeCa);
    lv_img_set_src(ui_Image7, &ui_img_remove_ca_icon_png);
    lv_obj_set_width(ui_Image7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Image7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Image7, 0);
    lv_obj_set_y(ui_Image7, -46);
    lv_obj_set_align(ui_Image7, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image7, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_img_set_zoom(ui_Image7, 384);

    ui_BAR_Big_Coffee1 = lv_bar_create(ui_SC_DeCa);
    lv_bar_set_value(ui_BAR_Big_Coffee1, 100, LV_ANIM_OFF);
    lv_bar_set_start_value(ui_BAR_Big_Coffee1, 0, LV_ANIM_OFF);
    lv_obj_set_width(ui_BAR_Big_Coffee1, 332);
    lv_obj_set_height(ui_BAR_Big_Coffee1, 24);
    lv_obj_set_x(ui_BAR_Big_Coffee1, 0);
    lv_obj_set_y(ui_BAR_Big_Coffee1, 37);
    lv_obj_set_align(ui_BAR_Big_Coffee1, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_BAR_Big_Coffee1, lv_color_hex(0x3A342C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Big_Coffee1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_BAR_Big_Coffee1, lv_color_hex(0xFDBA25), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Big_Coffee1, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BAR_Big_Coffee1, lv_color_hex(0xFD7C00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BAR_Big_Coffee1, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    ui_Lab_DeCaing = lv_label_create(ui_SC_DeCa);
    lv_obj_set_width(ui_Lab_DeCaing, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_DeCaing, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_DeCaing, -265);
    lv_obj_set_y(ui_Lab_DeCaing, 215);
    lv_obj_set_align(ui_Lab_DeCaing, LV_ALIGN_TOP_RIGHT);
    lv_label_set_text(ui_Lab_DeCaing, "除鈣中...");
    lv_obj_set_style_text_color(ui_Lab_DeCaing, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_DeCaing, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_DeCaing, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB__Big_Coffee_progress1 = lv_label_create(ui_SC_DeCa);
    lv_obj_set_width(ui_LAB__Big_Coffee_progress1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB__Big_Coffee_progress1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB__Big_Coffee_progress1, 6);
    lv_obj_set_y(ui_LAB__Big_Coffee_progress1, 72);
    lv_obj_set_align(ui_LAB__Big_Coffee_progress1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB__Big_Coffee_progress1, "10%");
    lv_obj_set_style_text_color(ui_LAB__Big_Coffee_progress1, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB__Big_Coffee_progress1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB__Big_Coffee_progress1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Wait6 = lv_label_create(ui_SC_DeCa);
    lv_obj_set_width(ui_Lab_Wait6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Wait6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Wait6, 277);
    lv_obj_set_y(ui_Lab_Wait6, 217);
    lv_label_set_text(ui_Lab_Wait6, "，請稍候");
    lv_obj_set_style_text_color(ui_Lab_Wait6, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Wait6, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Wait6, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_Cancel5 = lv_btn_create(ui_SC_DeCa);
    lv_obj_set_width(ui_BTN_Cancel5, 100);
    lv_obj_set_height(ui_BTN_Cancel5, 45);
    lv_obj_set_x(ui_BTN_Cancel5, 0);
    lv_obj_set_y(ui_BTN_Cancel5, 119);
    lv_obj_set_align(ui_BTN_Cancel5, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Cancel5, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_Cancel5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BTN_Cancel5, lv_color_hex(0xFDBA25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_Cancel5, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_Cancel5, lv_color_hex(0xFD7C00), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_Cancel5, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Cancel5 = lv_label_create(ui_BTN_Cancel5);
    lv_obj_set_width(ui_Lab_Cancel5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Cancel5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_Cancel5, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Cancel5, "取消");
    lv_obj_set_style_text_color(ui_Lab_Cancel5, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Cancel5, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Cancel5, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_Lab_DeCaing = ui_Lab_DeCaing;

}

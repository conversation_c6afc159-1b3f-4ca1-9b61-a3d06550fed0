// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_PWR_ON_screen_init(void)
{
    ui_SC_PWR_ON = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_PWR_ON, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_PWR_ON, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_PWR = lv_imgbtn_create(ui_SC_PWR_ON);
    lv_imgbtn_set_src(ui_BTN_PWR, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_power_on_png, NULL);
    lv_imgbtn_set_src(ui_BTN_PWR, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_power_on_png, NULL);
    lv_obj_set_width(ui_BTN_PWR, 92);
    lv_obj_set_height(ui_BTN_PWR, 93);
    lv_obj_set_x(ui_BTN_PWR, 0);
    lv_obj_set_y(ui_BTN_PWR, 40);
    lv_obj_set_align(ui_BTN_PWR, LV_ALIGN_CENTER);
    lv_obj_set_style_img_recolor(ui_BTN_PWR, lv_color_hex(0x333333), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_img_recolor_opa(ui_BTN_PWR, 64, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_Label1 = lv_label_create(ui_SC_PWR_ON);
    lv_obj_set_width(ui_Label1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label1, 0);
    lv_obj_set_y(ui_Label1, -80);
    lv_obj_set_align(ui_Label1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label1, "Teaffic");
    lv_obj_set_style_text_decor(ui_Label1, LV_TEXT_DECOR_NONE, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label1, &ui_font_NS_BL_56, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_PWR_ARC = lv_arc_create(ui_SC_PWR_ON);
    lv_obj_set_width(ui_PWR_ARC, 140);
    lv_obj_set_height(ui_PWR_ARC, 140);
    lv_obj_set_x(ui_PWR_ARC, 0);
    lv_obj_set_y(ui_PWR_ARC, 40);
    lv_obj_set_align(ui_PWR_ARC, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_PWR_ARC, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_SCROLL_ELASTIC |
                      LV_OBJ_FLAG_SCROLL_MOMENTUM | LV_OBJ_FLAG_SCROLL_CHAIN);     /// Flags
    lv_arc_set_value(ui_PWR_ARC, 0);
    lv_arc_set_bg_angles(ui_PWR_ARC, 0, 360);
    lv_arc_set_rotation(ui_PWR_ARC, -90);
    lv_obj_set_style_arc_color(ui_PWR_ARC, lv_color_hex(0xEAA032), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_PWR_ARC, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_shadow_color(ui_PWR_ARC, lv_color_hex(0xF6BB62), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_PWR_ARC, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui_PWR_ARC, 20, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui_PWR_ARC, 20, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_color(ui_PWR_ARC, lv_color_hex(0xEAA032), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_PWR_ARC, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_PWR_ARC, 20, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_PWR_ARC, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_PWR_ARC, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    ui_Lab_wait_heater = lv_label_create(ui_SC_PWR_ON);
    lv_obj_set_width(ui_Lab_wait_heater, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_wait_heater, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_wait_heater, 2);
    lv_obj_set_y(ui_Lab_wait_heater, -11);
    lv_obj_set_align(ui_Lab_wait_heater, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_wait_heater, "等待加熱中");
    lv_obj_add_flag(ui_Lab_wait_heater, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_text_color(ui_Lab_wait_heater, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_wait_heater, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_wait_heater, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Bar_Wait_Heater = lv_bar_create(ui_SC_PWR_ON);
    lv_bar_set_range(ui_Bar_Wait_Heater, 0, 90);
    lv_obj_set_width(ui_Bar_Wait_Heater, 291);
    lv_obj_set_height(ui_Bar_Wait_Heater, 28);
    lv_obj_set_x(ui_Bar_Wait_Heater, 5);
    lv_obj_set_y(ui_Bar_Wait_Heater, 46);
    lv_obj_set_align(ui_Bar_Wait_Heater, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Bar_Wait_Heater, LV_OBJ_FLAG_HIDDEN);     /// Flags

    lv_obj_set_style_bg_color(ui_Bar_Wait_Heater, lv_color_hex(0xFDBA25), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Bar_Wait_Heater, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_Bar_Wait_Heater, lv_color_hex(0xFD4C00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_Bar_Wait_Heater, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    ui_Lab_Build_Time = lv_label_create(ui_SC_PWR_ON);
    lv_obj_set_width(ui_Lab_Build_Time, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Build_Time, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Build_Time, 10);
    lv_obj_set_y(ui_Lab_Build_Time, -10);
    lv_obj_set_align(ui_Lab_Build_Time, LV_ALIGN_BOTTOM_LEFT);
    lv_label_set_text(ui_Lab_Build_Time, "Build Time");
    lv_obj_set_style_text_color(ui_Lab_Build_Time, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Build_Time, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Build_Time, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_SC_PWR_ON = ui_SC_PWR_ON;
    uic_BTN_PWR = ui_BTN_PWR;
    uic_PWR_ARC = ui_PWR_ARC;
    uic_Lab_wait_heater = ui_Lab_wait_heater;
    uic_Bar_Wait_Heater = ui_Bar_Wait_Heater;
    uic_Lab_Build_Time = ui_Lab_Build_Time;

}

// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Clean_screen_init(void)
{
    ui_SC_Clean = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Clean, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Clean, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Clean1 = lv_label_create(ui_SC_Clean);
    lv_obj_set_width(ui_Lab_Clean1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Clean1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Clean1, 0);
    lv_obj_set_y(ui_Lab_Clean1, -134);
    lv_obj_set_align(ui_Lab_Clean1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Clean1, "清潔");
    lv_obj_set_style_text_color(ui_Lab_Clean1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Clean1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Clean1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image9 = lv_img_create(ui_SC_Clean);
    lv_img_set_src(ui_Image9, &ui_img_clean_icon_png);
    lv_obj_set_width(ui_Image9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Image9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Image9, 0);
    lv_obj_set_y(ui_Image9, -46);
    lv_obj_set_align(ui_Image9, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image9, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_img_set_zoom(ui_Image9, 384);

    ui_BAR_Clean = lv_bar_create(ui_SC_Clean);
    lv_bar_set_value(ui_BAR_Clean, 100, LV_ANIM_OFF);
    lv_bar_set_start_value(ui_BAR_Clean, 0, LV_ANIM_OFF);
    lv_obj_set_width(ui_BAR_Clean, 332);
    lv_obj_set_height(ui_BAR_Clean, 24);
    lv_obj_set_x(ui_BAR_Clean, 0);
    lv_obj_set_y(ui_BAR_Clean, 37);
    lv_obj_set_align(ui_BAR_Clean, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_BAR_Clean, lv_color_hex(0x3A342C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Clean, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_BAR_Clean, lv_color_hex(0xFDBA25), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Clean, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BAR_Clean, lv_color_hex(0xFD7C00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BAR_Clean, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    ui_Lab_Cleaning = lv_label_create(ui_SC_Clean);
    lv_obj_set_width(ui_Lab_Cleaning, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Cleaning, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Cleaning, -263);
    lv_obj_set_y(ui_Lab_Cleaning, 217);
    lv_obj_set_align(ui_Lab_Cleaning, LV_ALIGN_TOP_RIGHT);
    lv_label_set_text(ui_Lab_Cleaning, "清潔中...");
    lv_obj_set_style_text_color(ui_Lab_Cleaning, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Cleaning, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Cleaning, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB__Clean_progress = lv_label_create(ui_SC_Clean);
    lv_obj_set_width(ui_LAB__Clean_progress, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB__Clean_progress, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB__Clean_progress, 6);
    lv_obj_set_y(ui_LAB__Clean_progress, 72);
    lv_obj_set_align(ui_LAB__Clean_progress, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB__Clean_progress, "10%");
    lv_obj_set_style_text_color(ui_LAB__Clean_progress, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB__Clean_progress, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB__Clean_progress, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Wait5 = lv_label_create(ui_SC_Clean);
    lv_obj_set_width(ui_Lab_Wait5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Wait5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Wait5, 276);
    lv_obj_set_y(ui_Lab_Wait5, 215);
    lv_label_set_text(ui_Lab_Wait5, "，請稍候");
    lv_obj_set_style_text_color(ui_Lab_Wait5, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Wait5, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Wait5, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_Cancel8 = lv_btn_create(ui_SC_Clean);
    lv_obj_set_width(ui_BTN_Cancel8, 100);
    lv_obj_set_height(ui_BTN_Cancel8, 45);
    lv_obj_set_x(ui_BTN_Cancel8, 0);
    lv_obj_set_y(ui_BTN_Cancel8, 119);
    lv_obj_set_align(ui_BTN_Cancel8, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Cancel8, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_Cancel8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BTN_Cancel8, lv_color_hex(0xFDBA25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_Cancel8, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_Cancel8, lv_color_hex(0xFD7C00), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_Cancel8, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Stop2 = lv_label_create(ui_BTN_Cancel8);
    lv_obj_set_width(ui_Lab_Stop2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Stop2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_Stop2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Stop2, "取消");
    lv_obj_set_style_text_color(ui_Lab_Stop2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Stop2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Stop2, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_SC_Clean = ui_SC_Clean;
    uic_BAR_Clean = ui_BAR_Clean;
    uic_Lab_Cleaning = ui_Lab_Cleaning;
    uic_LAB__Clean_progress = ui_LAB__Clean_progress;

}

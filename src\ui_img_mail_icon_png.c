// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/mail_icon.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_mail_icon_png_data[] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0x07,0x48,0xBF,0x07,0xD9,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0xBF,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xFF,0x9F,0x07,0xFF,0xBF,0x07,0xD9,0x9F,0x07,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x03,0x5F,0x07,0xD3,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x7F,0x07,0xFF,0x5F,0x07,0xD3,
    0x00,0x00,0x03,0x00,0x00,0x01,0x00,0x00,0x03,0x00,0x00,0x09,0x3E,0x07,0xF1,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x5F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x5F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x5F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x3F,0x07,0xFF,0x5F,0x07,0xFF,0x3F,0x07,0xFF,0x3E,0x07,0xF1,0x00,0x00,0x09,0x00,0x00,0x03,0x00,0x00,0x06,0x00,0x00,0x14,0xFE,0x06,0xF2,0x1E,0x07,0xFF,0x1E,0x07,0xFF,0x9D,0x06,0xE0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x36,0x05,0xA0,0x9D,0x06,0xE0,0x1E,0x07,0xFF,
    0x1E,0x07,0xFF,0xFE,0x06,0xF2,0x00,0x00,0x14,0x00,0x00,0x06,0x00,0x00,0x0A,0x00,0x00,0x1F,0xDE,0x06,0xF3,0xDE,0x06,0xFF,0xDE,0x06,0xFF,0x9D,0x06,0xF1,0xF1,0x03,0x9B,0x00,0x00,0x64,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x64,0xF1,0x03,0x9B,0x9D,0x06,0xF1,0xDE,0x06,0xFF,0xDE,0x06,0xFF,0xBE,0x06,0xF3,0x00,0x00,0x1F,0x00,0x00,0x0A,0x00,0x00,0x0D,0x00,0x00,0x27,0x9E,0x06,0xF4,0x9E,0x06,0xFF,0x9E,0x06,0xFF,0xBE,0x06,0xFF,0xBE,0x06,0xFF,0x1B,0x06,0xDF,0x2E,0x03,0x87,0x00,0x00,0x5E,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5D,0x00,0x00,0x5E,0x2E,0x03,0x88,0x1B,0x06,0xE0,0xBE,0x06,0xFF,
    0xBE,0x06,0xFF,0xBE,0x06,0xFF,0xBE,0x06,0xFF,0x9E,0x06,0xF4,0x00,0x00,0x27,0x00,0x00,0x0D,0x00,0x00,0x0F,0x00,0x00,0x2A,0x5D,0x06,0xF5,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0xBB,0x05,0xC9,0xAC,0x02,0x62,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0x00,0x00,0x42,0xCD,0x02,0x63,0xDB,0x05,0xC9,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x7E,0x06,0xFF,0x3D,0x06,0xF5,0x00,0x00,0x2A,0x00,0x00,0x0F,0x00,0x00,0x10,0x00,0x00,0x2C,0x1D,0x06,0xF5,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFC,0xBB,0x05,0xAA,0xAD,0x02,0x39,0x00,0x00,0x22,0x00,0x00,0x22,0x00,0x00,0x22,0x00,0x00,0x22,0x00,0x00,0x22,0x00,0x00,0x22,0x00,0x00,0x22,0xCD,0x02,0x39,0xBB,0x05,0xAA,0x3E,0x06,0xFC,0x3E,0x06,0xFF,0x3E,0x06,0xFF,
    0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x3E,0x06,0xFF,0x1D,0x06,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0xFD,0x05,0xF5,0x1D,0x06,0xFF,0xFD,0x05,0xFF,0x3A,0x05,0xDE,0xF8,0x04,0xC7,0x1D,0x06,0xFE,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xF6,0xBC,0x05,0x89,0x0E,0x03,0x18,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x0F,0x03,0x19,0xBC,0x05,0x8A,0xFD,0x05,0xF6,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0x1D,0x06,0xFE,0xF8,0x04,0xC7,0x5A,0x05,0xDE,0x1D,0x06,0xFF,0x1D,0x06,0xFF,0xFD,0x05,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0xBC,0x05,0xF5,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xB8,0x04,0xD2,0x00,0x00,0x6D,0x4B,0x02,0x81,0x3A,0x05,0xD3,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xEA,0xBC,0x05,0x6D,0x14,0x04,0x0D,0xBC,0x05,0x6D,0xDD,0x05,0xEA,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xDD,0x05,0xFF,
    0xDD,0x05,0xFF,0x3A,0x05,0xD2,0x2B,0x02,0x80,0x00,0x00,0x6D,0xB8,0x04,0xD2,0xDD,0x05,0xFF,0xDD,0x05,0xFF,0xBC,0x05,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x9C,0x05,0xF5,0xBD,0x05,0xFF,0xBD,0x05,0xFF,0x98,0x04,0xD1,0x00,0x00,0x6D,0x00,0x00,0x69,0x00,0x00,0x64,0xEF,0x02,0x8A,0x3B,0x05,0xE1,0x9D,0x05,0xFF,0xBD,0x05,0xFF,0x9D,0x05,0xFF,0xBD,0x05,0xFF,0x9D,0x05,0xFF,0xBD,0x05,0xF7,0x9D,0x05,0xFF,0xBD,0x05,0xFF,0x9D,0x05,0xFF,0xBD,0x05,0xFF,0xBD,0x05,0xFF,0x3B,0x05,0xE0,0xEF,0x02,0x8A,0x00,0x00,0x64,0x00,0x00,0x69,0x00,0x00,0x6D,0xB8,0x04,0xD1,0x9D,0x05,0xFF,0x9D,0x05,0xFF,0x7C,0x05,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x5C,0x05,0xF5,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x98,0x04,0xCE,0x00,0x00,0x5F,0x00,0x00,0x5D,0x00,0x00,0x61,0x00,0x00,0x64,0x00,0x00,0x63,0x72,0x03,0x99,0x3C,0x05,0xEC,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x3C,0x05,0xEC,
    0x72,0x03,0x99,0x00,0x00,0x63,0x00,0x00,0x64,0x00,0x00,0x61,0x00,0x00,0x5D,0x00,0x00,0x5F,0x78,0x04,0xCE,0x7D,0x05,0xFF,0x7D,0x05,0xFF,0x5C,0x05,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x1C,0x05,0xF5,0x3C,0x05,0xFF,0x3C,0x05,0xFF,0x78,0x04,0xC9,0x00,0x00,0x49,0x00,0x00,0x43,0x00,0x00,0x4B,0x00,0x00,0x57,0x00,0x00,0x61,0x00,0x00,0x64,0x82,0x00,0x67,0xD5,0x03,0xA8,0x1C,0x05,0xF6,0x3C,0x05,0xFF,0x3C,0x05,0xFF,0x3C,0x05,0xFF,0x1C,0x05,0xF5,0xD4,0x03,0xA8,0x62,0x00,0x67,0x00,0x00,0x64,0x00,0x00,0x61,0x00,0x00,0x58,0x00,0x00,0x4B,0x00,0x00,0x43,0x00,0x00,0x48,0x78,0x04,0xC9,0x3C,0x05,0xFF,0x3C,0x05,0xFF,0x1C,0x05,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0xDC,0x04,0xF5,0x1C,0x05,0xFF,0x1C,0x05,0xFF,0x78,0x04,0xC5,0x00,0x00,0x35,0x00,0x00,0x29,0x00,0x00,0x2E,0x00,0x00,0x3C,0x00,0x00,0x4C,0x00,0x00,0x59,0x00,0x00,0x62,0x00,0x00,0x64,0xE5,0x00,0x6E,0xD5,0x03,0xB3,0x37,0x04,0xC3,0xD5,0x03,0xB2,0xE5,0x00,0x6E,
    0x00,0x00,0x64,0x00,0x00,0x62,0x00,0x00,0x59,0x00,0x00,0x4C,0x00,0x00,0x3C,0x00,0x00,0x2D,0x00,0x00,0x28,0x00,0x00,0x35,0x78,0x04,0xC5,0x1C,0x05,0xFF,0xFC,0x04,0xFF,0xFC,0x04,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0xBB,0x04,0xF5,0xDC,0x04,0xFF,0xDC,0x04,0xFF,0x39,0x04,0xC3,0x00,0x00,0x29,0x00,0x00,0x17,0x00,0x00,0x16,0x00,0x00,0x1F,0x00,0x00,0x2E,0x00,0x00,0x3F,0x00,0x00,0x4F,0x00,0x00,0x5C,0x00,0x00,0x63,0x00,0x00,0x67,0x00,0x00,0x67,0x00,0x00,0x66,0x00,0x00,0x63,0x00,0x00,0x5B,0x00,0x00,0x4E,0x00,0x00,0x3F,0x00,0x00,0x2E,0x00,0x00,0x1F,0x00,0x00,0x15,0x00,0x00,0x16,0x00,0x00,0x29,0x39,0x04,0xC3,0xDC,0x04,0xFF,0xDC,0x04,0xFF,0xBB,0x04,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x9B,0x04,0xF5,0x9C,0x04,0xFF,0x9C,0x04,0xFF,0x18,0x04,0xC3,0x00,0x00,0x25,0x00,0x00,0x0F,0x00,0x00,0x08,0x00,0x00,0x0C,0x00,0x00,0x15,0x00,0x00,0x22,0x00,0x00,0x31,0x00,0x00,0x42,0x00,0x00,0x50,0x00,0x00,0x5A,0x00,0x00,0x5D,
    0x00,0x00,0x59,0x00,0x00,0x50,0x00,0x00,0x42,0x00,0x00,0x31,0x00,0x00,0x22,0x00,0x00,0x15,0x00,0x00,0x0C,0x00,0x00,0x08,0x00,0x00,0x0F,0x00,0x00,0x25,0x18,0x04,0xC3,0x9C,0x04,0xFF,0x9C,0x04,0xFF,0x9B,0x04,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x5B,0x04,0xF5,0x7B,0x04,0xFF,0x7C,0x04,0xFF,0xF8,0x03,0xC3,0x00,0x00,0x24,0x00,0x00,0x0C,0x00,0x00,0x03,0x00,0x00,0x03,0x00,0x00,0x07,0x00,0x00,0x0D,0x00,0x00,0x17,0x00,0x00,0x24,0x00,0x00,0x32,0x00,0x00,0x3C,0x00,0x00,0x40,0x00,0x00,0x3C,0x00,0x00,0x32,0x00,0x00,0x24,0x00,0x00,0x17,0x00,0x00,0x0D,0x00,0x00,0x07,0x00,0x00,0x03,0x00,0x00,0x03,0x00,0x00,0x0C,0x00,0x00,0x24,0xF8,0x03,0xC3,0x7C,0x04,0xFF,0x7B,0x04,0xFF,0x5B,0x04,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x1B,0x04,0xF5,0x3B,0x04,0xFF,0x3B,0x04,0xFF,0x1A,0x04,0xEC,0xF9,0x03,0xB7,0x1A,0x04,0xB0,0x3B,0x04,0xAC,0x3B,0x04,0xAC,0x3B,0x04,0xAC,0x3B,0x04,0xAD,0x3B,0x04,0xAE,0x1A,0x04,0xB0,0x1A,0x04,0xB3,
    0xFA,0x03,0xB6,0xFA,0x03,0xB6,0xFA,0x03,0xB6,0x1A,0x04,0xB3,0x1A,0x04,0xB0,0x3B,0x04,0xAE,0x3B,0x04,0xAD,0x3B,0x04,0xAC,0x3B,0x04,0xAC,0x3B,0x04,0xAC,0x1A,0x04,0xB0,0xF9,0x03,0xB7,0x1A,0x04,0xEC,0x3B,0x04,0xFF,0x3B,0x04,0xFF,0x1B,0x04,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0xFA,0x03,0xF5,0xFB,0x03,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0x1B,0x04,0xFF,0xFB,0x03,0xFF,0xFB,0x03,0xFF,0x1B,0x04,0xFF,0xFA,0x03,0xF5,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x10,0x00,0x00,0x2C,0x78,0x03,0xD3,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,
    0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0xDB,0x03,0xFF,0x78,0x03,0xD3,0x00,0x00,0x2C,0x00,0x00,0x10,0x00,0x00,0x0F,0x00,0x00,0x2A,0x6A,0x01,0x6B,0xF6,0x02,0xCA,0x58,0x03,0xE3,0x58,0x03,0xE0,0x59,0x03,0xDD,0x59,0x03,0xDB,0x79,0x03,0xDA,0x59,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDA,0x59,0x03,0xDA,0x79,0x03,0xDA,0x79,0x03,0xDB,0x59,0x03,0xDD,0x58,0x03,0xE0,0x38,0x03,0xE3,0xF6,0x02,0xCA,0x6A,0x01,0x6B,0x00,0x00,0x2A,0x00,0x00,0x0F,0x00,0x00,0x0D,0x00,0x00,0x27,0x00,0x00,0x48,0x00,0x00,0x63,0x00,0x00,0x6E,0x00,0x00,0x6C,0x00,0x00,0x65,0x00,0x00,0x5F,0x00,0x00,0x5D,0x00,0x00,0x5C,0x00,0x00,0x5C,
    0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5C,0x00,0x00,0x5D,0x00,0x00,0x5F,0x00,0x00,0x65,0x00,0x00,0x6C,0x00,0x00,0x6E,0x00,0x00,0x63,0x00,0x00,0x48,0x00,0x00,0x27,0x00,0x00,0x0D,0x00,0x00,0x0B,0x00,0x00,0x1F,0x00,0x00,0x3B,0x00,0x00,0x55,0x00,0x00,0x62,0x00,0x00,0x65,0x00,0x00,0x64,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x61,0x00,0x00,0x62,0x00,0x00,0x62,0x00,0x00,0x64,0x00,0x00,0x65,0x00,0x00,0x62,0x00,0x00,0x55,0x00,0x00,0x3B,0x00,0x00,0x1F,0x00,0x00,0x0B,0x00,0x00,0x07,0x00,0x00,0x14,0x00,0x00,0x28,0x00,0x00,0x3B,0x00,0x00,0x47,0x00,0x00,0x4C,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,
    0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4D,0x00,0x00,0x4C,0x00,0x00,0x47,0x00,0x00,0x3B,0x00,0x00,0x28,0x00,0x00,0x14,0x00,0x00,0x07,0x00,0x00,0x03,0x00,0x00,0x0A,0x00,0x00,0x15,0x00,0x00,0x20,0x00,0x00,0x27,0x00,0x00,0x2B,0x00,0x00,0x2B,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2C,0x00,0x00,0x2B,0x00,0x00,0x2B,0x00,0x00,0x27,0x00,0x00,0x20,0x00,0x00,0x15,0x00,0x00,0x0A,0x00,0x00,0x03,0x00,0x00,0x01,0x00,0x00,0x03,0x00,0x00,0x07,0x00,0x00,0x0C,0x00,0x00,0x0F,0x00,0x00,0x10,0x00,0x00,0x11,
    0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x11,0x00,0x00,0x10,0x00,0x00,0x0F,0x00,0x00,0x0C,0x00,0x00,0x07,0x00,0x00,0x03,0x00,0x00,0x01,
};
const lv_img_dsc_t ui_img_mail_icon_png = {
    .header.always_zero = 0,
    .header.w = 33,
    .header.h = 26,
    .data_size = sizeof(ui_img_mail_icon_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_mail_icon_png_data
};


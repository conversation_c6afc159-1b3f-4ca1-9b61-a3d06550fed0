/*******************************************************************************
 * Size: 36 px
 * Bpp: 4
 * Opts: --bpp 4 --size 36 --font D:/Fusiontech/GUI/Coffee/assets/NotoSansTC-Regular.ttf -o D:/Fusiontech/GUI/Coffee/assets\ui_font_NS_36.c --format lvgl -r 0x20-0x7f --symbols 主選單沖茶咖啡設定除鈣語言聯絡客服熱水清潔蒸氣 --no-compress --no-prefilter
 ******************************************************************************/

#include "ui.h"

#ifndef UI_FONT_NS_36
#define UI_FONT_NS_36 1
#endif

#if UI_FONT_NS_36

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xd, 0xff, 0x70, 0xd, 0xff, 0x60, 0xc, 0xff,
    0x60, 0xc, 0xff, 0x60, 0xc, 0xff, 0x50, 0xb,
    0xff, 0x50, 0xb, 0xff, 0x40, 0xa, 0xff, 0x40,
    0xa, 0xff, 0x30, 0x9, 0xff, 0x30, 0x9, 0xff,
    0x30, 0x8, 0xff, 0x20, 0x8, 0xff, 0x20, 0x7,
    0xff, 0x10, 0x7, 0xff, 0x10, 0x6, 0xff, 0x0,
    0x6, 0xff, 0x0, 0x5, 0xff, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfe, 0x40, 0x6f, 0xff, 0xf0,
    0x8f, 0xff, 0xf2, 0x6f, 0xff, 0xf0, 0x9, 0xfe,
    0x40,

    /* U+0022 "\"" */
    0x45, 0x53, 0x0, 0x3, 0x55, 0x4b, 0xff, 0xa0,
    0x0, 0xaf, 0xfc, 0xbf, 0xfa, 0x0, 0xa, 0xff,
    0xcb, 0xff, 0xa0, 0x0, 0xaf, 0xfb, 0xaf, 0xf9,
    0x0, 0x9, 0xff, 0xb9, 0xff, 0x80, 0x0, 0x8f,
    0xf9, 0x7f, 0xf6, 0x0, 0x6, 0xff, 0x76, 0xff,
    0x50, 0x0, 0x4f, 0xf6, 0x4f, 0xf3, 0x0, 0x3,
    0xff, 0x43, 0xff, 0x20, 0x0, 0x1f, 0xf2, 0x1f,
    0xf1, 0x0, 0x0, 0xff, 0x10,

    /* U+0023 "#" */
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0xe,
    0xf4, 0x0, 0x0, 0x0, 0x7, 0xfa, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0xe,
    0xf3, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x8f, 0x90, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x3, 0x44, 0x8f, 0xc4, 0x44, 0x44, 0xef,
    0x74, 0x43, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x24,
    0x45, 0xff, 0x54, 0x44, 0x4a, 0xfb, 0x44, 0x40,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x7, 0xfb, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0xd, 0xf5,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0xbf, 0x70, 0x0,
    0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x8, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xdf, 0xff, 0xc7, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x7f, 0xff, 0xfc, 0xcd, 0xff, 0xff, 0x70,
    0x2, 0xff, 0xfb, 0x10, 0x0, 0x19, 0xff, 0x90,
    0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x49, 0x0,
    0xc, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xff, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf8,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0xe, 0xa1, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf2,
    0xaf, 0xfe, 0x71, 0x0, 0x0, 0x5e, 0xff, 0xb0,
    0x3e, 0xff, 0xff, 0xdc, 0xce, 0xff, 0xfe, 0x10,
    0x1, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0,
    0x0, 0x1, 0x6b, 0xef, 0xfe, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x4, 0xbe, 0xfe, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xb2, 0x3, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xc0, 0x0, 0x1, 0xef, 0xb0, 0x0, 0x0, 0x0,
    0x3f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x5, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x0, 0x0,
    0x0, 0x2f, 0xf6, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x2, 0xff, 0x50, 0x0, 0x1f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0x4f, 0xf3, 0x0, 0x9, 0xfa, 0x0, 0x8, 0xdf,
    0xfb, 0x40, 0x0, 0x3f, 0xf5, 0x0, 0x0, 0x8,
    0xff, 0x0, 0x2, 0xff, 0x10, 0x1d, 0xff, 0xff,
    0xff, 0x70, 0x0, 0xdf, 0xc0, 0x0, 0x1, 0xef,
    0xa0, 0x0, 0xbf, 0x80, 0xb, 0xff, 0x50, 0x2a,
    0xff, 0x40, 0x5, 0xff, 0xb2, 0x3, 0xdf, 0xf2,
    0x0, 0x4f, 0xe0, 0x4, 0xff, 0x50, 0x0, 0xc,
    0xfd, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0xd, 0xf6, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x4f,
    0xf3, 0x0, 0x4, 0xbf, 0xfe, 0xa2, 0x0, 0x6,
    0xfd, 0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x1, 0xff, 0x50, 0x0, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0xbf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x3, 0xff, 0x40, 0x0, 0x0, 0xb, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x1,
    0xff, 0x60, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0xe,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x50, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x50, 0x2a, 0xff, 0x40, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xdf,
    0xfb, 0x40, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x29, 0xdf, 0xfc, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x95, 0x6d, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x60, 0x0,
    0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0xbf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0, 0xa,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xa0, 0x0, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x8f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1,
    0x0, 0x8f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x61, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0x0, 0xbf,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf1, 0x0, 0xcf, 0xfe, 0x2c, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x9f, 0xfe, 0x10,
    0x1e, 0xff, 0x80, 0x0, 0x0, 0xd, 0xff, 0x40,
    0x2f, 0xff, 0x30, 0x0, 0x3f, 0xff, 0x70, 0x0,
    0x5, 0xff, 0xc0, 0x8, 0xff, 0xb0, 0x0, 0x0,
    0x5f, 0xff, 0x70, 0x0, 0xdf, 0xf4, 0x0, 0xaf,
    0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x90, 0x9f,
    0xfa, 0x0, 0xa, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xdf, 0xfe, 0x10, 0x0, 0x8f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x50,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xf9, 0x10, 0x0, 0xc, 0xff, 0xf8,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0x81,
    0x0, 0x2e, 0xff, 0xff, 0xba, 0xbe, 0xff, 0xff,
    0x77, 0xff, 0xff, 0xf9, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20, 0x1, 0x9f, 0xff, 0x80,
    0x0, 0x4, 0x9d, 0xff, 0xfc, 0x93, 0x0, 0x0,
    0x0, 0x18, 0xd2,

    /* U+0027 "'" */
    0x45, 0x53, 0xbf, 0xfa, 0xbf, 0xfa, 0xbf, 0xfa,
    0xaf, 0xf9, 0x9f, 0xf8, 0x7f, 0xf6, 0x6f, 0xf5,
    0x4f, 0xf3, 0x3f, 0xf2, 0x1f, 0xf1,

    /* U+0028 "(" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0xe, 0xe5,
    0x0, 0x0, 0x7f, 0xf1, 0x0, 0x1, 0xff, 0x80,
    0x0, 0x7, 0xff, 0x10, 0x0, 0xe, 0xfa, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x0, 0xbf, 0xd0, 0x0,
    0x1, 0xff, 0x80, 0x0, 0x6, 0xff, 0x40, 0x0,
    0xa, 0xff, 0x0, 0x0, 0xe, 0xfc, 0x0, 0x0,
    0x2f, 0xf9, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0,
    0x7f, 0xf5, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0x0,
    0x9f, 0xf2, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0xaf, 0xf0, 0x0, 0x0, 0xaf, 0xf0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,
    0x8f, 0xf3, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0x3f, 0xf7, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0xd, 0xfd, 0x0, 0x0, 0x9, 0xff, 0x10, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x0, 0xff, 0xa0, 0x0,
    0x0, 0x9f, 0xf0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0xc, 0xfc, 0x0, 0x0, 0x5, 0xff, 0x30,
    0x0, 0x0, 0xdf, 0xb0, 0x0, 0x0, 0x4f, 0xf4,
    0x0, 0x0, 0xb, 0xa2, 0x0, 0x0, 0x0, 0x0,

    /* U+0029 ")" */
    0x0, 0x20, 0x0, 0x0, 0x3e, 0xf2, 0x0, 0x0,
    0xe, 0xfa, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0x7f, 0xf2, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xb, 0xfe, 0x0,
    0x0, 0x5, 0xff, 0x40, 0x0, 0x1, 0xff, 0x90,
    0x0, 0x0, 0xdf, 0xd0, 0x0, 0x0, 0x9f, 0xf1,
    0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0xf, 0xfb,
    0x0, 0x0, 0xe, 0xfc, 0x0, 0x0, 0xd, 0xfd,
    0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0xd, 0xfd,
    0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0, 0x7f, 0xf3,
    0x0, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0xef, 0xc0,
    0x0, 0x2, 0xff, 0x70, 0x0, 0x7, 0xff, 0x20,
    0x0, 0xc, 0xfc, 0x0, 0x0, 0x2f, 0xf6, 0x0,
    0x0, 0x9f, 0xe0, 0x0, 0x1, 0xff, 0x80, 0x0,
    0x8, 0xff, 0x10, 0x0, 0x2f, 0xf7, 0x0, 0x0,
    0x19, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x1, 0x84, 0x0, 0xdf, 0xa0,
    0x25, 0x90, 0x7f, 0xff, 0xcf, 0xfe, 0xef, 0xff,
    0x32, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0x81, 0x0,
    0x17, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xc, 0xff, 0x9f,
    0xf8, 0x0, 0x0, 0x6, 0xff, 0x60, 0x9f, 0xf2,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0xbf, 0x90, 0x0,
    0x1, 0x70, 0x0, 0x1, 0x70, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x47, 0x77, 0x77, 0x79, 0xff, 0x97, 0x77, 0x77,
    0x74, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0x18, 0x93, 0x0, 0xe, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xb0, 0x2f, 0xff, 0xfe, 0x0, 0x6e,
    0xff, 0xf0, 0x0, 0x8, 0xfd, 0x0, 0x0, 0xcf,
    0xa0, 0x0, 0x4f, 0xf4, 0x0, 0x3e, 0xfb, 0x0,
    0x8f, 0xfd, 0x10, 0xc, 0xfb, 0x10, 0x0, 0x55,
    0x0, 0x0,

    /* U+002D "-" */
    0x28, 0x88, 0x88, 0x88, 0x87, 0x5f, 0xff, 0xff,
    0xff, 0xfe, 0x5f, 0xff, 0xff, 0xff, 0xfe,

    /* U+002E "." */
    0x6, 0xee, 0x60, 0x2f, 0xff, 0xf2, 0x5f, 0xff,
    0xf5, 0x2f, 0xff, 0xf2, 0x6, 0xee, 0x60,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x2, 0x9d, 0xff, 0xd9, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfe, 0xaa, 0xef,
    0xff, 0x70, 0x0, 0x0, 0x2f, 0xff, 0x90, 0x0,
    0x8, 0xff, 0xf3, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0xaf, 0xfb, 0x0, 0x2, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x7, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0xb,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0,
    0xe, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xe0, 0xf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf2, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf3, 0x3f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf3, 0x3f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0x2f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf2, 0x1f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0xf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf0, 0xd, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xe0, 0xa, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xa0, 0x6, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x60, 0x1, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x10, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xf2, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xbb, 0xef, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9d, 0xff, 0xd9, 0x20,
    0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x5, 0xdf, 0xf5, 0x0, 0x0, 0x0,
    0x26, 0xaf, 0xff, 0xff, 0x50, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x9e,
    0xee, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf5, 0x0, 0x0, 0x9, 0xbb, 0xbb, 0xbf,
    0xff, 0xdb, 0xbb, 0xb7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xad, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa,

    /* U+0032 "2" */
    0x0, 0x0, 0x4a, 0xdf, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xda, 0xbe, 0xff,
    0xff, 0x40, 0x0, 0x4f, 0xff, 0xb2, 0x0, 0x0,
    0x4e, 0xff, 0xe0, 0x0, 0xc, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xfe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa1,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3,

    /* U+0033 "3" */
    0x0, 0x0, 0x39, 0xdf, 0xff, 0xd9, 0x30, 0x0,
    0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xb1,
    0x0, 0x5, 0xff, 0xff, 0xdb, 0xac, 0xff, 0xff,
    0xd0, 0x0, 0x8f, 0xfa, 0x30, 0x0, 0x1, 0xaf,
    0xff, 0x80, 0x0, 0x84, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x4,
    0x89, 0xcf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x7c, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfe, 0x6, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xb4, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf5, 0xbf, 0xff, 0xa3, 0x0, 0x0,
    0x19, 0xff, 0xfc, 0x0, 0xbf, 0xff, 0xfe, 0xcb,
    0xdf, 0xff, 0xfd, 0x10, 0x0, 0x6e, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x6, 0xad,
    0xff, 0xfc, 0x93, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfb, 0x9f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0x2a, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x90, 0xbf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xe1,
    0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xf4, 0x0, 0xcf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf9, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x0, 0x0, 0xcf, 0xf5, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0x40, 0x0, 0xc, 0xff,
    0x50, 0x0, 0x0, 0xa, 0xff, 0x80, 0x0, 0x0,
    0xcf, 0xf5, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0,
    0x0, 0xc, 0xff, 0x50, 0x0, 0x1, 0xef, 0xf3,
    0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0, 0x0, 0xaf,
    0xf7, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x2a, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xef, 0xfc, 0xaa, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,

    /* U+0035 "5" */
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xfc, 0xcc,
    0xcc, 0xcc, 0xcc, 0x90, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x41,
    0x57, 0x76, 0x20, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfb, 0xff, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xaf, 0xfa, 0x30, 0x2, 0x6e, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xc0, 0x0, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0,
    0x4f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x10, 0xc, 0xff, 0xf8, 0x20, 0x0, 0x4, 0xdf,
    0xff, 0x60, 0x0, 0x1c, 0xff, 0xff, 0xec, 0xce,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x6a, 0xdf, 0xfe, 0xb6, 0x0, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x6b, 0xef, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xeb, 0xbe, 0xff,
    0xfa, 0x0, 0x5, 0xff, 0xfb, 0x30, 0x0, 0x4,
    0xce, 0x20, 0x1, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x60, 0x6,
    0xcf, 0xfe, 0xa4, 0x0, 0x0, 0xdf, 0xf4, 0x3e,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0xe, 0xff, 0x7f,
    0xfe, 0x97, 0x8c, 0xff, 0xfb, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x6, 0xff, 0xf6, 0xf, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xd0, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x2e,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf4,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x6a, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf6, 0x7f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x62, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf3, 0xc, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x0, 0x5f, 0xfe, 0x10, 0x0, 0x0,
    0xd, 0xff, 0x90, 0x0, 0xbf, 0xfd, 0x30, 0x0,
    0x1b, 0xff, 0xe1, 0x0, 0x1, 0xdf, 0xff, 0xc9,
    0xae, 0xff, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xef, 0xfc, 0x71, 0x0, 0x0,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x3c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xce, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0xb, 0xff, 0xfb, 0x77, 0xaf,
    0xff, 0xe1, 0x0, 0x0, 0x6f, 0xff, 0x40, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0xcf, 0xf6, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x10, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x40, 0x1, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x50,
    0x0, 0xbf, 0xf4, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0, 0xe,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xd3, 0x0, 0x0,
    0x7f, 0xe1, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xa2,
    0x3, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xc7, 0xdf, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x6f,
    0xfa, 0x0, 0x3, 0xaf, 0xff, 0xe3, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0x20,
    0xb, 0xff, 0x20, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xa0, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf1, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf4, 0x5f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf5, 0x3f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf3, 0xe, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xe0, 0x6, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x0,
    0x9f, 0xff, 0xea, 0x77, 0x9d, 0xff, 0xfa, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x16, 0xbe, 0xff, 0xec, 0x71,
    0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x18, 0xdf, 0xfe, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xe9, 0x9b, 0xff,
    0xfe, 0x20, 0x0, 0x3, 0xff, 0xf8, 0x0, 0x0,
    0x1c, 0xff, 0xd0, 0x0, 0xc, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0x0, 0x1f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0xfe, 0x0, 0x5f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x40, 0x6f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x6f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xc0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xe0, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xb, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf0, 0x3, 0xff, 0xfd, 0x51,
    0x2, 0x7e, 0xfc, 0xff, 0xf0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xa2, 0xff, 0xf0, 0x0, 0x4,
    0xcf, 0xff, 0xff, 0xd6, 0x2, 0xff, 0xe0, 0x0,
    0x0, 0x2, 0x56, 0x63, 0x0, 0x4, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0, 0x53, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xe0, 0x0, 0x3, 0xff,
    0x81, 0x0, 0x2, 0xbf, 0xff, 0x40, 0x0, 0x9,
    0xff, 0xff, 0xdc, 0xdf, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0x0, 0x0, 0x1, 0x8c, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x0,

    /* U+003A ":" */
    0x6, 0xee, 0x60, 0x2f, 0xff, 0xf2, 0x5f, 0xff,
    0xf5, 0x2f, 0xff, 0xf2, 0x7, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xee, 0x60,
    0x2f, 0xff, 0xf2, 0x5f, 0xff, 0xf5, 0x2f, 0xff,
    0xf2, 0x6, 0xee, 0x60,

    /* U+003B ";" */
    0x0, 0x6e, 0xe6, 0x0, 0x2f, 0xff, 0xf2, 0x5,
    0xff, 0xff, 0x50, 0x2f, 0xff, 0xf2, 0x0, 0x7f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x89, 0x30,
    0x0, 0xef, 0xff, 0x40, 0x4f, 0xff, 0xfb, 0x2,
    0xff, 0xff, 0xe0, 0x6, 0xef, 0xff, 0x0, 0x0,
    0x8f, 0xd0, 0x0, 0xc, 0xfa, 0x0, 0x4, 0xff,
    0x40, 0x3, 0xef, 0xb0, 0x8, 0xff, 0xd1, 0x0,
    0xcf, 0xb1, 0x0, 0x5, 0x50, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x9f, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x16,
    0xdf, 0xff, 0xff, 0x93, 0x0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xfb, 0x50, 0x0, 0x0, 0x1, 0x7d,
    0xff, 0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xe9, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xcf, 0xff, 0xfd, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9f, 0xff, 0xff,
    0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xff, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xef, 0xff, 0xfe, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8e, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x46,

    /* U+003D "=" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x47, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x74, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa,

    /* U+003E ">" */
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xae, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0xff, 0xff, 0xf9, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xff,
    0xfd, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbf, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x7d, 0xff, 0xff, 0xd7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x17, 0xdf,
    0xff, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x5b, 0xff,
    0xff, 0xf9, 0x30, 0x0, 0x0, 0x4, 0xaf, 0xff,
    0xff, 0xc6, 0x0, 0x0, 0x0, 0x28, 0xef, 0xff,
    0xfe, 0x82, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xe8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x38, 0xab, 0xa7, 0x20, 0x0, 0x0,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x3, 0xff,
    0xfa, 0x30, 0x3, 0xbf, 0xff, 0x70, 0x7, 0xf4,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x69, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xe6, 0x0,
    0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x6a, 0xdf,
    0xff, 0xec, 0x95, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xd9, 0x54, 0x33, 0x58,
    0xcf, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x9f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf3, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xfd, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x70, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4, 0x3,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xfd,
    0x2a, 0xf8, 0x0, 0x0, 0xd, 0xf9, 0xa, 0xff,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xde,
    0xf5, 0x0, 0x0, 0x9, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0xaf, 0xfb, 0x30, 0x1a, 0xff, 0xf2,
    0x0, 0x0, 0x7, 0xfe, 0x5f, 0xf2, 0x0, 0x0,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0x8f, 0xe0, 0x0, 0x0, 0xd,
    0xfd, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x4, 0xff, 0xbf, 0xb0, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0xff, 0x90, 0x0, 0x0, 0x5,
    0xff, 0xdf, 0x80, 0x0, 0x0, 0xaf, 0xf0, 0x0,
    0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x7, 0xfd,
    0xff, 0x70, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0x0,
    0x4, 0xff, 0x20, 0x0, 0x0, 0xa, 0xfa, 0xff,
    0x60, 0x0, 0x0, 0xff, 0xa0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xf6, 0xff, 0x70,
    0x0, 0x0, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xfd,
    0x0, 0x0, 0x0, 0x6f, 0xf1, 0xef, 0x80, 0x0,
    0x0, 0xef, 0xc0, 0x0, 0x0, 0x1e, 0xfc, 0x0,
    0x0, 0x1, 0xef, 0x90, 0xcf, 0xa0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x1, 0xdf, 0xff, 0x10, 0x0,
    0x1d, 0xfe, 0x10, 0xaf, 0xe0, 0x0, 0x0, 0x4f,
    0xfe, 0x74, 0x7e, 0xf9, 0xff, 0xc5, 0x47, 0xef,
    0xf3, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x60, 0xaf, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x5c, 0xfe,
    0xa2, 0x0, 0x7, 0xdf, 0xfc, 0x60, 0x0, 0x0,
    0xa, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x1, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfe,
    0x84, 0x20, 0x0, 0x36, 0xbf, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xcf, 0xff,
    0xff, 0xfd, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x32,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xef, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x8d, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x48, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x3, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0, 0x5f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x70, 0x0, 0xb, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0x6,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfd,
    0x0, 0x0, 0x1, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf3, 0x0, 0x0,
    0x0, 0x6f, 0xfc, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfa, 0xaa, 0xaa, 0xaa, 0xbf, 0xff, 0x20, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x4f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf2,
    0x0, 0x0, 0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf8, 0x0, 0x0, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x5,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x30, 0xa, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x80, 0xf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf3, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf9,

    /* U+0042 "B" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x83, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0x5f, 0xff, 0xaa, 0xaa,
    0xab, 0xdf, 0xff, 0xff, 0x30, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x1, 0x7f, 0xff, 0xe0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf4, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf7, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf8, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf6, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf2, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xb0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x1, 0x37, 0xef, 0xfd, 0x10, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x5f, 0xff, 0x99, 0x99, 0x99, 0xbd,
    0xff, 0xff, 0xb1, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0xfc, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x70,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xc0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xc0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x60, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xfc, 0x0, 0x5f, 0xff,
    0xaa, 0xaa, 0xab, 0xcf, 0xff, 0xff, 0xd1, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xed,
    0xa6, 0x10, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x3, 0x9c, 0xef, 0xfd, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0x50, 0x0, 0x9,
    0xff, 0xff, 0x93, 0x0, 0x1, 0x6d, 0xff, 0xb0,
    0x0, 0x6f, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x9d, 0x0, 0x1, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x20, 0x0, 0x7f, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xd1, 0x0, 0xa, 0xff, 0xff,
    0x83, 0x0, 0x1, 0x6d, 0xff, 0xf6, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x5, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9d, 0xff,
    0xfd, 0x94, 0x0, 0x0,

    /* U+0044 "D" */
    0x5f, 0xff, 0xff, 0xff, 0xfd, 0xb7, 0x20, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x5f, 0xff, 0xbb, 0xbb,
    0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x39, 0xff, 0xff, 0x90, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xf6, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0x10, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x80, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf2, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf5, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfa, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfa, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf5, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x70,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xfe, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf5, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x4a, 0xff, 0xff, 0x80, 0x0, 0x5f, 0xff,
    0xbb, 0xbc, 0xdf, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x30,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xfe, 0xb7,
    0x20, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x5f, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x5, 0xff,
    0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc0, 0x0, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xd2, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x35, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3,

    /* U+0046 "F" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x5f, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xca,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x2, 0x7b, 0xef, 0xfe, 0xc8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x7f, 0xff, 0xfb, 0x41, 0x0, 0x2, 0x8f,
    0xff, 0x70, 0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0x80, 0x0, 0x1f, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf8,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x2e, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xf2, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x6, 0xcc, 0xcc, 0xcf, 0xff, 0x2c, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2,
    0x9f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x25, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf2, 0x1f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x20,
    0xaf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf2, 0x2, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x20, 0x7, 0xff, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0x0,
    0xa, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x28, 0xef,
    0xff, 0x20, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x4, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xce, 0xff, 0xec, 0x83, 0x0,
    0x0,

    /* U+0048 "H" */
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x5f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xff, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf9,

    /* U+0049 "I" */
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff, 0x5f, 0xff,
    0x5f, 0xff, 0x5f, 0xff,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfb, 0x1, 0x80,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x84, 0xef, 0x90,
    0x0, 0x0, 0x6, 0xff, 0xf3, 0x5f, 0xff, 0xb3,
    0x0, 0x17, 0xff, 0xfd, 0x0, 0x8f, 0xff, 0xff,
    0xef, 0xff, 0xff, 0x30, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x18, 0xcf, 0xff,
    0xc8, 0x10, 0x0,

    /* U+004B "K" */
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x60, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x90, 0x0, 0x5f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xb0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xe2, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x9, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x4, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x2,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x1, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0xcf, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0xaf, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x8f, 0xff, 0xcf, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x90, 0xdf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xb0, 0x5,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xd0, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xe2, 0x0, 0x0, 0x2f, 0xff, 0x80,
    0x0, 0x0, 0x5, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x20, 0x0, 0x0, 0x5f, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfa, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x70, 0x0, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x10, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfa, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xc0,

    /* U+004C "L" */
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd6,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+004D "M" */
    0x5f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x95, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9, 0x5f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x95, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x5f, 0xfb,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xff, 0x95, 0xff, 0x7f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x6f, 0xf9, 0x5f, 0xf6, 0xbf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd4, 0xff,
    0x95, 0xff, 0x76, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x2f, 0xf8, 0x5f, 0xf9, 0x5f, 0xf8, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x26, 0xff, 0x95,
    0xff, 0x90, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0xdf,
    0xc0, 0x7f, 0xf9, 0x5f, 0xf9, 0x5, 0xff, 0x70,
    0x0, 0x0, 0x3f, 0xf6, 0x7, 0xff, 0x95, 0xff,
    0xa0, 0xe, 0xfc, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x8f, 0xf9, 0x5f, 0xfa, 0x0, 0x9f, 0xf2, 0x0,
    0x0, 0xef, 0xb0, 0x8, 0xff, 0x95, 0xff, 0xa0,
    0x3, 0xff, 0x80, 0x0, 0x4f, 0xf5, 0x0, 0x8f,
    0xf9, 0x5f, 0xfa, 0x0, 0xd, 0xfe, 0x0, 0xa,
    0xfe, 0x0, 0x8, 0xff, 0x95, 0xff, 0xa0, 0x0,
    0x7f, 0xf4, 0x0, 0xff, 0x90, 0x0, 0x8f, 0xf9,
    0x5f, 0xfa, 0x0, 0x1, 0xff, 0x90, 0x4f, 0xf3,
    0x0, 0x8, 0xff, 0x95, 0xff, 0xa0, 0x0, 0xb,
    0xfe, 0xa, 0xfd, 0x0, 0x0, 0x8f, 0xf9, 0x5f,
    0xfa, 0x0, 0x0, 0x5f, 0xf5, 0xff, 0x70, 0x0,
    0x8, 0xff, 0x95, 0xff, 0xa0, 0x0, 0x0, 0xff,
    0xef, 0xf2, 0x0, 0x0, 0x8f, 0xf9, 0x5f, 0xfa,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x8,
    0xff, 0x95, 0xff, 0xa0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x8f, 0xf9, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0, 0x8, 0xff,
    0x95, 0xff, 0xa0, 0x0, 0x0, 0x3, 0x64, 0x0,
    0x0, 0x0, 0x8f, 0xf9, 0x5f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x95,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf9,

    /* U+004E "N" */
    0x5f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf6, 0x5f, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0x5f, 0xfc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf6, 0x5f, 0xf7, 0xdf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xf8, 0x4f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xf9,
    0xc, 0xff, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0x5f, 0xfa, 0x3, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0xcf, 0xf6, 0x5f, 0xfb, 0x0, 0xaf, 0xf9, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xfb, 0x0, 0x2f,
    0xff, 0x30, 0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xfc,
    0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0xcf, 0xf6,
    0x5f, 0xfc, 0x0, 0x0, 0xef, 0xf6, 0x0, 0x0,
    0xcf, 0xf6, 0x5f, 0xfc, 0x0, 0x0, 0x5f, 0xfe,
    0x0, 0x0, 0xcf, 0xf6, 0x5f, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0x80, 0x0, 0xcf, 0xf6, 0x5f, 0xfc,
    0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0xbf, 0xf6,
    0x5f, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xfb, 0x0,
    0xbf, 0xf6, 0x5f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x40, 0xaf, 0xf6, 0x5f, 0xfc, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xc0, 0x9f, 0xf6, 0x5f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0x7f, 0xf6,
    0x5f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfd,
    0x6f, 0xf6, 0x5f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xbf, 0xf6, 0x5f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf6, 0x5f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf6,
    0x5f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf6, 0x5f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf6,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xfd, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xe7, 0x20,
    0x0, 0x39, 0xff, 0xff, 0x80, 0x0, 0x0, 0x9f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff,
    0x40, 0x0, 0x3f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfe, 0x0, 0xb, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf6,
    0x1, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xc0, 0x6f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x19,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf5, 0xcf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x7d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf9, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x9e, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf9, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x8b, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x9f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x45, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf1, 0xf,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfb, 0x0, 0xaf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x50, 0x2, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xd0, 0x0, 0x8, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xf3, 0x0, 0x0, 0xb, 0xff,
    0xfe, 0x72, 0x0, 0x3, 0x9f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xff, 0xc9, 0x40,
    0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xdb, 0x83, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x5f, 0xff, 0xbb, 0xbb, 0xbc,
    0xef, 0xff, 0xff, 0x40, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x28, 0xff, 0xff, 0x10, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf8, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf9,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0x30, 0x5f, 0xff, 0x0, 0x0, 0x1, 0x38,
    0xef, 0xff, 0x80, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x5, 0xff,
    0xfb, 0xbb, 0xbb, 0xa8, 0x62, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xfd, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfe,
    0x72, 0x0, 0x3, 0x9f, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0x40, 0x0, 0x3, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xe0, 0x0,
    0xb, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf6, 0x0, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0,
    0x6f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x10, 0x9f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x50,
    0xcf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x70, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x90,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x90, 0xef, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x90,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x80, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x70,
    0x9f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x40, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x10,
    0xf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfb, 0x0, 0xa, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0,
    0x2, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xd0, 0x0, 0x0, 0x8f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0x30, 0x0,
    0x0, 0xb, 0xff, 0xfd, 0x50, 0x0, 0x1, 0x7e,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xdc, 0xdf, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xae, 0xff, 0xfd, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfd,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xdc, 0xce, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xfd, 0x91,

    /* U+0052 "R" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xec, 0xa5, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x60, 0x0, 0x5f, 0xff, 0xbb, 0xbb, 0xbb,
    0xdf, 0xff, 0xff, 0x80, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x16, 0xef, 0xff, 0x40, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xfb, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x15, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf1, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf6,
    0x5, 0xff, 0xf0, 0x0, 0x0, 0x2, 0x6d, 0xff,
    0xfc, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe6, 0x0, 0x0, 0x5f, 0xff, 0xaa,
    0xaa, 0xbf, 0xff, 0x70, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x4, 0xff, 0xf6, 0x0,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xff,
    0xe0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x80, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x20, 0x0, 0x5f, 0xff,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xfb, 0x0, 0x5,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xd0, 0x5, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x60, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfe, 0x15, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf9,

    /* U+0053 "S" */
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfe, 0xc8, 0x20,
    0x0, 0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xf4, 0x0, 0x2, 0xff, 0xfe,
    0x71, 0x0, 0x2, 0x8e, 0xff, 0xb0, 0x0, 0xaf,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x9, 0xd1, 0x0,
    0xf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfe, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff,
    0xfb, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x0, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xb0,
    0xaf, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf5, 0x1d, 0xff, 0xff, 0x93, 0x0, 0x0, 0x4a,
    0xff, 0xfc, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xee,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x49, 0xce, 0xff, 0xec, 0x82, 0x0, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xbc, 0xcc, 0xcc, 0xcc,
    0xff, 0xfe, 0xcc, 0xcc, 0xcc, 0xc5, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7, 0x7f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x6f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf6, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0x2f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0xe, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x9, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xa0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x30, 0x0, 0x9f, 0xff, 0xd6,
    0x10, 0x1, 0x5d, 0xff, 0xfa, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfe, 0xef, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe,
    0xc8, 0x20, 0x0, 0x0,

    /* U+0056 "V" */
    0xcf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x87, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf3, 0x2f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x0,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x90, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0,
    0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x90, 0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf4, 0x0, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x0,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xdf, 0xf5, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf5, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfe, 0x0,
    0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf3, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x80, 0x0, 0xaf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfd, 0x0,
    0xe, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x60, 0x8f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0xc,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf2, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xcf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0,

    /* U+0057 "W" */
    0xf, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x80,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0x8,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xf, 0xff, 0x10, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x1, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0xc, 0xfd, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x7f, 0xfb, 0x0, 0xe, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xff, 0x7e, 0xfd, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x70, 0x0, 0xaf, 0xfa,
    0x0, 0x0, 0x0, 0x4f, 0xf4, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0xdf, 0xf4, 0x0, 0x7, 0xff, 0xd0,
    0x0, 0x0, 0x9, 0xff, 0x7, 0xff, 0x50, 0x0,
    0x0, 0xf, 0xff, 0x10, 0x0, 0x3f, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xc0, 0x3f, 0xfa, 0x0, 0x0,
    0x3, 0xff, 0xd0, 0x0, 0x0, 0xff, 0xf3, 0x0,
    0x0, 0x1f, 0xf9, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x7f, 0xfa, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0,
    0x5, 0xff, 0x50, 0xc, 0xff, 0x20, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x9f, 0xf1, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0xdf,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x0, 0xd,
    0xfd, 0x0, 0x4, 0xff, 0xa0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x1, 0xff,
    0x90, 0x0, 0xf, 0xfe, 0x0, 0x3, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0xff, 0xf3, 0x0, 0x5f, 0xf5,
    0x0, 0x0, 0xcf, 0xf2, 0x0, 0x6f, 0xf9, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x60, 0x9, 0xff, 0x10,
    0x0, 0x8, 0xff, 0x60, 0x9, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x8f, 0xf9, 0x0, 0xcf, 0xd0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0xcf, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xc0, 0xf, 0xf9, 0x0, 0x0,
    0x0, 0xff, 0xd0, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x3, 0xff, 0x50, 0x0, 0x0,
    0xb, 0xff, 0x11, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf2, 0x7f, 0xf1, 0x0, 0x0, 0x0,
    0x7f, 0xf4, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x5a, 0xfd, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x87, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf8, 0xef, 0x90, 0x0, 0x0, 0x0, 0xf,
    0xfb, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x40, 0x0, 0x0,

    /* U+0058 "X" */
    0x9, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0xdf, 0xfa, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x80, 0x0, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0,
    0x6, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xc0, 0x0, 0x0, 0xef, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x50, 0x0, 0x7f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x0, 0xe,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xf6, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xe1, 0xef, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xdf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xa6, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf3, 0xd, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0,
    0x5f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x3, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0x0,
    0xa, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0,
    0x6f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xfe,
    0x10, 0x0, 0xe, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xf9, 0x0, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xc0,

    /* U+0059 "Y" */
    0xc, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfe, 0x0, 0x4f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x60, 0x0, 0xcf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xe0, 0x0,
    0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf6, 0x0, 0x0, 0xc, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x10, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0xf8, 0x0, 0x0, 0x4, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x60, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfd, 0x0, 0xa, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x2, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xc0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x5f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0,

    /* U+005A "Z" */
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0xbc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xdf, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0,

    /* U+005B "[" */
    0x3f, 0xff, 0xff, 0xff, 0x3f, 0xfe, 0xee, 0xed,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0x2e, 0xee, 0xee, 0xed,

    /* U+005C "\\" */
    0x4f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8,

    /* U+005D "]" */
    0xcf, 0xff, 0xff, 0xf6, 0xbe, 0xee, 0xef, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1f, 0xf6,
    0x0, 0x0, 0x1f, 0xf6, 0xcf, 0xff, 0xff, 0xf6,
    0xae, 0xee, 0xee, 0xe5,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x48, 0x84, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfe, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf3, 0x3f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xd0, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x7, 0xff, 0x30, 0x0,
    0x0, 0x9, 0xff, 0x10, 0x1, 0xff, 0x90, 0x0,
    0x0, 0xf, 0xfb, 0x0, 0x0, 0xbf, 0xf0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x5f, 0xf6, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x9, 0xff, 0x20,
    0x9, 0xff, 0x30, 0x0, 0x0, 0x3, 0xff, 0x90,
    0xe, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5,

    /* U+005F "_" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,

    /* U+0060 "`" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x6f, 0x30, 0x0,
    0x0, 0x6f, 0xfe, 0x10, 0x0, 0x3, 0xef, 0xfd,
    0x0, 0x0, 0x3, 0xef, 0xfb, 0x0, 0x0, 0x2,
    0xef, 0xf9, 0x0, 0x0, 0x2, 0xef, 0xf7, 0x0,
    0x0, 0x2, 0xef, 0xe0, 0x0, 0x0, 0x2, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0061 "a" */
    0x0, 0x0, 0x1, 0x47, 0x77, 0x40, 0x0, 0x0,
    0x0, 0x5, 0xbf, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xc, 0xff, 0xfa, 0x64, 0x36, 0xdf, 0xff, 0x40,
    0x3, 0xf9, 0x10, 0x0, 0x0, 0xd, 0xff, 0xb0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x1, 0x47, 0xac, 0xff, 0xf6,
    0x0, 0x0, 0x28, 0xdf, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x2b, 0xff, 0xff, 0xeb, 0x86, 0xef, 0xf6,
    0x4, 0xff, 0xfe, 0x83, 0x0, 0x0, 0xef, 0xf6,
    0x1f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xf6,
    0x9f, 0xff, 0x40, 0x0, 0x5, 0xef, 0xff, 0xf6,
    0x2f, 0xff, 0xfd, 0xab, 0xef, 0xfc, 0xaf, 0xf6,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x70, 0x7f, 0xf6,
    0x0, 0x29, 0xdf, 0xfc, 0x81, 0x0, 0x5f, 0xf6,

    /* U+0062 "b" */
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf9, 0x0, 0x2, 0x67, 0x74, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x3, 0xbf, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xbf, 0xf8, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0xbf, 0xff, 0xff, 0xd7, 0x45, 0x8e,
    0xff, 0xf9, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0x20, 0xbf, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x90, 0xbf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xe0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf4,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf5, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf6, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf5, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf1, 0xbf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x70, 0xbf,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0xdf, 0xfe, 0x10,
    0xbf, 0xff, 0xf8, 0x10, 0x0, 0x4d, 0xff, 0xf6,
    0x0, 0xbf, 0xfd, 0xff, 0xfd, 0xce, 0xff, 0xff,
    0x90, 0x0, 0xbf, 0xf2, 0x7f, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xbf, 0xf0, 0x1, 0x8d, 0xff,
    0xd8, 0x10, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x0, 0x1, 0x57, 0x76, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x9f, 0xff, 0xf9, 0x54, 0x6b,
    0xff, 0x60, 0x0, 0x5f, 0xff, 0xc1, 0x0, 0x0,
    0x5, 0x90, 0x0, 0xe, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x51,
    0x0, 0x3, 0xff, 0xff, 0x70, 0x0, 0x2, 0xaf,
    0xa0, 0x0, 0x5, 0xff, 0xff, 0xfc, 0xcd, 0xff,
    0xff, 0x20, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x5a, 0xef, 0xfd,
    0xa4, 0x0, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x47, 0x76,
    0x30, 0x4, 0xff, 0xf0, 0x0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xc3, 0x3f, 0xff, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xf0, 0x0,
    0xb, 0xff, 0xfe, 0x84, 0x47, 0xdf, 0xff, 0xff,
    0x0, 0x6, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x6f,
    0xff, 0xf0, 0x0, 0xef, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x6f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf0, 0xb, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0,
    0xf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf0, 0x1f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0xe, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0,
    0xaf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0x6, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x0, 0x1f, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf0, 0x0, 0x7f, 0xff, 0xd4,
    0x0, 0x1, 0x9f, 0xff, 0xff, 0x0, 0x0, 0xbf,
    0xff, 0xfe, 0xcd, 0xff, 0xf9, 0xff, 0xf0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xf6, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x3a, 0xef, 0xfd, 0x81, 0x0,
    0xbf, 0xf0,

    /* U+0065 "e" */
    0x0, 0x0, 0x0, 0x3, 0x67, 0x75, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xdf, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0xa, 0xff, 0xf9, 0x42,
    0x26, 0xef, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0x40,
    0x0, 0x0, 0x1c, 0xff, 0x60, 0x0, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x6, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1, 0xb,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4,
    0xe, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf6, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x1f, 0xff, 0x65, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x51, 0xf, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0, 0x0, 0x2,
    0xad, 0x0, 0x0, 0x4, 0xff, 0xff, 0xeb, 0xaa,
    0xdf, 0xff, 0x50, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x4a,
    0xdf, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x4, 0xbe, 0xfe, 0xc6, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf8, 0x0, 0x4, 0xff, 0xfe,
    0xaa, 0xe4, 0x0, 0xb, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x78, 0x9f, 0xff, 0x98, 0x88, 0x20,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x26, 0x77, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x7, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x4, 0xff, 0xfa,
    0x20, 0x3, 0xcf, 0xfd, 0x88, 0x83, 0x0, 0xdf,
    0xf9, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0,
    0x3f, 0xff, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0,
    0x0, 0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x6f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0x5, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x1f, 0xff,
    0x30, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0,
    0x8f, 0xfd, 0x10, 0x0, 0x3, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xcf, 0xfe, 0x74, 0x49, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc8, 0xdf,
    0xfd, 0x93, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xdb, 0xbb,
    0xbb, 0xaa, 0x84, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x2c,
    0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x2e, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x27, 0xff,
    0xfa, 0xb, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xc1, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfb, 0x2f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x60, 0xef, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xc0, 0x5,
    0xff, 0xfe, 0x96, 0x44, 0x57, 0xcf, 0xff, 0xc1,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x70, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xff, 0xec,
    0x95, 0x0, 0x0, 0x0,

    /* U+0068 "h" */
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf9, 0x0, 0x1, 0x57, 0x75, 0x10, 0x0,
    0xbf, 0xf9, 0x1, 0x9f, 0xff, 0xff, 0xf7, 0x0,
    0xbf, 0xf8, 0x3e, 0xff, 0xff, 0xff, 0xff, 0x70,
    0xbf, 0xfb, 0xff, 0xf9, 0x66, 0xaf, 0xff, 0xf1,
    0xbf, 0xff, 0xfb, 0x10, 0x0, 0x5, 0xff, 0xf7,
    0xbf, 0xff, 0x90, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,

    /* U+0069 "i" */
    0x7, 0xee, 0x60, 0x1f, 0xff, 0xf1, 0x2f, 0xff,
    0xf1, 0xb, 0xff, 0xa0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff,
    0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb,
    0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0,
    0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff,
    0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb,
    0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0,
    0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xb, 0xff,
    0xa0, 0xb, 0xff, 0xa0,

    /* U+006A "j" */
    0x0, 0x0, 0x7, 0xfe, 0x60, 0x0, 0x0, 0x2f,
    0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff, 0xf1, 0x0,
    0x0, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x33,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0,
    0xa, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0,
    0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0,
    0xa, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0,
    0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0xa, 0xff, 0x90, 0x0, 0x0,
    0xb, 0xff, 0x90, 0x0, 0x0, 0xd, 0xff, 0x70,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x1d, 0xab, 0xff,
    0xfd, 0x0, 0x5f, 0xff, 0xff, 0xf3, 0x0, 0x5d,
    0xff, 0xea, 0x20, 0x0,

    /* U+006B "k" */
    0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf4, 0xb, 0xff, 0x80, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0xbf, 0xf8, 0x0,
    0x0, 0x1, 0xef, 0xf9, 0x0, 0xb, 0xff, 0x80,
    0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0xbf, 0xf8,
    0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0xb, 0xff,
    0x80, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0xbf,
    0xf8, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x1d, 0xff, 0x90, 0x0, 0x0, 0x0,
    0xbf, 0xf8, 0xb, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x87, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0xff, 0xfc, 0xff, 0xa0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf4, 0x2f, 0xff, 0x40,
    0x0, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x8f, 0xfd,
    0x0, 0x0, 0xb, 0xff, 0xf9, 0x0, 0x0, 0xdf,
    0xf7, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0, 0x5,
    0xff, 0xf2, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0xb, 0xff, 0xb0, 0x0, 0xbf, 0xf8, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x50, 0xb, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x8f, 0xfe, 0x10, 0xbf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf9, 0xb, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf3,

    /* U+006C "l" */
    0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa,
    0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf,
    0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0,
    0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa,
    0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf,
    0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0,
    0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa,
    0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf,
    0xfa, 0x0, 0xbf, 0xfa, 0x0, 0xbf, 0xfa, 0x0,
    0xaf, 0xfb, 0x0, 0x7f, 0xff, 0xc3, 0x2f, 0xff,
    0xf7, 0x4, 0xdf, 0xf7,

    /* U+006D "m" */
    0x0, 0x0, 0x0, 0x2, 0x67, 0x63, 0x0, 0x0,
    0x0, 0x2, 0x67, 0x62, 0x0, 0x0, 0xbf, 0xf1,
    0x1, 0xaf, 0xff, 0xff, 0xd2, 0x0, 0x2, 0xbf,
    0xff, 0xff, 0xb1, 0x0, 0xbf, 0xf3, 0x3e, 0xff,
    0xff, 0xff, 0xfe, 0x10, 0x4f, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xbf, 0xf9, 0xff, 0xe8, 0x67, 0xdf,
    0xff, 0x85, 0xff, 0xd8, 0x68, 0xef, 0xff, 0x60,
    0xbf, 0xff, 0xfa, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x1e, 0xff, 0xc0, 0xbf, 0xff,
    0x80, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x7, 0xff, 0xf0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf2, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,

    /* U+006E "n" */
    0x0, 0x0, 0x0, 0x1, 0x57, 0x75, 0x10, 0x0,
    0xbf, 0xf1, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x0,
    0xbf, 0xf3, 0x3d, 0xff, 0xff, 0xff, 0xff, 0x70,
    0xbf, 0xf9, 0xff, 0xf9, 0x66, 0xaf, 0xff, 0xf1,
    0xbf, 0xff, 0xfb, 0x10, 0x0, 0x5, 0xff, 0xf7,
    0xbf, 0xff, 0x90, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,

    /* U+006F "o" */
    0x0, 0x0, 0x0, 0x2, 0x67, 0x75, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xd7, 0x45, 0x8e, 0xff, 0xf8, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x0, 0x0, 0x1b, 0xff, 0xf4, 0x0,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xc0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x30, 0xcf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf8, 0xf, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xc1, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x1f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xe1, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xfe, 0xf, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd0, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x9, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x4f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf1,
    0x0, 0xcf, 0xfe, 0x20, 0x0, 0x0, 0x4, 0xff,
    0xf9, 0x0, 0x2, 0xff, 0xfe, 0x50, 0x0, 0x7,
    0xff, 0xfd, 0x0, 0x0, 0x4, 0xff, 0xff, 0xec,
    0xcf, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x3, 0xcf,
    0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xef, 0xfd, 0x93, 0x0, 0x0, 0x0,

    /* U+0070 "p" */
    0x0, 0x0, 0x0, 0x2, 0x67, 0x74, 0x0, 0x0,
    0x0, 0xbf, 0xf1, 0x3, 0xcf, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xbf, 0xf4, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0xbf, 0xfe, 0xff, 0xd7, 0x45,
    0x8e, 0xff, 0xf9, 0x0, 0xbf, 0xff, 0xf7, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0x30, 0xbf, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe0, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf2,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf4, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf5, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf6, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf5, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf4, 0xbf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x70,
    0xbf, 0xfd, 0x20, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0x10, 0xbf, 0xff, 0xf8, 0x10, 0x0, 0x4d, 0xff,
    0xf6, 0x0, 0xbf, 0xff, 0xff, 0xfd, 0xce, 0xff,
    0xff, 0x90, 0x0, 0xbf, 0xf8, 0x7f, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0xbf, 0xf9, 0x1, 0x8d,
    0xff, 0xd8, 0x10, 0x0, 0x0, 0xbf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x0, 0x4, 0x77, 0x63, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xfd,
    0x40, 0xbf, 0xf0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x9e, 0xff, 0x0, 0x0, 0xbf, 0xff,
    0xe8, 0x44, 0x7d, 0xff, 0xff, 0xf0, 0x0, 0x6f,
    0xff, 0xb1, 0x0, 0x0, 0x6, 0xff, 0xff, 0x0,
    0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf0, 0x6, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x0, 0xbf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf0, 0xe, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x1f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x1, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0xf, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x0, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf0, 0xa, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0x6f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x1, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x4, 0xff,
    0xff, 0x0, 0x7, 0xff, 0xfd, 0x40, 0x0, 0x19,
    0xff, 0xff, 0xf0, 0x0, 0xb, 0xff, 0xff, 0xec,
    0xdf, 0xff, 0xbf, 0xff, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0x62, 0xff, 0xf0, 0x0, 0x0,
    0x3, 0xae, 0xff, 0xd8, 0x10, 0x3f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x0,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x4, 0x77, 0x40, 0xbf, 0xf1,
    0x2, 0xcf, 0xff, 0xe0, 0xbf, 0xf2, 0x2e, 0xff,
    0xff, 0xb0, 0xbf, 0xf4, 0xdf, 0xfa, 0x78, 0x50,
    0xbf, 0xfd, 0xfd, 0x20, 0x0, 0x0, 0xbf, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xbf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x2, 0x67, 0x75, 0x20, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0xff, 0xff, 0xc4, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x2f,
    0xff, 0xb3, 0x11, 0x4a, 0xff, 0x60, 0x8, 0xff,
    0xc0, 0x0, 0x0, 0x3, 0x80, 0x0, 0xbf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xfc,
    0x50, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xe7, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf7,
    0x7, 0x50, 0x0, 0x0, 0x0, 0xd, 0xff, 0x54,
    0xff, 0xa2, 0x0, 0x0, 0x8, 0xff, 0xf1, 0x8f,
    0xff, 0xfc, 0x98, 0xae, 0xff, 0xf6, 0x0, 0x4c,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x3,
    0x9d, 0xff, 0xfc, 0x82, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x48, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x8, 0x89, 0xff, 0xf9, 0x88, 0x88, 0x40, 0x0,
    0x1, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb, 0xae, 0x90,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xeb, 0x70,

    /* U+0075 "u" */
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf9,
    0xaf, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9,
    0x5f, 0xff, 0xb1, 0x0, 0x18, 0xff, 0xcf, 0xf9,
    0xd, 0xff, 0xff, 0xee, 0xff, 0xf7, 0x5f, 0xf9,
    0x3, 0xef, 0xff, 0xff, 0xff, 0x50, 0x4f, 0xf9,
    0x0, 0x19, 0xdf, 0xfd, 0x81, 0x0, 0x3f, 0xf9,

    /* U+0076 "v" */
    0x5f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x10, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xc0, 0xa, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x0, 0x4f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0x9, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xbf, 0xf6,
    0x0, 0x0, 0x3f, 0xff, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x10, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x6, 0xff, 0xb0, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x2f,
    0xfe, 0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf4, 0x0, 0x5, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x0, 0xbf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3,
    0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x80, 0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfd, 0xe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf5, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xef, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x80, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0xe, 0xff, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x9f, 0xf8, 0x5, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xdf, 0xf4, 0x1, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0xff, 0xdf, 0xf1, 0x0, 0x0, 0x1, 0xff, 0xf0,
    0x0, 0xcf, 0xf7, 0x0, 0x0, 0x4, 0xff, 0x5f,
    0xf5, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x8f,
    0xfb, 0x0, 0x0, 0x8, 0xfe, 0xf, 0xf9, 0x0,
    0x0, 0x9, 0xff, 0x70, 0x0, 0x4f, 0xff, 0x0,
    0x0, 0xc, 0xfa, 0xd, 0xfd, 0x0, 0x0, 0xd,
    0xff, 0x30, 0x0, 0xf, 0xff, 0x30, 0x0, 0x1f,
    0xf7, 0x9, 0xff, 0x10, 0x0, 0x1f, 0xff, 0x0,
    0x0, 0xb, 0xff, 0x70, 0x0, 0x5f, 0xf3, 0x5,
    0xff, 0x60, 0x0, 0x5f, 0xfb, 0x0, 0x0, 0x7,
    0xff, 0xb0, 0x0, 0x9f, 0xe0, 0x1, 0xff, 0xa0,
    0x0, 0x9f, 0xf6, 0x0, 0x0, 0x2, 0xff, 0xf0,
    0x0, 0xdf, 0xb0, 0x0, 0xdf, 0xe0, 0x0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0xef, 0xf3, 0x2, 0xff,
    0x60, 0x0, 0x9f, 0xf2, 0x1, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xaf, 0xf6, 0x5, 0xff, 0x20, 0x0,
    0x4f, 0xf6, 0x4, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x5f, 0xfa, 0x9, 0xfe, 0x0, 0x0, 0xf, 0xfa,
    0x8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0xd, 0xfa, 0x0, 0x0, 0xc, 0xfe, 0xb, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xd, 0xff, 0x2f, 0xf6,
    0x0, 0x0, 0x8, 0xff, 0x2e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x8f, 0xf2, 0x0, 0x0,
    0x4, 0xff, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xc0, 0x0, 0x0,

    /* U+0078 "x" */
    0xa, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x90, 0x1, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xfe, 0x10, 0x0, 0x6f, 0xff, 0x20, 0x0, 0x0,
    0xef, 0xf6, 0x0, 0x0, 0xc, 0xff, 0xb0, 0x0,
    0x7, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xf4,
    0x0, 0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f,
    0xfd, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x61, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xea, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xef,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x3b, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xfa, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf1, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x80, 0x0, 0x1e, 0xff, 0x80, 0x0,
    0x0, 0x4f, 0xfe, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0xdf, 0xf6, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x60, 0x2f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf1,

    /* U+0079 "y" */
    0x5f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x10, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xc0, 0x8, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf6, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0xbf,
    0xf8, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0x5, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xbf, 0xf6,
    0x0, 0x0, 0xe, 0xff, 0x40, 0x0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x0,
    0x5, 0xff, 0xb0, 0x0, 0x0, 0x2, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x50, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfb, 0x0, 0x5, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf1, 0x0, 0x9f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x60, 0xe,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf1, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x7c, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xbd,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+007A "z" */
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x6, 0xaa, 0xaa, 0xaa, 0xaa, 0xdf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xda, 0xaa, 0xaa, 0xaa, 0xaa, 0xa0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,

    /* U+007B "{" */
    0x0, 0x0, 0x7, 0xdf, 0xff, 0x0, 0x0, 0xaf,
    0xff, 0xed, 0x0, 0x2, 0xff, 0xd2, 0x0, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0, 0x8,
    0xff, 0x20, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0,
    0x0, 0x6, 0xff, 0x30, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x3, 0xff, 0x50, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x8,
    0xff, 0x30, 0x0, 0x1, 0x7f, 0xfd, 0x0, 0x0,
    0xaf, 0xff, 0xc2, 0x0, 0x0, 0xaf, 0xff, 0xa1,
    0x0, 0x0, 0x13, 0x9f, 0xfc, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x20, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x3,
    0xff, 0x50, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0,
    0x7, 0xff, 0x20, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x0, 0x5, 0xff, 0x70, 0x0,
    0x0, 0x1, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0x0, 0x0, 0x5, 0xbd, 0xed,

    /* U+007C "|" */
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,

    /* U+007D "}" */
    0xcf, 0xfd, 0x81, 0x0, 0x0, 0xbe, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0x50, 0x0, 0x0,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0xff, 0xa0, 0x0,
    0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x1, 0xff,
    0x80, 0x0, 0x0, 0x2, 0xff, 0x70, 0x0, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0xf9, 0x20,
    0x0, 0x0, 0x1b, 0xff, 0xfc, 0x0, 0x0, 0x8,
    0xff, 0xfc, 0x0, 0x0, 0x8f, 0xfb, 0x41, 0x0,
    0x0, 0xef, 0xd0, 0x0, 0x0, 0x1, 0xff, 0x70,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x2,
    0xff, 0x60, 0x0, 0x0, 0x2, 0xff, 0x70, 0x0,
    0x0, 0x2, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xff, 0xb0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0x3d, 0xff, 0x40, 0x0, 0xcf, 0xff, 0xfb,
    0x0, 0x0, 0xae, 0xdb, 0x60, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x7, 0xdf, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x9, 0x70, 0xb, 0xff, 0xa8, 0xcf, 0xff, 0x50,
    0x0, 0x5f, 0xf5, 0x5f, 0xf5, 0x0, 0x6, 0xff,
    0xfb, 0x7a, 0xff, 0xc0, 0x7, 0x90, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfd, 0x70, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x49, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x9e, 0xff, 0xb9, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x79, 0x99, 0x99, 0x99, 0x99,
    0x9e, 0xff, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x95,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x2a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0x90,

    /* U+5496 "咖" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0x76, 0x66, 0x66, 0x50, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfe, 0xee, 0xef, 0xe0, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0xbd, 0xdd, 0xdd, 0xdd, 0xd5,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0xdf, 0xb7, 0x77, 0x7f, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x67, 0x7f, 0xfa,
    0x77, 0xdf, 0xb0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xf, 0xf5,
    0x0, 0xbf, 0xa0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xf, 0xf5,
    0x0, 0xbf, 0xa0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xf, 0xf4,
    0x0, 0xcf, 0x90, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf3,
    0x0, 0xcf, 0x90, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x2f, 0xf2,
    0x0, 0xdf, 0x80, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x4f, 0xf1,
    0x0, 0xdf, 0x70, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x5f, 0xf0,
    0x0, 0xef, 0x70, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x7f, 0xe0,
    0x0, 0xff, 0x60, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0x9f, 0xc0,
    0x0, 0xff, 0x60, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xbf, 0xa0,
    0x0, 0xff, 0x50, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x4f, 0xe0, 0x0, 0xef, 0x80,
    0x1, 0xff, 0x40, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe4, 0x44, 0x7f, 0xe0, 0x1, 0xff, 0x50,
    0x2, 0xff, 0x30, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xff, 0xff, 0xff, 0xe0, 0x4, 0xff, 0x20,
    0x3, 0xff, 0x20, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xff, 0xff, 0xff, 0xe0, 0x9, 0xfe, 0x0,
    0x4, 0xff, 0x10, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x6, 0xff, 0x0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x7, 0xff, 0x0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0,
    0x9, 0xfe, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xf6,
    0x28, 0x70, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0,
    0xb, 0xfc, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0,
    0xf, 0xf9, 0x0, 0xdf, 0xb7, 0x77, 0x7f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa, 0x3, 0x32,
    0x8f, 0xf6, 0x0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xf1, 0x9, 0xff,
    0xff, 0xf1, 0x0, 0xdf, 0x60, 0x0, 0xe, 0xf6,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x5, 0xff,
    0xff, 0x50, 0x0, 0x67, 0x30, 0x0, 0x3, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x33,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5561 "啡" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xa0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfb, 0x0, 0xc, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x22, 0x22, 0x22, 0x20, 0x0,
    0x0, 0x0, 0xef, 0xb0, 0x0, 0xcf, 0xe0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0xe, 0xfb, 0x0, 0xc, 0xfe, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0xb0, 0x0, 0xcf, 0xe0,
    0x0, 0x0, 0x0, 0x5f, 0xf5, 0x55, 0x57, 0xff,
    0x22, 0x22, 0x22, 0x2f, 0xfb, 0x0, 0xc, 0xff,
    0x22, 0x22, 0x22, 0x5, 0xff, 0x0, 0x0, 0x3f,
    0xf2, 0xdf, 0xff, 0xff, 0xff, 0xb0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf5, 0x5f, 0xf0, 0x0, 0x3,
    0xff, 0x2d, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0x55, 0xff, 0x0, 0x0,
    0x3f, 0xf2, 0x45, 0x55, 0x55, 0xff, 0xb0, 0x0,
    0xcf, 0xf5, 0x55, 0x55, 0x51, 0x5f, 0xf0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0xc, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0xf0,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0xe, 0xfb,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0xef,
    0xb0, 0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0x0, 0x3, 0xff, 0x24, 0x77, 0x77, 0x7f,
    0xfb, 0x0, 0xc, 0xff, 0x77, 0x77, 0x77, 0x5,
    0xff, 0x0, 0x0, 0x3f, 0xf2, 0x9f, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf0,
    0x5f, 0xf0, 0x0, 0x3, 0xff, 0x28, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0x5, 0xff, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0xef, 0xb0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0x5f, 0xf0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0xf, 0xfa, 0x0, 0xc, 0xfe, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0xff, 0xa0, 0x0, 0xcf, 0xe0, 0x0,
    0x0, 0x0, 0x5f, 0xf4, 0x44, 0x46, 0xff, 0x20,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0xc, 0xfe, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x16, 0xff, 0x80, 0x0, 0xcf, 0xf1,
    0x11, 0x11, 0x10, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x38, 0xcf, 0xff, 0xf6, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xc5, 0xff, 0x33, 0x33, 0x33,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xfc, 0x5f, 0xf0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xa4, 0xbf, 0xf0, 0x0, 0xc,
    0xff, 0x66, 0x66, 0x66, 0x55, 0xff, 0x0, 0x0,
    0x0, 0x7, 0xb5, 0x0, 0xf, 0xfc, 0x0, 0x0,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x26, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x0,
    0xc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0x60,
    0x0, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+55AE "單" */
    0x0, 0x3d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x80,
    0x6, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd7, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0xef,
    0xa0, 0x7, 0xff, 0x0, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0xe,
    0xfa, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0xff,
    0x80, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0xef, 0xa0, 0x7, 0xff, 0x0, 0x0, 0x0, 0xf,
    0xf8, 0x0, 0x0, 0x4, 0xff, 0xcc, 0xcc, 0xcc,
    0xcf, 0xfa, 0x0, 0x7f, 0xfc, 0xcc, 0xcc, 0xcc,
    0xff, 0x80, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xf5, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x5f, 0xf5, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x61, 0x11, 0x11, 0x11, 0x9f,
    0xf6, 0x11, 0x11, 0x11, 0x12, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x8f, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x5f, 0xf9, 0x55, 0x55, 0x55,
    0x5a, 0xff, 0x85, 0x55, 0x55, 0x55, 0x5f, 0xfa,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x4e, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x67, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0xbf, 0xfa, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x8d, 0xff, 0x0,
    0x0, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x0, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x0, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x0, 0x0, 0xff, 0x90, 0x13, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x31, 0xb, 0xff, 0x0, 0x0, 0xcc, 0x70, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x9, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x77, 0x77, 0x77, 0x7b, 0xff, 0xa7,
    0x77, 0x77, 0x77, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0x74, 0x0, 0x0,
    0x7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x7, 0xff, 0xb9, 0x99, 0x99,
    0x99, 0x99, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x7, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfe, 0x0, 0x0, 0x7,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0x80, 0x0,
    0x7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0xbf, 0xf6,
    0x0, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x1e,
    0xff, 0x70, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa0,
    0x3, 0xff, 0xfc, 0x37, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x10, 0x0, 0x3e, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xf6, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xfe,
    0xcb, 0xa9, 0x99, 0x99, 0x99, 0x99, 0xa4, 0x3e,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x2c, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x58, 0xbd, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x0,

    /* U+5BA2 "客" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x1, 0xff, 0xc7, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x78,
    0xff, 0x90, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0,
    0x7, 0xb8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf9, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0x4, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x1f, 0xf9,
    0x0, 0x0, 0x3, 0xff, 0xf7, 0x22, 0x22, 0x22,
    0x22, 0x23, 0x20, 0x0, 0x2f, 0xf9, 0x0, 0x1,
    0xcc, 0x70, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x1, 0xcc, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xff,
    0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0x9f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf,
    0xff, 0xb1, 0x5f, 0xff, 0x70, 0x0, 0x1, 0xcf,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0x60, 0x0, 0x5f, 0xff, 0xb1, 0x5,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf9, 0x10, 0x0, 0x0, 0x3e, 0xff,
    0xfc, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xff, 0xff, 0xe8, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xcf, 0xff, 0xfa, 0x7e, 0xff, 0xff,
    0xd7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xbf, 0xff, 0xff, 0xa3, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xea, 0x62, 0x0, 0x0, 0x0,
    0x0, 0x48, 0xcf, 0xff, 0xff, 0xe8, 0x10, 0x0,
    0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xca,
    0x70, 0x3d, 0xff, 0xff, 0xff, 0xfd, 0x83, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x8d, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0xdf, 0xff, 0xe9, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x36, 0xad, 0xfc, 0x0, 0x4, 0x96, 0x20, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf3, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x1a, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x76, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+670D "服" */
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x6d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xdb,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x1f, 0xfb, 0x99, 0x99, 0x9f,
    0xf7, 0x0, 0x7f, 0xfb, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaf, 0xfd, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x0, 0x1f, 0xf5, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1f,
    0xf5, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f,
    0xf1, 0x0, 0x0, 0x44, 0x44, 0x4e, 0xfc, 0x0,
    0x0, 0x1f, 0xfa, 0x77, 0x77, 0x8f, 0xf7, 0x0,
    0x7f, 0xf1, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xed,
    0x91, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf9, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x30, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x1f,
    0xf5, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f,
    0xf1, 0xdf, 0x80, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x7f, 0xf1, 0x7f, 0xe0, 0x0, 0x0, 0x9, 0xff,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x7f, 0xf1, 0x1f, 0xf4, 0x0, 0x0, 0xe,
    0xfb, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x7f, 0xf1, 0xb, 0xfb, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x5f, 0xfa, 0x99, 0x99,
    0xaf, 0xf7, 0x0, 0x7f, 0xf1, 0x4, 0xff, 0x30,
    0x0, 0xbf, 0xe0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0, 0xdf,
    0xb0, 0x3, 0xff, 0x80, 0x0, 0x0, 0x8f, 0xf0,
    0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0,
    0x5f, 0xf4, 0xb, 0xff, 0x10, 0x0, 0x0, 0x9f,
    0xd0, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1,
    0x0, 0xd, 0xfd, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x7f,
    0xf1, 0x0, 0x4, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xef, 0x90, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x7f, 0xf1, 0x0, 0x0, 0xaf, 0xff, 0x50, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x8f, 0xff, 0x30,
    0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x1f,
    0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x6, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x0, 0x6f, 0xfe,
    0x8f, 0xfe, 0x30, 0x0, 0xd, 0xfc, 0x0, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x7f, 0xf1, 0x9, 0xff,
    0xe2, 0x7, 0xff, 0xf7, 0x0, 0x3f, 0xf7, 0x0,
    0x28, 0x77, 0xaf, 0xf6, 0x0, 0x7f, 0xf5, 0xdf,
    0xfe, 0x20, 0x0, 0x7f, 0xff, 0xc3, 0x9f, 0xf1,
    0x0, 0x1f, 0xff, 0xff, 0xf2, 0x0, 0x7f, 0xf4,
    0xff, 0xb1, 0x0, 0x0, 0x5, 0xff, 0xf6, 0x7,
    0xa0, 0x0, 0xc, 0xff, 0xfc, 0x50, 0x0, 0x7f,
    0xf1, 0x56, 0x0, 0x0, 0x0, 0x0, 0x2b, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6C23 "氣" */
    0x0, 0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xb, 0xff, 0x82, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xe2, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x41, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x7, 0xff, 0xf6, 0x1, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xd6, 0x0, 0x0, 0x3f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x22, 0x22, 0x23, 0x66, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x28, 0x90, 0x0, 0x7, 0xff, 0x20,
    0x0, 0xa, 0x71, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x7, 0xff,
    0x20, 0x0, 0x6f, 0xf7, 0x0, 0x1f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x20, 0x7,
    0xff, 0x20, 0x1, 0xff, 0xa0, 0x0, 0xf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0x7, 0xff, 0x20, 0xb, 0xfd, 0x0, 0x0, 0xf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xe1, 0x7, 0xff, 0x20, 0x7f, 0xe2, 0x0, 0x0,
    0xf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x0, 0x7, 0xff, 0x20, 0x4b, 0x30, 0x0,
    0x0, 0xf, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xe, 0xfb, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xd, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x38, 0xff, 0x43,
    0x33, 0x33, 0x33, 0x31, 0xc, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x53, 0x7, 0xff,
    0x20, 0x15, 0x0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x47,
    0xff, 0x21, 0xdf, 0x90, 0x0, 0x0, 0x9, 0xff,
    0x10, 0x4, 0x50, 0x0, 0x0, 0x0, 0x2e, 0xfb,
    0x7, 0xff, 0x20, 0xaf, 0xfb, 0x10, 0x0, 0x6,
    0xff, 0x30, 0x5, 0xf6, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x7, 0xff, 0x20, 0x8, 0xff, 0xd1, 0x0,
    0x3, 0xff, 0x70, 0x6, 0xf8, 0x0, 0x0, 0x3e,
    0xff, 0x30, 0x7, 0xff, 0x20, 0x0, 0x6f, 0xfe,
    0x20, 0x0, 0xff, 0xc0, 0x7, 0xf7, 0x0, 0x6,
    0xff, 0xf4, 0x0, 0x7, 0xff, 0x20, 0x0, 0x5,
    0xff, 0xe2, 0x0, 0xaf, 0xf4, 0xa, 0xf5, 0x2,
    0xbf, 0xff, 0x30, 0x0, 0x7, 0xff, 0x20, 0x0,
    0x0, 0x4f, 0xfc, 0x0, 0x3f, 0xff, 0xaf, 0xf2,
    0x2, 0xef, 0xd2, 0x0, 0x0, 0x7, 0xff, 0x20,
    0x0, 0x0, 0x5, 0xd2, 0x0, 0x9, 0xff, 0xff,
    0xd0, 0x0, 0x49, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xfd, 0x30,

    /* U+6C34 "水" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xed, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfd, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x1, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x20, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x1f, 0xff, 0xa0, 0x0, 0x0, 0x8f, 0xff, 0x30,
    0x0, 0x0, 0x3, 0xbb, 0xbb, 0xbb, 0xbb, 0xdf,
    0xf7, 0x1, 0xff, 0xff, 0x20, 0x0, 0x6f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x30, 0x1f, 0xff, 0xfa, 0x0, 0x5f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf0, 0x1, 0xff, 0xff, 0xf3,
    0x5f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x1f, 0xfd,
    0xef, 0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x50, 0x1,
    0xff, 0xd6, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x1f, 0xfd, 0xd, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf9, 0x0, 0x1, 0xff, 0xd0, 0x3f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x20, 0x0, 0x1f, 0xfd, 0x0, 0x9f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xb0, 0x0, 0x1, 0xff, 0xd0,
    0x0, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf3, 0x0, 0x0, 0x1f,
    0xfd, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfa, 0x0, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x7, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x10,
    0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x9, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x70, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x0, 0x0,
    0xb, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfd, 0x40,
    0x0, 0x5f, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xb1, 0x2e, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfa, 0x0, 0x2e, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbd, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0xee, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0xda, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+6C96 "沖" */
    0x0, 0x6, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xcf, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfb, 0x88, 0x88, 0x89, 0xff,
    0xd8, 0x88, 0x88, 0x8f, 0xfb, 0xa, 0xc7, 0x10,
    0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x1f,
    0xf9, 0x0, 0x0, 0x0, 0xef, 0xb5, 0xff, 0xff,
    0x91, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x1,
    0xff, 0x90, 0x0, 0x0, 0xe, 0xfb, 0x6, 0xcf,
    0xff, 0xf9, 0x10, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x1f, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x0,
    0x4c, 0xff, 0xf9, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x1, 0xff, 0x90, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0x5, 0xee, 0x10, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0xe, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0xef,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0xe,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0xb, 0x10, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x14,
    0xff, 0xca, 0xaa, 0xaa, 0xbf, 0xfe, 0xaa, 0xaa,
    0xaa, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef, 0xe0,
    0x4f, 0xf5, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0xe, 0xfb, 0x0, 0x0, 0x0, 0x8f, 0xf6,
    0x3, 0xcc, 0x30, 0x0, 0x0, 0x1f, 0xf9, 0x0,
    0x0, 0x0, 0x88, 0x60, 0x0, 0x0, 0x1f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6E05 "清" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xb1, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x5, 0xff, 0xfe,
    0x30, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x2d,
    0xff, 0xf0, 0x22, 0x22, 0x22, 0x22, 0x22, 0xff,
    0xc2, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0,
    0x1, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x0, 0x3, 0x33, 0x33, 0x33,
    0x33, 0xff, 0xc3, 0x33, 0x33, 0x33, 0x32, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xcc,
    0xcc, 0xcc, 0xcc, 0xff, 0xfc, 0xcc, 0xcc, 0xcc,
    0xc8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x30, 0x0,
    0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xea,
    0xaa, 0xaa, 0xaa, 0xaa, 0xa5, 0x6e, 0xff, 0xf9,
    0x10, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x1, 0x9f,
    0xff, 0xf3, 0x16, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x63, 0x0,
    0x3, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x40, 0x0, 0x0, 0x34, 0x54,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x54, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0x0, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0xaf, 0xfe, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x60, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0xaf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0xbf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0, 0xbf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x1f, 0xfb, 0x0,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xf3,
    0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x2, 0xff,
    0xb0, 0x0, 0x0, 0xc, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0xb,
    0xff, 0x30, 0x0, 0x0, 0x4f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x44, 0x46, 0xff, 0xa0, 0x0,
    0x5, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x9, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+6F54 "潔" */
    0x0, 0x63, 0x0, 0x0, 0x0, 0x0, 0x2, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xfa, 0x0, 0x13, 0x33, 0x3f,
    0xf8, 0x33, 0x32, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0x40, 0x0, 0x8f, 0xfc, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x6f, 0xf8, 0x3b, 0xbb,
    0xbf, 0xfd, 0xbb, 0xb7, 0x14, 0x44, 0xff, 0x84,
    0x46, 0xff, 0x50, 0x0, 0x0, 0x6c, 0x0, 0x0,
    0x0, 0xef, 0x60, 0x0, 0x0, 0x0, 0x1f, 0xf2,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xbb, 0xbf, 0xfd, 0xbb, 0xb2, 0x0, 0x4, 0xff,
    0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x8f,
    0xb0, 0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x22, 0x2f, 0xf7, 0x22, 0x20, 0x0, 0xd,
    0xf7, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x60, 0x1, 0x20, 0x4,
    0xff, 0x10, 0x0, 0x8f, 0xf0, 0x7, 0xa1, 0x0,
    0x0, 0x15, 0x67, 0x8f, 0xfe, 0xef, 0xfc, 0x0,
    0xdf, 0x90, 0x0, 0xa, 0xfd, 0x4, 0xff, 0xe4,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xaf, 0xe1, 0x13, 0x23, 0xff, 0xa0, 0x8, 0xff,
    0xf7, 0x0, 0x3e, 0xcb, 0x9f, 0xf8, 0x31, 0x1,
    0xbf, 0xf3, 0x0, 0xff, 0xff, 0xf5, 0x0, 0x4,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x8, 0xe3, 0x0, 0xc, 0xfe, 0xd7, 0x0, 0x0,
    0x2, 0xef, 0xe1, 0x0, 0x0, 0x3, 0x31, 0x5,
    0xfb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0xff, 0x60, 0x0, 0x7, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf,
    0xfa, 0x21, 0x12, 0x5d, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x70, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x7, 0xff,
    0xed, 0xcb, 0xcf, 0xff, 0xf8, 0x10, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x2,
    0x0, 0x1, 0x9f, 0xff, 0x91, 0x0, 0x0, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0,
    0x0, 0x3a, 0xff, 0xe8, 0x10, 0x0, 0x1, 0x22,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf, 0xc0, 0x0,
    0x17, 0xdf, 0xff, 0xfb, 0xcd, 0xde, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x3f, 0xf6, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed,
    0xcb, 0xa9, 0xcf, 0xf2, 0x0, 0x9, 0xff, 0x10,
    0x2, 0xdb, 0x98, 0x65, 0x43, 0x2f, 0xf9, 0x0,
    0x0, 0x0, 0x1, 0xee, 0x40, 0x0, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0xff, 0x90,
    0x0, 0x51, 0x0, 0x3, 0x0, 0x0, 0x6f, 0xf4,
    0x0, 0x0, 0x0, 0x1b, 0xfb, 0x10, 0xf, 0xf9,
    0x0, 0x8f, 0xe5, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x0, 0x0, 0x0, 0x3d, 0xff, 0x90, 0x0, 0xff,
    0x90, 0x3, 0xdf, 0xfb, 0x10, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x1, 0x9f, 0xff, 0x60, 0x0, 0xf,
    0xf9, 0x0, 0x0, 0x8f, 0xfe, 0x50, 0x0, 0xbf,
    0xf1, 0x0, 0x7, 0xef, 0xfc, 0x20, 0x0, 0x0,
    0xff, 0x90, 0x0, 0x0, 0x4e, 0xff, 0x90, 0x2f,
    0xfa, 0x0, 0x0, 0x9f, 0xe5, 0x0, 0x6, 0xcc,
    0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x20,
    0x6e, 0x30, 0x0, 0x0, 0x60, 0x0, 0x0, 0x1f,
    0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x8, 0x40,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x33, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+71B1 "熱" */
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xaa, 0xaa,
    0xbf, 0xfc, 0xaa, 0xaa, 0x70, 0x0, 0x0, 0x5f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x55, 0x55, 0x6f, 0xf8, 0x55, 0x55, 0x30,
    0x1, 0x11, 0x7f, 0xf2, 0x11, 0x22, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x40, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc2, 0x55, 0x5a, 0xfe,
    0x55, 0x58, 0xff, 0x10, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x9f, 0xd0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf7, 0x0, 0x9f, 0x80, 0x0, 0x0,
    0x0, 0xa, 0xfc, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x30, 0x9, 0xf8, 0x0,
    0x0, 0x7, 0x10, 0xcf, 0xa0, 0x0, 0x5f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0x8f,
    0x90, 0x0, 0x15, 0xfe, 0x5d, 0xf9, 0x0, 0x5,
    0xff, 0x10, 0x0, 0x0, 0x5, 0xdf, 0xf3, 0x0,
    0x5, 0xff, 0xff, 0xfe, 0x5f, 0xff, 0xff, 0x60,
    0x0, 0x5f, 0xf1, 0x0, 0x0, 0x2e, 0xff, 0xd3,
    0x1, 0xaa, 0x37, 0xab, 0xbb, 0x90, 0x2d, 0xff,
    0xf6, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0, 0x7c,
    0x60, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf8, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xfc, 0x15, 0xff, 0x10,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xe, 0xfb, 0xdf, 0xfe, 0x6f,
    0xf1, 0x8, 0x20, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4, 0xff, 0x50, 0xaf,
    0x95, 0xff, 0x10, 0xdf, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0x71, 0x5f, 0xf1, 0xd, 0xf0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x0, 0x5, 0xff, 0x10, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf4, 0x2, 0x45, 0x77,
    0x1d, 0xff, 0x10, 0x0, 0x0, 0x5f, 0xf1, 0xf,
    0xe0, 0x1, 0x34, 0x67, 0x9b, 0xff, 0xff, 0xff,
    0xff, 0xdb, 0xff, 0x60, 0x0, 0x0, 0x4, 0xff,
    0x44, 0xfc, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xec, 0xcf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0x90, 0xf, 0xff, 0xec, 0xa9,
    0x75, 0x32, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xc1, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x5a, 0xa0,
    0x0, 0x1, 0xbe, 0x70, 0x0, 0x5, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0, 0x7,
    0xff, 0x10, 0x0, 0xd, 0xfe, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x0, 0x7f, 0xf4, 0x0,
    0x0, 0xd, 0xff, 0x30, 0x0, 0x0, 0x1e, 0xff,
    0x10, 0x0, 0x3, 0xff, 0x60, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0xc,
    0xff, 0x60, 0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0,
    0xb, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xa0,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0xef,
    0xf1, 0x0, 0x29, 0xa0, 0x0, 0x0, 0x0, 0x8,
    0x63, 0x0, 0x0, 0x2, 0x52, 0x0, 0x0, 0x0,
    0x6, 0xa4, 0x0,

    /* U+7D61 "絡" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x76, 0x66, 0x66, 0x67, 0x50, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x9f, 0xf3, 0x0, 0x62, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x2f,
    0xf8, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x10, 0x0, 0x0, 0xc, 0xfe, 0x0,
    0xa, 0xff, 0x50, 0x2, 0xef, 0xff, 0xb0, 0x0,
    0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x7, 0xff,
    0x40, 0x3, 0xff, 0xb0, 0x2, 0xef, 0xfc, 0xff,
    0x50, 0x0, 0x2, 0xff, 0xe0, 0x0, 0x0, 0x3,
    0xff, 0x90, 0x0, 0xdf, 0xf2, 0x3, 0xef, 0xfb,
    0xc, 0xff, 0x20, 0x0, 0xcf, 0xf5, 0x0, 0x0,
    0x4, 0xef, 0xfa, 0xac, 0xef, 0xf7, 0x0, 0x8f,
    0xfb, 0x0, 0x2f, 0xfd, 0x10, 0xaf, 0xf9, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x6a, 0x0, 0x0, 0x5f, 0xfc, 0x8f, 0xfc,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0xb9, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x1, 0x0, 0x7,
    0xff, 0x62, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xb0, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xd1, 0xa, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xfd, 0xaf, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0, 0x4f, 0xf0,
    0x0, 0x0, 0x9, 0xff, 0xfb, 0x10, 0x8f, 0xff,
    0xc3, 0x0, 0x0, 0x0, 0xaf, 0xf5, 0x1, 0x36,
    0xff, 0x40, 0x0, 0x6e, 0xff, 0xf7, 0x0, 0x0,
    0x5e, 0xff, 0xfa, 0x30, 0x1, 0xaf, 0xff, 0xdf,
    0xff, 0xff, 0xf9, 0x17, 0xef, 0xff, 0xc2, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xa0, 0x4f, 0xff,
    0xff, 0xff, 0xeb, 0xbf, 0xe1, 0xef, 0xff, 0xa4,
    0x44, 0x44, 0x44, 0x44, 0x49, 0xff, 0xf2, 0x0,
    0xef, 0xc9, 0x63, 0x0, 0x2, 0xff, 0x13, 0xdd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x8, 0x30,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x2a,
    0xc0, 0x0, 0xa, 0xfe, 0x11, 0x11, 0x11, 0x11,
    0x13, 0xff, 0x80, 0x0, 0x1, 0xfd, 0x30, 0xdf,
    0x30, 0xff, 0x20, 0x0, 0xaf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x3f, 0xf2,
    0xc, 0xf6, 0xa, 0xf7, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x80, 0x0, 0x5,
    0xff, 0x0, 0x9f, 0x80, 0x5f, 0xd0, 0x0, 0xaf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf8, 0x0,
    0x0, 0x8f, 0xd0, 0x7, 0xfb, 0x1, 0xff, 0x10,
    0xa, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x80, 0x0, 0xb, 0xfa, 0x0, 0x6f, 0xd0, 0xc,
    0xf6, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0xef, 0x70, 0x4, 0xfe,
    0x0, 0x9f, 0x70, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x80, 0x0, 0x2f, 0xf4, 0x0,
    0x2f, 0xf0, 0x2, 0x10, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x7, 0xff,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x4c, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf7, 0x77, 0x77, 0x77, 0x77, 0x8f, 0xf8,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+806F "聯" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0x30, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x0, 0x27, 0x77, 0x77, 0x77, 0x77, 0x77, 0x50,
    0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x7, 0xfe, 0x10, 0x0, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x1f, 0xf3, 0x0, 0x1f, 0xf6, 0x1, 0x50, 0x0,
    0xd, 0xf7, 0x1, 0x50, 0x0, 0x0, 0x7f, 0xc0,
    0x0, 0x1f, 0xf3, 0x0, 0xbf, 0xb0, 0xa, 0xf9,
    0x0, 0x7f, 0xd0, 0x8, 0xfa, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x1f, 0xf3, 0x6, 0xfe, 0x10, 0x4f,
    0xf3, 0x3, 0xff, 0x20, 0x1f, 0xf4, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x1f, 0xf3, 0x6f, 0xfb, 0x88,
    0xef, 0x80, 0x3f, 0xfe, 0xab, 0xdf, 0xb0, 0x0,
    0x0, 0x7f, 0xd2, 0x22, 0x3f, 0xf3, 0x7f, 0xff,
    0xff, 0xfc, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf3, 0x3a,
    0x76, 0xaf, 0xf2, 0x0, 0x8, 0x64, 0x3d, 0xf8,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x0, 0x7f,
    0xd0, 0x46, 0x0, 0x0, 0x7f, 0xd2, 0x22, 0x3f,
    0xf3, 0x0, 0x1d, 0xf9, 0x5, 0xf5, 0x0, 0x3,
    0xff, 0x30, 0xcf, 0x10, 0x0, 0x7f, 0xc0, 0x0,
    0x1f, 0xf3, 0x0, 0xcf, 0xb0, 0x2, 0xfb, 0x0,
    0x1d, 0xf8, 0x0, 0x5f, 0x80, 0x0, 0x7f, 0xc0,
    0x0, 0x1f, 0xf3, 0x1b, 0xfc, 0x22, 0x45, 0xef,
    0x0, 0xbf, 0xe5, 0x68, 0xaf, 0xe0, 0x0, 0x7f,
    0xc0, 0x0, 0x1f, 0xf3, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x7f, 0xc0, 0x0, 0x1f, 0xf3, 0x7f, 0xff, 0xdb,
    0x97, 0x7f, 0x99, 0xfd, 0xa8, 0x64, 0x24, 0xfb,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf3, 0x24, 0x10,
    0x0, 0x0, 0xe, 0x83, 0x10, 0x0, 0x0, 0x0,
    0xb6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf3, 0x1,
    0x99, 0x0, 0x4, 0x99, 0x0, 0xaa, 0x20, 0x1,
    0x54, 0x0, 0x0, 0x7f, 0xe6, 0x66, 0x7f, 0xf3,
    0x2, 0xff, 0x10, 0x7, 0xfe, 0x1, 0xff, 0x40,
    0x4, 0xff, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x1f,
    0xf3, 0x2, 0xff, 0x10, 0x7, 0xfe, 0x1, 0xff,
    0x40, 0x4, 0xff, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x1f, 0xf3, 0x2, 0xff, 0x10, 0x7, 0xfe, 0x1,
    0xff, 0x40, 0x4, 0xff, 0x0, 0x0, 0x7f, 0xc0,
    0x0, 0x1f, 0xf3, 0x2, 0xff, 0x10, 0x7, 0xfe,
    0x1, 0xff, 0x40, 0x4, 0xff, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x1f, 0xf3, 0x2, 0xff, 0x10, 0x7,
    0xfe, 0x1, 0xff, 0x40, 0x4, 0xff, 0x0, 0x0,
    0x7f, 0xc0, 0x1, 0x5f, 0xf3, 0x2, 0xff, 0xff,
    0xff, 0xfe, 0x1, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xaf, 0xfc, 0xff, 0xff, 0xf3, 0x2, 0xff,
    0xff, 0xff, 0xfc, 0x1, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x1,
    0x78, 0x66, 0x6e, 0xfa, 0x1, 0xff, 0x85, 0x58,
    0xff, 0x0, 0x5f, 0xff, 0xc9, 0x74, 0x2f, 0xf3,
    0x0, 0x0, 0x0, 0x1f, 0xf6, 0x1, 0xff, 0x40,
    0x3, 0xbb, 0x0, 0x15, 0x20, 0x0, 0x0, 0x1f,
    0xf3, 0x0, 0x0, 0x0, 0x8f, 0xf1, 0x1, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x1,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0x4f, 0xfe, 0x10,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf3, 0x0, 0x19, 0xff, 0xf4,
    0x0, 0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0x0, 0xef, 0xfe,
    0x30, 0x0, 0x1, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x0, 0x4f,
    0x91, 0x0, 0x0, 0x1, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0,

    /* U+8336 "茶" */
    0x0, 0x0, 0x0, 0x0, 0x23, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x18, 0x88, 0x88, 0x88,
    0xdf, 0xf8, 0x88, 0x85, 0x2, 0x88, 0x88, 0x8f,
    0xfd, 0x88, 0x88, 0x88, 0x50, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x64, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x6, 0xff, 0xb0, 0x0, 0xf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x47, 0x70,
    0x0, 0x5f, 0xff, 0xb0, 0x0, 0x7, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xfe, 0x36, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xd2, 0x0, 0x2d, 0xff,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xfd, 0x40, 0x1, 0x22,
    0x0, 0x2, 0xbf, 0xff, 0xe8, 0x10, 0x0, 0x0,
    0x0, 0x2, 0x8e, 0xff, 0xfe, 0x60, 0x0, 0xb,
    0xff, 0x10, 0x0, 0x4, 0xcf, 0xff, 0xfb, 0x50,
    0x0, 0x27, 0xcf, 0xff, 0xfe, 0x70, 0x0, 0x0,
    0xb, 0xff, 0x10, 0x0, 0x0, 0x4, 0xcf, 0xff,
    0xff, 0xb2, 0x2e, 0xff, 0xfc, 0x50, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x10, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xff, 0xb0, 0x4, 0xd8, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6b, 0x10, 0x0, 0x0, 0x58, 0x88,
    0x88, 0x88, 0x88, 0x8e, 0xff, 0x98, 0x88, 0x88,
    0x88, 0x88, 0x70, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x15, 0x10, 0x0, 0xb,
    0xff, 0x10, 0x0, 0x28, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0,
    0xb, 0xff, 0x10, 0x4, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xa0,
    0x0, 0xb, 0xff, 0x10, 0x0, 0x7f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xfb,
    0x0, 0x0, 0xb, 0xff, 0x10, 0x0, 0x3, 0xdf,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xa0, 0x0, 0x0, 0xb, 0xff, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x2b, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x80, 0x0, 0x8, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf9, 0x0, 0x1,
    0xcf, 0xa1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+84B8 "蒸" */
    0x0, 0x0, 0x0, 0x0, 0x37, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x6, 0x66, 0x66,
    0x66, 0xaf, 0xf8, 0x66, 0x65, 0x0, 0x66, 0x66,
    0x6e, 0xfd, 0x66, 0x66, 0x66, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x23, 0xbf, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x44,
    0x44, 0x44, 0x44, 0x30, 0x1, 0x7e, 0xff, 0xd5,
    0x0, 0x0, 0x1a, 0x60, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0x2, 0xdf, 0xf7, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5, 0xff,
    0xff, 0x30, 0x0, 0x5f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x5,
    0xff, 0xef, 0xf4, 0x8, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x10,
    0x5, 0xff, 0x5c, 0xff, 0xef, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf4,
    0x0, 0x5, 0xff, 0x40, 0xbf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x60, 0x0, 0x5, 0xff, 0x40, 0x8, 0xff, 0xfb,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff,
    0xf5, 0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x3d,
    0xff, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x29, 0xff,
    0xfd, 0x30, 0x1, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x7e, 0xff, 0xff, 0xc8, 0x40, 0x1b, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xbf, 0xff, 0xd6, 0x0,
    0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0x70, 0x9,
    0xff, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xeb, 0x0,
    0x0, 0x71, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0,
    0x29, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5,
    0x0, 0x1c, 0xf8, 0x0, 0x0, 0x2f, 0xf8, 0x0,
    0x1, 0xef, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xe1, 0x0, 0xd, 0xfd, 0x0, 0x0, 0xa, 0xff,
    0x20, 0x0, 0x2e, 0xff, 0x60, 0x0, 0x0, 0xd,
    0xff, 0x40, 0x0, 0x9, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xa0, 0x0, 0x2, 0xef, 0xf6, 0x0, 0x0,
    0xbf, 0xf9, 0x0, 0x0, 0x5, 0xff, 0x50, 0x0,
    0x0, 0xbf, 0xf1, 0x0, 0x0, 0x2e, 0xff, 0x40,
    0xb, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0, 0x3, 0xff,
    0xd0, 0x2, 0xcd, 0x10, 0x0, 0x0, 0x1, 0xa7,
    0x30, 0x0, 0x0, 0x8, 0x30, 0x0, 0x0, 0x0,
    0x68, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xa5, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x5a, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+8A2D "設" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x0, 0x7, 0xff, 0x99, 0x99,
    0x99, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xe5, 0x0, 0x0, 0x0, 0x7, 0xff, 0x10,
    0x0, 0x0, 0xef, 0xa0, 0x0, 0x0, 0x2, 0x22,
    0x22, 0x25, 0x22, 0x22, 0x22, 0x10, 0x7, 0xff,
    0x10, 0x0, 0x0, 0xef, 0xa0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x8,
    0xff, 0x0, 0x0, 0x0, 0xef, 0xa0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xb, 0xfd, 0x0, 0x0, 0x0, 0xef, 0xa0, 0x0,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x10, 0xf, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0xef,
    0xa0, 0x0, 0x0, 0x0, 0x35, 0x55, 0x55, 0x55,
    0x55, 0x50, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xdf, 0xc3, 0x33, 0x40, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x2e, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xe0, 0x0, 0x7c, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc6, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6b, 0xbb, 0xbb, 0xbb, 0xbb, 0xb1, 0x67,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x78, 0x60,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x35, 0x55, 0x55, 0x55, 0x55,
    0x50, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xbf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xc0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x70, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x7f, 0xf6, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x10, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0xe, 0xfe,
    0x10, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0,
    0x9f, 0xd5, 0x55, 0x55, 0x8f, 0xf1, 0x0, 0x5,
    0xff, 0xb0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x2e, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x5f, 0xf1,
    0x0, 0x0, 0x1e, 0xff, 0x71, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x5f,
    0xf1, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x5f, 0xf1, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0x5f, 0xf1, 0x0, 0x0, 0x1, 0xaf, 0xff,
    0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd5,
    0x55, 0x55, 0x8f, 0xf1, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x2, 0x8e, 0xff,
    0xff, 0x73, 0xdf, 0xff, 0xfc, 0x61, 0x0, 0x0,
    0x9f, 0xfe, 0xee, 0xee, 0xee, 0xe9, 0xcf, 0xff,
    0xff, 0xc2, 0x0, 0x8, 0xff, 0xff, 0xff, 0xc5,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xd5, 0x0, 0x0, 0x0, 0x2a, 0xff, 0xff,
    0xf2, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x10,

    /* U+8A9E "語" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x1, 0xef, 0xd0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x25, 0x55, 0x55, 0xbf, 0xf7,
    0x55, 0x55, 0x55, 0x55, 0x50, 0x0, 0x0, 0x0,
    0xe, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x55,
    0x55, 0x57, 0x55, 0x55, 0x55, 0x0, 0x0, 0x0,
    0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x66, 0x6b, 0xff, 0x66, 0x66, 0x68,
    0xff, 0x50, 0x0, 0x0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x20, 0x0, 0x0, 0xb, 0xfe, 0x0, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x1f, 0xf8,
    0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x22,
    0xaf, 0xf4, 0x22, 0x22, 0x26, 0xff, 0x72, 0x22,
    0x0, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0x97, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xb7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x30, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x42, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xdf, 0xa5, 0x55, 0x55, 0xaf, 0xe0, 0x6, 0xff,
    0x31, 0x11, 0x11, 0x11, 0x11, 0x3f, 0xf7, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x7f, 0xe0, 0x6,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf7,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x7f, 0xe0,
    0x6, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf7, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x7f,
    0xe0, 0x6, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0,
    0x7f, 0xe0, 0x6, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf7, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0x0, 0x7f, 0xe0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0xdf, 0xd9,
    0x99, 0x99, 0xcf, 0xe0, 0x6, 0xff, 0x31, 0x11,
    0x11, 0x11, 0x11, 0x3f, 0xf7, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xdf, 0xdb, 0xbb, 0xbb, 0xbb, 0xa0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x64, 0x44, 0x44, 0x44, 0x44, 0x6f, 0xf7,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+9078 "選" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfb, 0x0, 0x0,
    0x4, 0xde, 0xdd, 0xdd, 0xdd, 0xd2, 0xc, 0xdc,
    0xcc, 0xcc, 0xcc, 0xc1, 0x0, 0x0, 0x7f, 0xfa,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x9f, 0xf7, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x1f,
    0xf3, 0xf, 0xf4, 0x11, 0x11, 0x5f, 0xf1, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0x0, 0x5f, 0xe0, 0x0,
    0x0, 0xff, 0x30, 0xff, 0x30, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x2, 0xff, 0x70, 0x5, 0xff,
    0xdd, 0xdd, 0xdf, 0xf3, 0xf, 0xfd, 0xcc, 0xcc,
    0xdf, 0xf1, 0x0, 0x0, 0x0, 0x5, 0x40, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x5, 0xff, 0x11, 0x11, 0x11, 0x10, 0xf,
    0xf4, 0x11, 0x11, 0x11, 0x10, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xa0, 0x5f, 0xe0, 0x0, 0x0, 0x0,
    0x10, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf7, 0x7, 0xfe, 0x3, 0x58,
    0xad, 0xf7, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x37, 0x77, 0x7f, 0xfd, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x50, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x5, 0xff, 0x40, 0xd,
    0xff, 0xfd, 0xb8, 0x52, 0x0, 0x2, 0xbe, 0xff,
    0xff, 0xfe, 0xe9, 0x0, 0x0, 0x0, 0xef, 0xa0,
    0x0, 0x57, 0x30, 0x0, 0x88, 0x30, 0x0, 0x0,
    0x88, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x70, 0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x0, 0x1, 0xee, 0xee,
    0xef, 0xff, 0xee, 0xee, 0xef, 0xff, 0xee, 0xee,
    0xd0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xa, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0x33, 0x33, 0x4f, 0xf8, 0x33, 0x33, 0x4f,
    0xfa, 0x33, 0x33, 0x20, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x1, 0xff, 0x70, 0x0,
    0x1, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x95,
    0x33, 0x3c, 0xfd, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xa2, 0x33, 0x33, 0x34,
    0xff, 0x93, 0x33, 0x34, 0xff, 0xa3, 0x33, 0x33,
    0x30, 0x0, 0x0, 0x0, 0x2f, 0xf6, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x6, 0xff, 0x28,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xe2, 0x0, 0x0, 0x0, 0xcf,
    0xd0, 0x0, 0x0, 0x0, 0x6, 0xb6, 0x0, 0x0,
    0x19, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xc1,
    0x0, 0x7, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x50, 0x0, 0x1, 0x9f, 0xff,
    0x90, 0x0, 0x0, 0x1, 0x9f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xfd, 0x10, 0x39, 0xff,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0xcf, 0xfe, 0xfd, 0x23,
    0xdf, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xc1, 0x0, 0x0, 0x7f, 0xfa, 0x2e,
    0xff, 0x83, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x5f, 0xfe,
    0x10, 0x2d, 0xff, 0xfd, 0x97, 0x53, 0x22, 0x10,
    0x0, 0x11, 0x22, 0x34, 0x45, 0x67, 0x60, 0x6f,
    0xff, 0x40, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x9, 0xff, 0x60, 0x0, 0x0, 0x0, 0x5a, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x7, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x23, 0x44, 0x55, 0x55, 0x54, 0x44,
    0x33, 0x21, 0x0,

    /* U+9223 "鈣" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x73, 0x0, 0x0, 0x0, 0xaf, 0xfd,
    0x30, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf8, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5f,
    0xfe, 0x5e, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0xaf, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x50, 0x0, 0x6, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x3e, 0xf5,
    0x22, 0x10, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x81, 0x11, 0x11, 0x11, 0x12,
    0x80, 0xff, 0x80, 0x0, 0x4f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0xff, 0x80, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xd, 0x4c, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0xff, 0x80, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x2, 0x33, 0x3f,
    0xf9, 0x33, 0x32, 0x0, 0xff, 0x80, 0x0, 0x4f,
    0xfa, 0x77, 0x77, 0x77, 0x40, 0x0, 0x0, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0xff, 0x80, 0x0,
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0xff,
    0x80, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xdd, 0xdd, 0xdf, 0xfe, 0xdd, 0xdd, 0xa0,
    0xff, 0x80, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0xff, 0x80, 0x0, 0x4f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x88, 0x88, 0x8f, 0xfc, 0x88,
    0x88, 0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x35, 0x0, 0xe,
    0xf7, 0x0, 0xa8, 0x10, 0x68, 0x76, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x9f, 0xf4, 0x0, 0xff, 0x10,
    0xe, 0xf7, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0xbf,
    0x50, 0xe, 0xf7, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0,
    0x7f, 0x90, 0xe, 0xf7, 0x7, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x0, 0x4f, 0xd0, 0xe, 0xf7, 0xb, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf0, 0x0, 0xf, 0xf0, 0xe, 0xf7, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xe0, 0x0, 0xe, 0xf3, 0xe, 0xf7, 0x4f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xc0, 0x0, 0xb, 0xb2, 0xe, 0xf7,
    0x5, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xb0, 0x0, 0x0, 0x0, 0xe,
    0xf8, 0x47, 0xad, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x14,
    0x7f, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x28, 0xbe,
    0xff, 0xff, 0xff, 0xfe, 0xb7, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20, 0x2f,
    0xff, 0xff, 0xfc, 0x95, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x76, 0x55, 0x8f, 0xfe, 0x0,
    0xe, 0xea, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xfe,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0,

    /* U+9664 "除" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x77, 0x79,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0xef,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0,
    0x8, 0xff, 0x10, 0x0, 0x0, 0x2, 0xef, 0xf4,
    0x3f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xf3,
    0x0, 0xd, 0xfb, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0x60, 0x4, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x3f,
    0xf3, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x3, 0xef,
    0xf7, 0x0, 0x0, 0x5f, 0xff, 0x60, 0x0, 0x0,
    0x3f, 0xf3, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x7f,
    0xff, 0x70, 0x0, 0x0, 0x5, 0xff, 0xfa, 0x10,
    0x0, 0x3f, 0xf3, 0x0, 0xef, 0x70, 0x0, 0x1b,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xe5, 0x0, 0x3f, 0xf3, 0x5, 0xff, 0x10, 0x6,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0xb0, 0x3f, 0xf3, 0xb, 0xf9, 0x0,
    0x4f, 0xff, 0xc6, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x58, 0xff, 0x50, 0x3f, 0xf3, 0x1f, 0xf6,
    0x0, 0x4, 0xe6, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x47, 0x0, 0x3f, 0xf3, 0x7,
    0xff, 0x20, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3f, 0xf3,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf3, 0x0, 0x1f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf3, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf3, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0xff, 0x73,
    0x66, 0x66, 0x66, 0x66, 0x7f, 0xfb, 0x66, 0x66,
    0x66, 0x66, 0x20, 0x3f, 0xf3, 0x0, 0x0, 0xcf,
    0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x3f, 0xf3, 0x0, 0x0,
    0xcf, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x3f, 0xf3, 0x0,
    0x0, 0xef, 0x90, 0x11, 0x11, 0x11, 0x11, 0x2f,
    0xf8, 0x11, 0x11, 0x11, 0x11, 0x0, 0x3f, 0xf3,
    0x10, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x1, 0x0, 0x0, 0x0, 0x3f,
    0xf3, 0xcf, 0xff, 0xff, 0x10, 0x0, 0x2d, 0x93,
    0x0, 0x1f, 0xf8, 0x1, 0xaf, 0x40, 0x0, 0x0,
    0x3f, 0xf3, 0x7f, 0xff, 0xe4, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0x1f, 0xf8, 0x0, 0xdf, 0xe2, 0x0,
    0x0, 0x3f, 0xf3, 0x15, 0x43, 0x0, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0x1f, 0xf8, 0x0, 0x2f, 0xfc,
    0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x20, 0x0, 0x1f, 0xf8, 0x0, 0x6,
    0xff, 0x80, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf7, 0x0, 0x0, 0x1f, 0xf8, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x3f, 0xf3, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xd0, 0x0, 0x0, 0x1f, 0xf8,
    0x0, 0x0, 0x1f, 0xfd, 0x0, 0x3f, 0xf3, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0x20, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x0, 0x6, 0xff, 0x70, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x3f,
    0xf3, 0x0, 0x0, 0x0, 0x5, 0x90, 0x0, 0x65,
    0x55, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x32, 0x0,
    0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 129, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 186, .box_w = 6, .box_h = 27, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 81, .adv_w = 273, .box_w = 11, .box_h = 11, .ofs_x = 3, .ofs_y = 17},
    {.bitmap_index = 142, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 376, .adv_w = 320, .box_w = 16, .box_h = 35, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 656, .adv_w = 530, .box_w = 31, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1059, .adv_w = 392, .box_w = 23, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1358, .adv_w = 160, .box_w = 4, .box_h = 11, .ofs_x = 3, .ofs_y = 17},
    {.bitmap_index = 1380, .adv_w = 195, .box_w = 8, .box_h = 38, .ofs_x = 3, .ofs_y = -8},
    {.bitmap_index = 1532, .adv_w = 195, .box_w = 8, .box_h = 38, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 1684, .adv_w = 269, .box_w = 13, .box_h = 12, .ofs_x = 2, .ofs_y = 16},
    {.bitmap_index = 1762, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1933, .adv_w = 160, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1975, .adv_w = 200, .box_w = 10, .box_h = 3, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 1990, .adv_w = 160, .box_w = 6, .box_h = 5, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2005, .adv_w = 226, .box_w = 14, .box_h = 35, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 2250, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2484, .adv_w = 320, .box_w = 15, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 2679, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2913, .adv_w = 320, .box_w = 17, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3134, .adv_w = 320, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3381, .adv_w = 320, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3628, .adv_w = 320, .box_w = 17, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3849, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4083, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4317, .adv_w = 320, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4551, .adv_w = 160, .box_w = 6, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4611, .adv_w = 160, .box_w = 7, .box_h = 27, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 4706, .adv_w = 320, .box_w = 18, .box_h = 17, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 4859, .adv_w = 320, .box_w = 18, .box_h = 11, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 4958, .adv_w = 320, .box_w = 18, .box_h = 17, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 5111, .adv_w = 273, .box_w = 15, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5321, .adv_w = 545, .box_w = 30, .box_h = 33, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 5816, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6102, .adv_w = 378, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6362, .adv_w = 367, .box_w = 20, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6622, .adv_w = 396, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6882, .adv_w = 339, .box_w = 17, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7103, .adv_w = 318, .box_w = 16, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7311, .adv_w = 397, .box_w = 21, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7584, .adv_w = 419, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7844, .adv_w = 169, .box_w = 4, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7896, .adv_w = 308, .box_w = 15, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8091, .adv_w = 372, .box_w = 21, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8364, .adv_w = 313, .box_w = 16, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8572, .adv_w = 468, .box_w = 23, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8871, .adv_w = 416, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9131, .adv_w = 427, .box_w = 23, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9430, .adv_w = 365, .box_w = 19, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9677, .adv_w = 427, .box_w = 24, .box_h = 32, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 10061, .adv_w = 366, .box_w = 19, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 10308, .adv_w = 343, .box_w = 19, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10555, .adv_w = 345, .box_w = 20, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10815, .adv_w = 415, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 11075, .adv_w = 331, .box_w = 21, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11348, .adv_w = 506, .box_w = 31, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11751, .adv_w = 330, .box_w = 21, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12024, .adv_w = 306, .box_w = 21, .box_h = 26, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12297, .adv_w = 347, .box_w = 20, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12557, .adv_w = 195, .box_w = 8, .box_h = 35, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 12697, .adv_w = 226, .box_w = 14, .box_h = 35, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12942, .adv_w = 195, .box_w = 8, .box_h = 35, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 13082, .adv_w = 320, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 11},
    {.bitmap_index = 13210, .adv_w = 322, .box_w = 20, .box_h = 3, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 13240, .adv_w = 349, .box_w = 9, .box_h = 10, .ofs_x = 5, .ofs_y = 22},
    {.bitmap_index = 13285, .adv_w = 324, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 13453, .adv_w = 356, .box_w = 18, .box_h = 28, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 13705, .adv_w = 294, .box_w = 17, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13884, .adv_w = 357, .box_w = 19, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14150, .adv_w = 319, .box_w = 18, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14339, .adv_w = 187, .box_w = 12, .box_h = 29, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14513, .adv_w = 325, .box_w = 19, .box_h = 29, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 14789, .adv_w = 350, .box_w = 16, .box_h = 28, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15013, .adv_w = 158, .box_w = 6, .box_h = 28, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 15097, .adv_w = 158, .box_w = 10, .box_h = 36, .ofs_x = -2, .ofs_y = -8},
    {.bitmap_index = 15277, .adv_w = 318, .box_w = 17, .box_h = 28, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15515, .adv_w = 164, .box_w = 6, .box_h = 28, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15599, .adv_w = 533, .box_w = 28, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 15893, .adv_w = 351, .box_w = 16, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16061, .adv_w = 349, .box_w = 19, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16261, .adv_w = 357, .box_w = 18, .box_h = 29, .ofs_x = 3, .ofs_y = -8},
    {.bitmap_index = 16522, .adv_w = 357, .box_w = 19, .box_h = 29, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 16798, .adv_w = 223, .box_w = 12, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16924, .adv_w = 270, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17082, .adv_w = 217, .box_w = 14, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17264, .adv_w = 350, .box_w = 16, .box_h = 20, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 17424, .adv_w = 300, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17614, .adv_w = 462, .box_w = 28, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17894, .adv_w = 287, .box_w = 18, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18074, .adv_w = 300, .box_w = 19, .box_h = 28, .ofs_x = 0, .ofs_y = -8},
    {.bitmap_index = 18340, .adv_w = 274, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 18500, .adv_w = 195, .box_w = 10, .box_h = 35, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 18675, .adv_w = 156, .box_w = 4, .box_h = 40, .ofs_x = 3, .ofs_y = -10},
    {.bitmap_index = 18755, .adv_w = 195, .box_w = 10, .box_h = 35, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 18930, .adv_w = 320, .box_w = 18, .box_h = 6, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 18984, .adv_w = 576, .box_w = 33, .box_h = 33, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 19529, .adv_w = 576, .box_w = 32, .box_h = 34, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 20073, .adv_w = 576, .box_w = 33, .box_h = 35, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 20651, .adv_w = 576, .box_w = 33, .box_h = 32, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 21179, .adv_w = 576, .box_w = 34, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 21757, .adv_w = 576, .box_w = 35, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 22370, .adv_w = 576, .box_w = 34, .box_h = 33, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 22931, .adv_w = 576, .box_w = 34, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 23509, .adv_w = 576, .box_w = 35, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 24104, .adv_w = 576, .box_w = 33, .box_h = 33, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 24649, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 25244, .adv_w = 576, .box_w = 33, .box_h = 33, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25789, .adv_w = 576, .box_w = 35, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26384, .adv_w = 576, .box_w = 35, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26997, .adv_w = 576, .box_w = 34, .box_h = 34, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 27575, .adv_w = 576, .box_w = 34, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 28153, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 28748, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 29343, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 29938, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 30533, .adv_w = 576, .box_w = 35, .box_h = 34, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 31128, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 31723, .adv_w = 576, .box_w = 34, .box_h = 35, .ofs_x = 2, .ofs_y = -4}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x65b, 0x726, 0x773, 0xd5f, 0xd67, 0x18d2, 0x1de8,
    0x1df9, 0x1e5b, 0x1fca, 0x2119, 0x2376, 0x2f26, 0x3234, 0x34fb,
    0x367d, 0x3bc5, 0x3bf2, 0x3c63, 0x423d, 0x43e8, 0x4829
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20027, .range_length = 18474, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 23, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -75, 0, -75, 0,
    0, 0, 0, -36, 0, -62, -8, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    -21, 0, 0, 0, 0, 0, -14, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 50, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -62, 0, -89,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -65, -14, -43, -23, 0,
    -61, 0, 0, 0, -9, 0, 0, 0,
    15, 0, 0, -31, 0, -23, -16, 0,
    -14, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -14,
    -13, -32, 0, -14, -8, -19, -44, -14,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -18, 0, -6, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -28, -8, -53, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -17,
    -22, 0, -8, 15, 15, 0, 0, 4,
    -14, 0, 0, 0, 0, 0, 0, 0,
    0, -34, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -18, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -36, 0, -62,
    0, 0, 0, 0, 0, 0, -18, -5,
    -8, 0, 0, -36, -12, -10, 0, 1,
    -10, -6, -28, 13, 0, -8, 0, 0,
    0, 0, 13, -10, -5, -6, -4, -4,
    -6, 0, 0, 0, 0, -21, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    -10, -17, 0, -5, -4, -4, -10, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -10, -8, -8, -10, 0,
    0, 0, 0, 0, 0, -18, 0, 0,
    0, 0, 0, 0, -20, -8, -17, -13,
    -10, -4, -4, -4, -6, -8, 0, 0,
    0, 0, -14, 0, 0, 0, 0, -19,
    -8, -10, -8, 0, -10, 0, 0, 0,
    0, -24, 0, 0, 0, -13, 0, 0,
    0, -8, 0, -27, 0, -17, 0, -8,
    -5, -13, -14, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, -17, 0, -8, 0, -21,
    -8, 0, 0, 0, 0, 0, -48, 0,
    -48, -47, 0, 0, 0, -26, -8, -91,
    -14, 0, 0, 1, 1, -17, -1, -21,
    0, -23, -10, 0, -17, 0, 0, -14,
    -14, -8, -12, -14, -12, -18, -12, -20,
    0, 0, 0, -19, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, -14,
    0, -10, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -17, 0, -17, 0, 0, 0,
    0, 0, 0, -28, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -15, 0, -28,
    0, -20, 0, 0, 0, 0, -6, -8,
    -14, 0, -7, -13, -10, -9, -8, 0,
    -12, 0, 0, 0, -6, 0, 0, 0,
    -8, 0, 0, -23, -11, -14, -12, -12,
    -14, -10, 0, -57, 0, -99, 0, -36,
    0, 0, 0, 0, -22, 0, -18, 0,
    -16, -79, -20, -51, -37, 0, -50, 0,
    -53, 0, -9, -10, -4, 0, 0, 0,
    0, -14, -8, -25, -23, 0, -25, 0,
    0, 0, 0, 0, -74, -23, -74, -52,
    0, 0, 0, -34, 0, -96, -8, -17,
    0, 0, 0, -17, -8, -54, 0, -29,
    -17, 0, -21, 0, 0, 0, -8, 0,
    0, 0, 0, -10, 0, -14, 0, 0,
    0, -8, 0, -20, 0, 0, 0, 0,
    0, -4, 0, -13, -10, -10, 0, 2,
    2, -4, -2, -8, 0, -4, -8, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -6, 0, 0, 0, -12,
    0, 7, 0, 0, 0, 0, 0, 0,
    0, -10, -10, -14, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, -70, -48,
    -70, -60, -14, -14, 0, -28, -17, -84,
    -28, 0, 0, 0, 0, -14, -10, -36,
    0, -48, -44, -13, -48, 0, 0, -32,
    -39, -13, -32, -23, -24, -28, -23, -50,
    0, 0, 0, 0, -12, 0, -12, -22,
    0, 0, 0, -12, 0, -32, -8, 0,
    0, -4, 0, -8, -10, 0, 0, -4,
    0, 0, -8, 0, 0, 0, -4, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, -43, -14, -43, -32, 0, 0,
    0, -10, -8, -48, -8, 0, -8, 5,
    0, 0, 0, -14, 0, -16, -11, 0,
    -16, 0, 0, -14, -9, 0, -21, -7,
    -7, -11, -7, -18, 0, 0, 0, 0,
    -23, -8, -23, -21, 0, 0, 0, 0,
    -5, -44, -5, 0, 0, 0, 0, 0,
    0, -5, 0, -12, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -8, 0, -8, 0, -19,
    0, 0, 0, 0, 0, 0, -13, -2,
    -10, -14, -8, 0, 0, 0, 0, 0,
    0, -8, -6, -12, 0, 0, 0, 0,
    0, -12, -8, -12, -10, -8, -12, -10,
    0, 0, 0, 0, -59, -44, -59, -45,
    -17, -17, -6, -10, -10, -66, -12, -10,
    -8, 0, 0, 0, 0, -18, 0, -45,
    -28, 0, -40, 0, 0, -28, -28, -20,
    -23, -10, -17, -23, -10, -32, 0, 0,
    0, 0, 0, -24, 0, 0, 0, 0,
    0, -5, -14, -23, -21, 0, -8, -5,
    -5, 0, -10, -12, 0, -12, -16, -14,
    -11, 0, 0, 0, 0, -10, -17, -12,
    -12, -17, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -57, -21, -36, -21, 0,
    -48, 0, 0, 0, 0, 0, 20, 0,
    46, 0, 0, 0, 0, -14, -8, 0,
    7, 0, 0, 0, 0, -36, 0, 0,
    0, 0, 0, 0, -9, 0, 0, 0,
    0, -17, 0, -12, -4, 0, -17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, -21, 0, -18, -8, 2, -8, 0,
    0, 0, -9, 0, 0, 0, 0, -39,
    0, -14, 0, -4, -31, 0, -18, -11,
    0, -3, 0, 0, 0, 0, -3, -13,
    0, -4, -4, -13, -4, -5, 0, 0,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -14, 0, -10,
    0, 0, -17, 0, 0, -8, -16, 0,
    -8, 0, 0, 0, 0, -8, 0, 2,
    2, 3, 2, 0, 0, 0, 0, -24,
    0, 5, 0, 0, 0, 0, -6, 0,
    0, -14, -14, -17, 0, -12, -8, 0,
    -18, 0, -14, -11, 0, -3, -8, 0,
    0, 0, 0, -8, 0, 2, 2, -6,
    2, -1, 7, 24, 31, 0, -34, -10,
    -34, -11, 0, 0, 16, 0, 0, 0,
    0, 28, 0, 41, 28, 20, 37, 0,
    39, -14, -8, 0, -11, 0, -8, 0,
    -4, 0, 0, 7, 0, -4, 0, -10,
    0, 0, 7, -23, 0, 0, 0, 29,
    0, 0, -25, 0, 0, 0, 0, -18,
    0, 0, 0, 0, -10, 0, 0, -12,
    -10, 0, 0, 0, 22, 0, 0, 0,
    0, -4, -4, 0, 8, -10, 0, 0,
    0, -24, 0, 0, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, -17, 0, -8,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    7, -28, 7, 0, 7, 7, -9, 0,
    0, 0, 0, -24, 0, 0, 0, 0,
    -9, 0, 0, -8, -13, 0, -8, 0,
    -8, 0, 0, -15, -10, 0, 0, -6,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -14,
    0, -10, 0, 0, -21, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -37, -17, -37, -24, 15, 15,
    0, -10, 0, -36, 0, 0, 0, 0,
    0, 0, 0, -8, 7, -17, -8, 0,
    -8, 0, 0, 0, -4, 0, 0, 15,
    11, 0, 15, -4, 0, 0, 0, -34,
    0, 5, 0, 0, 0, 0, -9, 0,
    0, 0, 0, -17, 0, -8, 0, 0,
    -14, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 3, -18,
    3, 5, 7, 7, -18, 0, 0, 0,
    0, -10, 0, 0, 0, 0, -4, 0,
    0, -16, -10, 0, -8, 0, 0, 0,
    -8, -14, 0, 0, 0, -12, 0, 0,
    0, 0, 0, -9, -23, -6, -23, -14,
    0, 0, 0, -9, 0, -28, 0, -14,
    0, -7, 0, 0, -10, -8, 0, -14,
    -4, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, -17, 0, 0,
    0, -9, -26, 0, -26, -6, 0, 0,
    0, -4, 0, -21, 0, -17, 0, -7,
    0, -10, -17, 0, 0, -8, -4, 0,
    0, 0, -8, 0, 0, 0, 0, 0,
    0, 0, 0, -13, -10, 0, 0, -16,
    3, -10, -6, 0, 0, 3, 0, 0,
    -8, 0, -4, -23, 0, -11, 0, -8,
    -23, 0, 0, -8, -13, 0, 0, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -23, 0,
    -23, -11, 0, 0, 0, 0, 0, -28,
    0, -14, 0, -4, 0, -4, -6, 0,
    0, -14, -4, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, -10, 0, -17,
    0, 0, 0, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -18,
    0, 0, 0, 0, -21, 0, 0, -17,
    -8, 0, -5, 0, 0, 0, 0, 0,
    -8, -4, 0, 0, -4, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_NS_36 = {
#else
lv_font_t ui_font_NS_36 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 42,          /*The maximum line height required by the font*/
    .base_line = 10,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -4,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_NS_36*/


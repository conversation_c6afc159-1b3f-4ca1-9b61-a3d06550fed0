{"C_Cpp.intelliSenseEngine": "default", "idf.adapterTargetName": "esp32s3", "idf.customExtraPaths": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320", "idf.customExtraVars": {"OPENOCD_SCRIPTS": "D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\share\\openocd\\scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "D:\\Espressif\\tools\\esp-rom-elfs\\20230320"}, "idf.espIdfPath": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "idf.openOcdConfigs": ["${workspaceFolder}/openocd_esp32s3.cfg"], "idf.port": "COM3", "idf.pythonBinPath": "D:\\Espressif\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "idf.toolsPath": "D:\\Espressif", "terminal.integrated.env.windows": {"PATH": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320;%PATH%", "IDF_PATH": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "IDF_TOOLS_PATH": "D:\\Espressif", "OPENOCD_SCRIPTS": "D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\share\\openocd\\scripts"}, "idf.flashBaudRate": "921600", "idf.monitorBaudRate": "115200", "files.associations": {"*.h": "c", "*.c": "c", "*.cpp": "cpp", "*.hpp": "cpp"}, "C_Cpp.default.compilerPath": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "clangd.path": "D:\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=${workspaceFolder}\\build"], "idf.portWin": "COM32", "idf.flashType": "UART", "roo-cline.vsCodeLmModelSelector": {}, "security.workspace.trust.enabled": true, "security.workspace.trust.startupPrompt": "once", "security.workspace.trust.banner": "never"}
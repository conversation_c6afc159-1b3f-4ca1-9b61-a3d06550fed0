{"version": "2.0.0", "tasks": [{"label": "Build - Build project", "type": "shell", "command": "idf.py build", "windows": {"command": "idf.py build", "options": {"env": {"PATH": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320;${env:PATH}", "IDF_PATH": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "IDF_TOOLS_PATH": "D:\\Espressif"}}}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"]}, {"label": "Flash - Flash to device", "type": "shell", "command": "idf.py -p COM21 -b 921600 flash", "windows": {"command": "idf.py -p COM21 -b 921600 flash", "options": {"env": {"PATH": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320;${env:PATH}", "IDF_PATH": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "IDF_TOOLS_PATH": "D:\\Espressif"}}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Monitor - Monitor device", "type": "shell", "command": "idf.py -p COM21 monitor", "windows": {"command": "idf.py -p COM21 monitor", "options": {"env": {"PATH": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320;${env:PATH}", "IDF_PATH": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "IDF_TOOLS_PATH": "D:\\Espressif"}}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Clean - Clean the project", "type": "shell", "command": "idf.py <PERSON><PERSON>an", "windows": {"command": "idf.py <PERSON><PERSON>an", "options": {"env": {"PATH": "D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20230928\\riscv32-esp-elf\\bin;D:\\Espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;D:\\Espressif\\tools\\cmake\\3.30.2\\bin;D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;D:\\Espressif\\tools\\ninja\\1.12.1;D:\\Espressif\\tools\\idf-exe\\1.0.3;D:\\Espressif\\tools\\ccache\\4.8.2\\ccache-4.8.2-windows-x86_64;D:\\Espressif\\tools\\dfu-util\\0.11\\dfu-util-0.11-win64;D:\\Espressif\\tools\\esp-rom-elfs\\20230320;${env:PATH}", "IDF_PATH": "D:\\Espressif\\frameworks\\esp-idf-v5.4.2", "IDF_TOOLS_PATH": "D:\\Espressif"}}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}
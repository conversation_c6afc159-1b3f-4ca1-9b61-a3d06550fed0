/*******************************************************************************
 * Size: 19 px
 * Bpp: 4
 * Opts: --bpp 4 --size 19 --font D:/Fusiontech/GUI/Coffee/assets/NotoSansTC-Regular.ttf -o D:/Fusiontech/GUI/Coffee/assets\ui_font_NS_19.c --format lvgl -r 0x20-0x7f --symbols 沖茶咖啡設定熱水奶泡蒸氣清潔 --no-compress --no-prefilter
 ******************************************************************************/

#include "ui.h"

#ifndef UI_FONT_NS_19
#define UI_FONT_NS_19 1
#endif

#if UI_FONT_NS_19

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xc, 0xe0, 0xc, 0xe0, 0xc, 0xe0, 0xb, 0xd0,
    0xb, 0xd0, 0xa, 0xc0, 0xa, 0xc0, 0x9, 0xc0,
    0x9, 0xb0, 0x9, 0xb0, 0x3, 0x40, 0x8, 0x90,
    0x2f, 0xf4, 0xc, 0xd1,

    /* U+0022 "\"" */
    0x4f, 0x80, 0x8f, 0x44, 0xf8, 0x8, 0xf3, 0x3f,
    0x70, 0x7f, 0x31, 0xf5, 0x6, 0xf1, 0xf, 0x40,
    0x4f, 0x0, 0xa2, 0x2, 0x90,

    /* U+0023 "#" */
    0x0, 0x7, 0xa0, 0x8, 0x90, 0x0, 0xa, 0x70,
    0xb, 0x70, 0x0, 0xc, 0x50, 0xd, 0x40, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x3, 0x4f, 0x44, 0x5f,
    0x43, 0x0, 0x2f, 0x0, 0x3f, 0x0, 0x0, 0x4d,
    0x0, 0x4d, 0x0, 0x0, 0x6c, 0x0, 0x6b, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xf8, 0x3, 0xba, 0x33,
    0xb9, 0x31, 0x0, 0xb6, 0x0, 0xc6, 0x0, 0x0,
    0xc5, 0x0, 0xd4, 0x0, 0x0, 0xe3, 0x0, 0xf2,
    0x0, 0x0, 0xf1, 0x1, 0xf1, 0x0,

    /* U+0024 "$" */
    0x0, 0x4, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x8, 0xef,
    0xe7, 0x0, 0xb, 0xfb, 0x8a, 0xfb, 0x3, 0xf9,
    0x0, 0x3, 0x40, 0x5f, 0x50, 0x0, 0x0, 0x3,
    0xf9, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x10, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x2, 0xaf,
    0xd2, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0x9f, 0x30, 0x0, 0x0, 0x7, 0xf4, 0x57,
    0x0, 0x0, 0xbf, 0x19, 0xfd, 0x98, 0xcf, 0x90,
    0x4, 0xbf, 0xfd, 0x70, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x0, 0x4, 0xf0, 0x0, 0x0,

    /* U+0025 "%" */
    0x1, 0xaf, 0xe8, 0x0, 0x0, 0x4, 0xd0, 0x0,
    0x0, 0xad, 0x24, 0xe7, 0x0, 0x0, 0xd5, 0x0,
    0x0, 0x1f, 0x40, 0x7, 0xe0, 0x0, 0x6c, 0x0,
    0x0, 0x3, 0xf1, 0x0, 0x4f, 0x0, 0xe, 0x30,
    0x0, 0x0, 0x3f, 0x10, 0x4, 0xf0, 0x7, 0xb0,
    0x0, 0x0, 0x1, 0xf3, 0x0, 0x7e, 0x1, 0xe2,
    0x2b, 0xec, 0x30, 0xb, 0xb0, 0x1d, 0x80, 0x99,
    0xd, 0xa2, 0x8f, 0x10, 0x1c, 0xff, 0xa0, 0x2e,
    0x14, 0xf1, 0x0, 0xd7, 0x0, 0x1, 0x10, 0xa,
    0x70, 0x7c, 0x0, 0x9, 0xb0, 0x0, 0x0, 0x3,
    0xe0, 0x8, 0xb0, 0x0, 0x8c, 0x0, 0x0, 0x0,
    0xc6, 0x0, 0x7c, 0x0, 0x9, 0xb0, 0x0, 0x0,
    0x5d, 0x0, 0x4, 0xf0, 0x0, 0xd8, 0x0, 0x0,
    0xd, 0x40, 0x0, 0xd, 0xa2, 0x7f, 0x20, 0x0,
    0x6, 0xb0, 0x0, 0x0, 0x2c, 0xfd, 0x40,

    /* U+0026 "&" */
    0x0, 0x3, 0xcf, 0xe6, 0x0, 0x0, 0x0, 0x1,
    0xec, 0x49, 0xf3, 0x0, 0x0, 0x0, 0x6f, 0x20,
    0xf, 0x60, 0x0, 0x0, 0x7, 0xf0, 0x2, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0x31, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0xec, 0xeb, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf9, 0x0, 0x0, 0x28, 0x20, 0x1d, 0xed,
    0xd0, 0x0, 0x8, 0xf1, 0xc, 0xe1, 0x2f, 0xb0,
    0x0, 0xea, 0x3, 0xf7, 0x0, 0x5f, 0xb0, 0x8f,
    0x20, 0x4f, 0x60, 0x0, 0x6f, 0xcf, 0x90, 0x1,
    0xfc, 0x0, 0x0, 0x9f, 0xf6, 0x0, 0x8, 0xfd,
    0x87, 0xbf, 0xcc, 0xfc, 0x40, 0x6, 0xcf, 0xfc,
    0x60, 0x5, 0xc4,

    /* U+0027 "'" */
    0x4f, 0x84, 0xf8, 0x3f, 0x71, 0xf5, 0xf, 0x40,
    0xa2,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x0, 0xc5, 0x0, 0x5f, 0x10,
    0xc, 0x90, 0x2, 0xf3, 0x0, 0x8e, 0x0, 0xc,
    0xa0, 0x0, 0xf7, 0x0, 0x1f, 0x50, 0x3, 0xf3,
    0x0, 0x3f, 0x30, 0x3, 0xf3, 0x0, 0x2f, 0x40,
    0x1, 0xf5, 0x0, 0xe, 0x80, 0x0, 0xbb, 0x0,
    0x6, 0xf0, 0x0, 0x1f, 0x40, 0x0, 0xab, 0x0,
    0x3, 0xf2, 0x0, 0x9, 0x40,

    /* U+0029 ")" */
    0x0, 0x0, 0x0, 0xe3, 0x0, 0x9, 0xc0, 0x0,
    0x2f, 0x30, 0x0, 0xc9, 0x0, 0x7, 0xe0, 0x0,
    0x3f, 0x30, 0x0, 0xf6, 0x0, 0xe, 0x80, 0x0,
    0xc9, 0x0, 0xc, 0xa0, 0x0, 0xca, 0x0, 0xd,
    0x90, 0x0, 0xe8, 0x0, 0x1f, 0x50, 0x4, 0xf2,
    0x0, 0x8d, 0x0, 0xd, 0x80, 0x4, 0xf1, 0x0,
    0xba, 0x0, 0xa, 0x10, 0x0,

    /* U+002A "*" */
    0x0, 0x2f, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x9e,
    0xcf, 0xcf, 0x70, 0x6e, 0xfd, 0x50, 0x2, 0xfd,
    0xe1, 0x0, 0xba, 0xc, 0x80, 0x2, 0x0, 0x11,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x1,
    0x11, 0x7f, 0x11, 0x11, 0x4f, 0xff, 0xff, 0xff,
    0xfd, 0x14, 0x44, 0x9f, 0x44, 0x43, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x0, 0x0,

    /* U+002C "," */
    0x17, 0x40, 0x8f, 0xf0, 0x4f, 0xf3, 0x2, 0xf1,
    0x9, 0xc0, 0xae, 0x20, 0x62, 0x0,

    /* U+002D "-" */
    0x1, 0x11, 0x11, 0x2f, 0xff, 0xfc, 0x4, 0x44,
    0x43,

    /* U+002E "." */
    0x2b, 0x59, 0xfd, 0x4f, 0x80,

    /* U+002F "/" */
    0x0, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x8, 0xb0,
    0x0, 0x0, 0xd, 0x60, 0x0, 0x0, 0x2f, 0x10,
    0x0, 0x0, 0x7d, 0x0, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x1, 0xf3, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xe, 0x50, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x0, 0x8b, 0x0, 0x0,
    0x0, 0xd6, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0,
    0x7, 0xd0, 0x0, 0x0, 0xb, 0x80, 0x0, 0x0,
    0x1f, 0x30, 0x0, 0x0, 0x5e, 0x0, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x8, 0xef, 0xc4, 0x0, 0x0, 0xbf, 0x97,
    0xdf, 0x40, 0x4, 0xf8, 0x0, 0xe, 0xd0, 0xa,
    0xf1, 0x0, 0x7, 0xf3, 0xd, 0xc0, 0x0, 0x3,
    0xf6, 0xf, 0xa0, 0x0, 0x1, 0xf8, 0xf, 0xa0,
    0x0, 0x1, 0xf9, 0xf, 0xa0, 0x0, 0x1, 0xf9,
    0xf, 0xb0, 0x0, 0x2, 0xf8, 0xd, 0xd0, 0x0,
    0x4, 0xf6, 0x9, 0xf1, 0x0, 0x8, 0xf2, 0x4,
    0xf8, 0x0, 0x1e, 0xd0, 0x0, 0xaf, 0xa7, 0xdf,
    0x40, 0x0, 0x8, 0xef, 0xc4, 0x0,

    /* U+0031 "1" */
    0x1, 0x4a, 0xf8, 0x0, 0x0, 0xbf, 0xff, 0x80,
    0x0, 0x1, 0x14, 0xf8, 0x0, 0x0, 0x0, 0x3f,
    0x80, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x0, 0x3, 0xf8, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0x0, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x28, 0x89,
    0xfc, 0x88, 0x25, 0xff, 0xff, 0xff, 0xf5,

    /* U+0032 "2" */
    0x0, 0x6d, 0xff, 0xb3, 0x0, 0xa, 0xfb, 0x79,
    0xff, 0x30, 0xb, 0x50, 0x0, 0x3f, 0xb0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0x0, 0xf, 0xb0, 0x0, 0x0,
    0x0, 0x6f, 0x50, 0x0, 0x0, 0x1, 0xed, 0x0,
    0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0, 0x8f,
    0x60, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0,
    0x8f, 0x90, 0x0, 0x0, 0xa, 0xfd, 0x77, 0x77,
    0x74, 0x2f, 0xff, 0xff, 0xff, 0xf9,

    /* U+0033 "3" */
    0x0, 0x5c, 0xff, 0xc5, 0x0, 0x9, 0xfb, 0x78,
    0xef, 0x70, 0x3, 0x50, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x1f,
    0xc0, 0x0, 0x1, 0x26, 0xde, 0x20, 0x0, 0xa,
    0xff, 0xe2, 0x0, 0x0, 0x2, 0x58, 0xdf, 0x50,
    0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0,
    0x5, 0xf6, 0x0, 0x0, 0x0, 0x5, 0xf7, 0x1c,
    0x30, 0x0, 0xc, 0xf3, 0x1d, 0xfb, 0x88, 0xdf,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x2, 0xff, 0x10, 0x0, 0x0, 0xc,
    0xff, 0x10, 0x0, 0x0, 0x6f, 0xaf, 0x10, 0x0,
    0x1, 0xfa, 0x8f, 0x10, 0x0, 0xb, 0xf1, 0x8f,
    0x10, 0x0, 0x5f, 0x60, 0x8f, 0x10, 0x0, 0xeb,
    0x0, 0x8f, 0x10, 0x9, 0xf2, 0x0, 0x8f, 0x10,
    0x3f, 0x82, 0x22, 0x9f, 0x31, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0x35, 0x55, 0x55, 0xbf, 0x64, 0x0,
    0x0, 0x0, 0x8f, 0x10, 0x0, 0x0, 0x0, 0x8f,
    0x10, 0x0, 0x0, 0x0, 0x8f, 0x10,

    /* U+0035 "5" */
    0x0, 0xff, 0xff, 0xff, 0xd0, 0x0, 0xfd, 0x88,
    0x88, 0x70, 0x1, 0xf8, 0x0, 0x0, 0x0, 0x2,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xf5, 0x1, 0x0,
    0x0, 0x4, 0xfc, 0xff, 0xe9, 0x0, 0x3, 0xe8,
    0x45, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x0, 0x5, 0xf7, 0x0, 0x0, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x0, 0x6, 0xf6, 0x1b,
    0x20, 0x0, 0x1e, 0xf1, 0x2d, 0xfb, 0x89, 0xef,
    0x60, 0x0, 0x7d, 0xff, 0xb4, 0x0,

    /* U+0036 "6" */
    0x0, 0x2a, 0xff, 0xc5, 0x0, 0x4f, 0xf9, 0x8b,
    0xf3, 0xe, 0xe1, 0x0, 0x2, 0x5, 0xf5, 0x0,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0xd, 0xd3,
    0xcf, 0xfc, 0x20, 0xee, 0xe8, 0x46, 0xee, 0x1e,
    0xf3, 0x0, 0x5, 0xf7, 0xec, 0x0, 0x0, 0xf,
    0xab, 0xe0, 0x0, 0x0, 0xfb, 0x7f, 0x20, 0x0,
    0x1f, 0x92, 0xfb, 0x0, 0x8, 0xf4, 0x7, 0xfc,
    0x79, 0xfb, 0x0, 0x5, 0xdf, 0xe8, 0x0,

    /* U+0037 "7" */
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x8, 0x88, 0x88,
    0x8c, 0xf5, 0x0, 0x0, 0x0, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x9e, 0x10, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0x0,
    0xe, 0xa0, 0x0, 0x0, 0x0, 0x4f, 0x50, 0x0,
    0x0, 0x0, 0x8f, 0x20, 0x0, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0x0, 0x2, 0xfa, 0x0,
    0x0, 0x0, 0x3, 0xf9, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x19, 0xef, 0xd7, 0x0, 0x0, 0xcf, 0x85,
    0xaf, 0x80, 0x5, 0xf6, 0x0, 0xa, 0xf0, 0x7,
    0xf2, 0x0, 0x5, 0xf2, 0x4, 0xf4, 0x0, 0x6,
    0xf0, 0x0, 0xbd, 0x10, 0xb, 0x80, 0x0, 0xd,
    0xf8, 0x7b, 0x0, 0x0, 0xbc, 0x4a, 0xfe, 0x30,
    0x9, 0xe1, 0x0, 0x2d, 0xf2, 0xf, 0x80, 0x0,
    0x3, 0xf8, 0x1f, 0x70, 0x0, 0x0, 0xfa, 0xe,
    0xd0, 0x0, 0x4, 0xf7, 0x5, 0xfd, 0x75, 0x8f,
    0xd1, 0x0, 0x3b, 0xff, 0xe9, 0x10,

    /* U+0039 "9" */
    0x0, 0x4c, 0xff, 0xa2, 0x0, 0x4, 0xfd, 0x77,
    0xee, 0x20, 0xd, 0xe1, 0x0, 0x1e, 0xb0, 0x1f,
    0x80, 0x0, 0x8, 0xf2, 0x2f, 0x70, 0x0, 0x4,
    0xf5, 0xf, 0xb0, 0x0, 0x6, 0xf7, 0xa, 0xf8,
    0x12, 0x8e, 0xf8, 0x0, 0xbf, 0xff, 0xb4, 0xf7,
    0x0, 0x1, 0x42, 0x4, 0xf6, 0x0, 0x0, 0x0,
    0x7, 0xf3, 0x0, 0x0, 0x0, 0xc, 0xe0, 0x2,
    0x30, 0x0, 0x7f, 0x70, 0xa, 0xfa, 0x8b, 0xfb,
    0x0, 0x0, 0x8e, 0xfd, 0x70, 0x0,

    /* U+003A ":" */
    0x4f, 0x89, 0xfd, 0x2b, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0x59, 0xfd, 0x4f,
    0x80,

    /* U+003B ";" */
    0x4f, 0x80, 0x9f, 0xd0, 0x2b, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0x40, 0x8f, 0xf0, 0x4f, 0xf3, 0x2, 0xf1,
    0x9, 0xc0, 0xae, 0x20, 0x62, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0,
    0x6d, 0xfc, 0x0, 0x3, 0x9f, 0xfb, 0x40, 0x5,
    0xcf, 0xd7, 0x10, 0x0, 0x4f, 0xd3, 0x0, 0x0,
    0x0, 0x1b, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x18,
    0xef, 0xc5, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xf8,
    0x0, 0x0, 0x0, 0x2, 0x8c, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003D "=" */
    0x4f, 0xff, 0xff, 0xff, 0xfd, 0x15, 0x55, 0x55,
    0x55, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x4f, 0xff, 0xff, 0xff, 0xfd, 0x14, 0x44,
    0x44, 0x44, 0x43,

    /* U+003E ">" */
    0x37, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x30,
    0x0, 0x0, 0x1, 0x7d, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x4a, 0xff, 0x93, 0x0, 0x0, 0x0, 0x18,
    0xfd, 0x0, 0x0, 0x4, 0xbf, 0xe7, 0x0, 0x28,
    0xef, 0xb5, 0x0, 0x2c, 0xfe, 0x82, 0x0, 0x0,
    0x4c, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x8e, 0xfe, 0x80, 0xd, 0xf9, 0x8d, 0xf8,
    0x5, 0x10, 0x0, 0xee, 0x0, 0x0, 0x0, 0xbf,
    0x0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0xb, 0xe1,
    0x0, 0x0, 0x9f, 0x30, 0x0, 0x4, 0xf6, 0x0,
    0x0, 0x9, 0xf0, 0x0, 0x0, 0x6, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa1, 0x0,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0xb, 0xe3, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x4, 0xae, 0xff, 0xd9, 0x20, 0x0,
    0x0, 0x2, 0xcf, 0x95, 0x33, 0x6c, 0xf7, 0x0,
    0x0, 0x3f, 0xb1, 0x0, 0x0, 0x0, 0x5f, 0x60,
    0x1, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf1,
    0xb, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0x2f, 0x30, 0x0, 0x7e, 0xf9, 0xc6, 0x0, 0x8b,
    0x8d, 0x0, 0x7, 0xf7, 0x4c, 0xf3, 0x0, 0x6d,
    0xc8, 0x0, 0x1f, 0x60, 0x5, 0xf0, 0x0, 0x5e,
    0xe6, 0x0, 0x7e, 0x0, 0x8, 0xc0, 0x0, 0x6d,
    0xe5, 0x0, 0xbb, 0x0, 0xa, 0x90, 0x0, 0xa9,
    0xe6, 0x0, 0xbb, 0x0, 0xd, 0x80, 0x2, 0xf3,
    0xb9, 0x0, 0x7f, 0x31, 0xad, 0xc1, 0x3d, 0xa0,
    0x7e, 0x0, 0xc, 0xff, 0x72, 0xef, 0xf9, 0x0,
    0x1f, 0x80, 0x0, 0x21, 0x0, 0x2, 0x10, 0x0,
    0x6, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xc5, 0x10, 0x14, 0x94, 0x0, 0x0,
    0x0, 0x2, 0xaf, 0xff, 0xfe, 0x92, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x20, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xbd, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xf6, 0xe9,
    0x0, 0x0, 0x0, 0x6, 0xf1, 0x9e, 0x0, 0x0,
    0x0, 0xb, 0xc0, 0x5f, 0x40, 0x0, 0x0, 0x1f,
    0x80, 0xf, 0x90, 0x0, 0x0, 0x6f, 0x30, 0xb,
    0xe0, 0x0, 0x0, 0xbe, 0x22, 0x27, 0xf4, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x6, 0xf7,
    0x55, 0x55, 0xde, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x7f, 0x40, 0x1f, 0xa0, 0x0, 0x0, 0x2f, 0xa0,
    0x6f, 0x50, 0x0, 0x0, 0xd, 0xf0, 0xcf, 0x0,
    0x0, 0x0, 0x8, 0xf4,

    /* U+0042 "B" */
    0x1f, 0xff, 0xff, 0xea, 0x30, 0x1, 0xfd, 0x77,
    0x7a, 0xff, 0x40, 0x1f, 0xa0, 0x0, 0x3, 0xfb,
    0x1, 0xfa, 0x0, 0x0, 0xe, 0xc0, 0x1f, 0xa0,
    0x0, 0x1, 0xfa, 0x1, 0xfb, 0x11, 0x25, 0xdf,
    0x20, 0x1f, 0xff, 0xff, 0xff, 0x60, 0x1, 0xfc,
    0x44, 0x56, 0xbf, 0xb0, 0x1f, 0xa0, 0x0, 0x0,
    0x8f, 0x51, 0xfa, 0x0, 0x0, 0x2, 0xf9, 0x1f,
    0xa0, 0x0, 0x0, 0x3f, 0x91, 0xfa, 0x0, 0x0,
    0xb, 0xf4, 0x1f, 0xd7, 0x77, 0x9e, 0xfa, 0x1,
    0xff, 0xff, 0xfe, 0xb5, 0x0,

    /* U+0043 "C" */
    0x0, 0x5, 0xbf, 0xfd, 0x70, 0x0, 0xb, 0xff,
    0xa9, 0xcf, 0xc0, 0x9, 0xfa, 0x0, 0x0, 0x56,
    0x3, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0x4, 0xd1, 0x0, 0xcf, 0xea, 0x9c, 0xfd, 0x10,
    0x0, 0x6c, 0xff, 0xd7, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xfe, 0xc6, 0x0, 0x1, 0xfd, 0x77,
    0x9e, 0xfd, 0x10, 0x1f, 0xa0, 0x0, 0x8, 0xfc,
    0x1, 0xfa, 0x0, 0x0, 0xa, 0xf5, 0x1f, 0xa0,
    0x0, 0x0, 0x3f, 0xa1, 0xfa, 0x0, 0x0, 0x0,
    0xfd, 0x1f, 0xa0, 0x0, 0x0, 0xe, 0xe1, 0xfa,
    0x0, 0x0, 0x0, 0xee, 0x1f, 0xa0, 0x0, 0x0,
    0xf, 0xd1, 0xfa, 0x0, 0x0, 0x3, 0xfa, 0x1f,
    0xa0, 0x0, 0x0, 0xbf, 0x41, 0xfa, 0x0, 0x0,
    0x9f, 0xc0, 0x1f, 0xd8, 0x8a, 0xef, 0xd1, 0x1,
    0xff, 0xff, 0xec, 0x60, 0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xd8, 0x88,
    0x88, 0x80, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0x0, 0x1f, 0xd8, 0x88, 0x88, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0xa0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xd9, 0x99, 0x99,
    0x91, 0x1f, 0xff, 0xff, 0xff, 0xf2,

    /* U+0046 "F" */
    0x1f, 0xff, 0xff, 0xff, 0xf1, 0xfd, 0x88, 0x88,
    0x88, 0x1f, 0xa0, 0x0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0x1,
    0xfd, 0x88, 0x88, 0x80, 0x1f, 0xa0, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0,
    0x0, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x4, 0xbe, 0xfe, 0xa2, 0x0, 0xa, 0xff,
    0xb9, 0xbf, 0xf3, 0x9, 0xfb, 0x10, 0x0, 0x17,
    0x2, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x50,
    0x0, 0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0xef, 0xff, 0xbd, 0xe0,
    0x0, 0x7, 0x88, 0xfb, 0xcf, 0x0, 0x0, 0x0,
    0xf, 0xb9, 0xf5, 0x0, 0x0, 0x0, 0xfb, 0x3f,
    0xc0, 0x0, 0x0, 0xf, 0xb0, 0xaf, 0xa1, 0x0,
    0x1, 0xfb, 0x0, 0xbf, 0xfb, 0x9b, 0xff, 0x70,
    0x0, 0x5b, 0xef, 0xea, 0x30,

    /* U+0048 "H" */
    0x1f, 0xa0, 0x0, 0x0, 0xd, 0xf1, 0xfa, 0x0,
    0x0, 0x0, 0xdf, 0x1f, 0xa0, 0x0, 0x0, 0xd,
    0xf1, 0xfa, 0x0, 0x0, 0x0, 0xdf, 0x1f, 0xa0,
    0x0, 0x0, 0xd, 0xf1, 0xfd, 0x99, 0x99, 0x99,
    0xef, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xfa,
    0x0, 0x0, 0x0, 0xdf, 0x1f, 0xa0, 0x0, 0x0,
    0xd, 0xf1, 0xfa, 0x0, 0x0, 0x0, 0xdf, 0x1f,
    0xa0, 0x0, 0x0, 0xd, 0xf1, 0xfa, 0x0, 0x0,
    0x0, 0xdf, 0x1f, 0xa0, 0x0, 0x0, 0xd, 0xf1,
    0xfa, 0x0, 0x0, 0x0, 0xdf,

    /* U+0049 "I" */
    0x1f, 0xa1, 0xfa, 0x1f, 0xa1, 0xfa, 0x1f, 0xa1,
    0xfa, 0x1f, 0xa1, 0xfa, 0x1f, 0xa1, 0xfa, 0x1f,
    0xa1, 0xfa, 0x1f, 0xa1, 0xfa,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x6f, 0x50, 0x0, 0x0, 0x6,
    0xf5, 0x0, 0x0, 0x0, 0x6f, 0x50, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0x0, 0x0, 0x6f, 0x50, 0x0,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0x0, 0x6f, 0x50,
    0x0, 0x0, 0x6, 0xf5, 0x0, 0x0, 0x0, 0x6f,
    0x50, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x0, 0x0,
    0x8f, 0x41, 0xd6, 0x0, 0xd, 0xf1, 0xc, 0xfb,
    0x9d, 0xf9, 0x0, 0x8, 0xef, 0xe8, 0x0,

    /* U+004B "K" */
    0x1f, 0xa0, 0x0, 0x0, 0xdf, 0x20, 0x1f, 0xa0,
    0x0, 0xb, 0xf4, 0x0, 0x1f, 0xa0, 0x0, 0x8f,
    0x70, 0x0, 0x1f, 0xa0, 0x5, 0xfa, 0x0, 0x0,
    0x1f, 0xa0, 0x3f, 0xc0, 0x0, 0x0, 0x1f, 0xa1,
    0xef, 0x50, 0x0, 0x0, 0x1f, 0xbc, 0xff, 0xd0,
    0x0, 0x0, 0x1f, 0xff, 0x67, 0xf6, 0x0, 0x0,
    0x1f, 0xf9, 0x0, 0xee, 0x10, 0x0, 0x1f, 0xc0,
    0x0, 0x5f, 0x90, 0x0, 0x1f, 0xa0, 0x0, 0xc,
    0xf2, 0x0, 0x1f, 0xa0, 0x0, 0x3, 0xfc, 0x0,
    0x1f, 0xa0, 0x0, 0x0, 0x9f, 0x50, 0x1f, 0xa0,
    0x0, 0x0, 0x1f, 0xe0,

    /* U+004C "L" */
    0x1f, 0xa0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x1,
    0xfa, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0,
    0x0, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xd9,
    0x99, 0x99, 0x61, 0xff, 0xff, 0xff, 0xfc,

    /* U+004D "M" */
    0x1f, 0xf3, 0x0, 0x0, 0x0, 0xcf, 0x81, 0xff,
    0x90, 0x0, 0x0, 0x2f, 0xf8, 0x1f, 0xde, 0x0,
    0x0, 0x7, 0xee, 0x81, 0xf9, 0xf4, 0x0, 0x0,
    0xd8, 0xf8, 0x1f, 0x7d, 0xa0, 0x0, 0x3f, 0x4f,
    0x81, 0xf7, 0x7f, 0x0, 0x8, 0xd1, 0xf8, 0x1f,
    0x81, 0xf5, 0x0, 0xe7, 0x2f, 0x81, 0xf8, 0xb,
    0xb0, 0x4f, 0x22, 0xf8, 0x1f, 0x80, 0x6f, 0x19,
    0xc0, 0x2f, 0x81, 0xf8, 0x0, 0xf6, 0xe6, 0x2,
    0xf8, 0x1f, 0x80, 0xa, 0xef, 0x10, 0x2f, 0x81,
    0xf8, 0x0, 0x4f, 0xa0, 0x2, 0xf8, 0x1f, 0x80,
    0x0, 0x93, 0x0, 0x2f, 0x81, 0xf8, 0x0, 0x0,
    0x0, 0x2, 0xf8,

    /* U+004E "N" */
    0x1f, 0xe1, 0x0, 0x0, 0xd, 0xd1, 0xff, 0x90,
    0x0, 0x0, 0xdd, 0x1f, 0xdf, 0x20, 0x0, 0xd,
    0xd1, 0xf7, 0xeb, 0x0, 0x0, 0xdd, 0x1f, 0x86,
    0xf4, 0x0, 0xd, 0xd1, 0xf8, 0xd, 0xd0, 0x0,
    0xdd, 0x1f, 0x90, 0x4f, 0x70, 0xd, 0xd1, 0xf9,
    0x0, 0xbf, 0x10, 0xdd, 0x1f, 0x90, 0x2, 0xf9,
    0xd, 0xd1, 0xf9, 0x0, 0x9, 0xf2, 0xcd, 0x1f,
    0x90, 0x0, 0x1e, 0xab, 0xd1, 0xf9, 0x0, 0x0,
    0x6f, 0xdd, 0x1f, 0x90, 0x0, 0x0, 0xdf, 0xd1,
    0xf9, 0x0, 0x0, 0x4, 0xfd,

    /* U+004F "O" */
    0x0, 0x7, 0xcf, 0xfd, 0x70, 0x0, 0x1, 0xcf,
    0xea, 0xad, 0xfd, 0x10, 0xb, 0xf9, 0x0, 0x0,
    0x7f, 0xc0, 0x3f, 0xb0, 0x0, 0x0, 0xa, 0xf5,
    0x9f, 0x40, 0x0, 0x0, 0x2, 0xfb, 0xcf, 0x0,
    0x0, 0x0, 0x0, 0xee, 0xde, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xde, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xcf, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x8f, 0x50,
    0x0, 0x0, 0x3, 0xfa, 0x3f, 0xc0, 0x0, 0x0,
    0xa, 0xf4, 0xa, 0xf9, 0x0, 0x0, 0x8f, 0xc0,
    0x0, 0xcf, 0xea, 0xad, 0xfd, 0x10, 0x0, 0x6,
    0xcf, 0xfd, 0x70, 0x0,

    /* U+0050 "P" */
    0x1f, 0xff, 0xff, 0xda, 0x30, 0x1, 0xfd, 0x77,
    0x8b, 0xff, 0x40, 0x1f, 0xa0, 0x0, 0x2, 0xfd,
    0x1, 0xfa, 0x0, 0x0, 0xb, 0xf0, 0x1f, 0xa0,
    0x0, 0x0, 0xbf, 0x11, 0xfa, 0x0, 0x0, 0x1e,
    0xe0, 0x1f, 0xb2, 0x23, 0x5d, 0xf6, 0x1, 0xff,
    0xff, 0xff, 0xe6, 0x0, 0x1f, 0xc5, 0x54, 0x20,
    0x0, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x1,
    0xfa, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x7, 0xcf, 0xfd, 0x70, 0x0, 0x0, 0x1c,
    0xfe, 0xaa, 0xdf, 0xd1, 0x0, 0xb, 0xf9, 0x0,
    0x0, 0x7f, 0xc0, 0x3, 0xfb, 0x0, 0x0, 0x0,
    0xaf, 0x50, 0x9f, 0x40, 0x0, 0x0, 0x2, 0xfb,
    0xc, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xe0, 0xde,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xd, 0xe0, 0x0,
    0x0, 0x0, 0xd, 0xf0, 0xcf, 0x0, 0x0, 0x0,
    0x0, 0xfe, 0x8, 0xf4, 0x0, 0x0, 0x0, 0x3f,
    0xa0, 0x3f, 0xb0, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0xaf, 0x80, 0x0, 0x7, 0xfc, 0x0, 0x0, 0xcf,
    0xd9, 0x8c, 0xfd, 0x10, 0x0, 0x0, 0x6c, 0xff,
    0xd7, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x10, 0x10,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x3, 0x78, 0x61,

    /* U+0052 "R" */
    0x1f, 0xff, 0xff, 0xeb, 0x40, 0x1, 0xfd, 0x77,
    0x8a, 0xff, 0x50, 0x1f, 0xa0, 0x0, 0x2, 0xfe,
    0x1, 0xfa, 0x0, 0x0, 0xa, 0xf1, 0x1f, 0xa0,
    0x0, 0x0, 0xaf, 0x11, 0xfa, 0x0, 0x0, 0x1e,
    0xe0, 0x1f, 0xb2, 0x22, 0x5d, 0xf6, 0x1, 0xff,
    0xff, 0xff, 0xe7, 0x0, 0x1f, 0xc5, 0x5c, 0xf4,
    0x0, 0x1, 0xfa, 0x0, 0x2f, 0xd0, 0x0, 0x1f,
    0xa0, 0x0, 0x8f, 0x70, 0x1, 0xfa, 0x0, 0x0,
    0xef, 0x10, 0x1f, 0xa0, 0x0, 0x5, 0xfa, 0x1,
    0xfa, 0x0, 0x0, 0xb, 0xf3,

    /* U+0053 "S" */
    0x0, 0x6, 0xdf, 0xfc, 0x60, 0x0, 0xa, 0xfd,
    0xaa, 0xdf, 0xb0, 0x4, 0xfb, 0x0, 0x0, 0x66,
    0x0, 0x6f, 0x50, 0x0, 0x0, 0x0, 0x4, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xd7, 0x0, 0x0, 0x0,
    0x1, 0x7d, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x6, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x70, 0xbc, 0x20, 0x0,
    0xc, 0xf3, 0x7, 0xff, 0xc9, 0xae, 0xf9, 0x0,
    0x2, 0x9e, 0xff, 0xc6, 0x0,

    /* U+0054 "T" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x88, 0x8a,
    0xfc, 0x88, 0x87, 0x0, 0x0, 0x3f, 0x90, 0x0,
    0x0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x90, 0x0, 0x0, 0x0, 0x3, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0, 0x0,
    0x3, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x90,
    0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x90, 0x0, 0x0, 0x0, 0x3, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0,
    0x0, 0x3, 0xf9, 0x0, 0x0,

    /* U+0055 "U" */
    0x2f, 0x90, 0x0, 0x0, 0xd, 0xd2, 0xf9, 0x0,
    0x0, 0x0, 0xdd, 0x2f, 0x90, 0x0, 0x0, 0xd,
    0xd2, 0xf9, 0x0, 0x0, 0x0, 0xdd, 0x2f, 0x90,
    0x0, 0x0, 0xd, 0xd2, 0xf9, 0x0, 0x0, 0x0,
    0xdd, 0x2f, 0x90, 0x0, 0x0, 0xd, 0xd2, 0xf9,
    0x0, 0x0, 0x0, 0xdd, 0x2f, 0xa0, 0x0, 0x0,
    0xd, 0xd0, 0xfb, 0x0, 0x0, 0x0, 0xfc, 0xd,
    0xf0, 0x0, 0x0, 0x3f, 0x90, 0x7f, 0x90, 0x0,
    0x1c, 0xf3, 0x0, 0xdf, 0xda, 0xae, 0xf9, 0x0,
    0x0, 0x8d, 0xff, 0xc6, 0x0,

    /* U+0056 "V" */
    0xdf, 0x0, 0x0, 0x0, 0xf, 0xc8, 0xf4, 0x0,
    0x0, 0x4, 0xf7, 0x3f, 0x90, 0x0, 0x0, 0x9f,
    0x20, 0xde, 0x0, 0x0, 0xe, 0xd0, 0x8, 0xf3,
    0x0, 0x3, 0xf8, 0x0, 0x3f, 0x80, 0x0, 0x7f,
    0x20, 0x0, 0xec, 0x0, 0xc, 0xd0, 0x0, 0x9,
    0xf1, 0x1, 0xf8, 0x0, 0x0, 0x4f, 0x60, 0x6f,
    0x30, 0x0, 0x0, 0xfa, 0xa, 0xe0, 0x0, 0x0,
    0xa, 0xf0, 0xe9, 0x0, 0x0, 0x0, 0x5f, 0x7f,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xb, 0xfa, 0x0, 0x0,

    /* U+0057 "W" */
    0x6f, 0x60, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x9f,
    0x13, 0xf9, 0x0, 0x0, 0xcf, 0x90, 0x0, 0xc,
    0xd0, 0xf, 0xc0, 0x0, 0xf, 0xdd, 0x0, 0x0,
    0xfa, 0x0, 0xcf, 0x0, 0x4, 0xf7, 0xf1, 0x0,
    0x3f, 0x70, 0x8, 0xf2, 0x0, 0x9d, 0x2f, 0x50,
    0x6, 0xf3, 0x0, 0x5f, 0x50, 0xd, 0x90, 0xf9,
    0x0, 0x9f, 0x0, 0x2, 0xf8, 0x1, 0xf5, 0xb,
    0xd0, 0xc, 0xd0, 0x0, 0xe, 0xb0, 0x4f, 0x20,
    0x7f, 0x10, 0xfa, 0x0, 0x0, 0xbe, 0x8, 0xe0,
    0x3, 0xf5, 0x2f, 0x60, 0x0, 0x8, 0xf1, 0xba,
    0x0, 0xf, 0x85, 0xf3, 0x0, 0x0, 0x4f, 0x4f,
    0x60, 0x0, 0xbc, 0x7f, 0x0, 0x0, 0x1, 0xfa,
    0xf2, 0x0, 0x7, 0xfa, 0xc0, 0x0, 0x0, 0xe,
    0xfe, 0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0,
    0xaf, 0xa0, 0x0, 0x0, 0xff, 0x60, 0x0,

    /* U+0058 "X" */
    0x2f, 0xc0, 0x0, 0x0, 0xcf, 0x10, 0x9f, 0x50,
    0x0, 0x5f, 0x70, 0x1, 0xed, 0x0, 0xd, 0xe0,
    0x0, 0x7, 0xf6, 0x5, 0xf5, 0x0, 0x0, 0xd,
    0xe0, 0xdc, 0x0, 0x0, 0x0, 0x5f, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x1f, 0xfd, 0x0, 0x0, 0x0, 0x9, 0xf6, 0xf6,
    0x0, 0x0, 0x2, 0xf8, 0xc, 0xe1, 0x0, 0x0,
    0xbf, 0x10, 0x4f, 0x90, 0x0, 0x4f, 0x80, 0x0,
    0xbf, 0x20, 0xd, 0xe0, 0x0, 0x2, 0xfb, 0x6,
    0xf7, 0x0, 0x0, 0xa, 0xf4,

    /* U+0059 "Y" */
    0xc, 0xf1, 0x0, 0x0, 0xe, 0xd0, 0x4, 0xf8,
    0x0, 0x0, 0x6f, 0x60, 0x0, 0xce, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0x5f, 0x60, 0x4, 0xf6, 0x0,
    0x0, 0xd, 0xd0, 0xb, 0xe0, 0x0, 0x0, 0x5,
    0xf5, 0x3f, 0x60, 0x0, 0x0, 0x0, 0xdc, 0xae,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0,

    /* U+005A "Z" */
    0x6, 0xff, 0xff, 0xff, 0xff, 0x70, 0x38, 0x88,
    0x88, 0x8e, 0xf3, 0x0, 0x0, 0x0, 0x5, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xed, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x40, 0x0, 0x0, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0x0, 0xd, 0xe1, 0x0, 0x0, 0x0,
    0x8, 0xf5, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x10, 0x0, 0x0, 0x0,
    0x8f, 0x50, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0x99, 0x99, 0x99, 0x50,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+005B "[" */
    0xff, 0xfc, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0,
    0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0,
    0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0,
    0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0,
    0xf5, 0x0, 0xf5, 0x0, 0xff, 0xeb, 0x11, 0x11,

    /* U+005C "\\" */
    0x9a, 0x0, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xa, 0x90, 0x0, 0x0,
    0x5, 0xe0, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0xc7, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0x0, 0xe, 0x60, 0x0,
    0x0, 0x9, 0xa0, 0x0, 0x0, 0x4, 0xf0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0xa9, 0x0,
    0x0, 0x0, 0x6d, 0x0, 0x0, 0x0, 0x1f, 0x20,
    0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0x7, 0xc0,
    0x0, 0x0, 0x2, 0xf1,

    /* U+005D "]" */
    0x5f, 0xff, 0x60, 0x0, 0xe6, 0x0, 0xe, 0x60,
    0x0, 0xe6, 0x0, 0xe, 0x60, 0x0, 0xe6, 0x0,
    0xe, 0x60, 0x0, 0xe6, 0x0, 0xe, 0x60, 0x0,
    0xe6, 0x0, 0xe, 0x60, 0x0, 0xe6, 0x0, 0xe,
    0x60, 0x0, 0xe6, 0x0, 0xe, 0x60, 0x0, 0xe6,
    0x0, 0xe, 0x60, 0x0, 0xe6, 0x5e, 0xef, 0x60,
    0x11, 0x10,

    /* U+005E "^" */
    0x0, 0x4, 0x81, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x4f, 0xbc, 0x0, 0x0, 0xa, 0xc3,
    0xf2, 0x0, 0x0, 0xf6, 0xd, 0x90, 0x0, 0x6f,
    0x10, 0x8e, 0x0, 0xc, 0xb0, 0x2, 0xf5, 0x2,
    0xf5, 0x0, 0xc, 0xb0, 0x8f, 0x0, 0x0, 0x6f,
    0x10,

    /* U+005F "_" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x52, 0x33, 0x33,
    0x33, 0x33, 0x31,

    /* U+0060 "`" */
    0x0, 0x0, 0x0, 0xc8, 0x0, 0xb, 0xf5, 0x0,
    0xb, 0xf3, 0x0, 0xb, 0xb0, 0x0, 0x1,

    /* U+0061 "a" */
    0x2, 0x9e, 0xfe, 0x80, 0x3, 0xfe, 0x98, 0xcf,
    0x80, 0x6, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0,
    0x9, 0xf1, 0x0, 0x5, 0x9b, 0xef, 0x20, 0x7f,
    0xea, 0x6b, 0xf2, 0x7f, 0x80, 0x0, 0x8f, 0x2d,
    0xd0, 0x0, 0x8, 0xf2, 0xde, 0x0, 0x2, 0xdf,
    0x28, 0xfb, 0x79, 0xfc, 0xf2, 0x9, 0xef, 0xb3,
    0x4f, 0x20,

    /* U+0062 "b" */
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0x0, 0x4f, 0x74, 0xcf, 0xe9, 0x0, 0x4f, 0xdf,
    0xa8, 0xcf, 0xb0, 0x4f, 0xd2, 0x0, 0xb, 0xf4,
    0x4f, 0x80, 0x0, 0x4, 0xf9, 0x4f, 0x80, 0x0,
    0x1, 0xfb, 0x4f, 0x80, 0x0, 0x0, 0xfc, 0x4f,
    0x80, 0x0, 0x2, 0xfa, 0x4f, 0x80, 0x0, 0x6,
    0xf7, 0x4f, 0xb0, 0x0, 0x1e, 0xf1, 0x4f, 0xee,
    0x99, 0xef, 0x60, 0x4f, 0x37, 0xef, 0xc4, 0x0,

    /* U+0063 "c" */
    0x0, 0x5, 0xcf, 0xfb, 0x20, 0x0, 0x8f, 0xe9,
    0x9e, 0xb0, 0x4, 0xfc, 0x0, 0x1, 0x10, 0xb,
    0xf2, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0xf, 0xd0,
    0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0,
    0x5, 0xfb, 0x0, 0x0, 0x30, 0x0, 0xaf, 0xd8,
    0x9e, 0xe0, 0x0, 0x6, 0xdf, 0xfa, 0x20,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0x0, 0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0xb,
    0xf0, 0x0, 0x7d, 0xfd, 0x6a, 0xf0, 0x9, 0xfd,
    0x89, 0xff, 0xf0, 0x5f, 0xc0, 0x0, 0x1d, 0xf0,
    0xbf, 0x20, 0x0, 0xb, 0xf0, 0xee, 0x0, 0x0,
    0xb, 0xf0, 0xfc, 0x0, 0x0, 0xb, 0xf0, 0xfd,
    0x0, 0x0, 0xb, 0xf0, 0xcf, 0x10, 0x0, 0xb,
    0xf0, 0x7f, 0xa0, 0x0, 0x3f, 0xf0, 0xd, 0xfc,
    0x8a, 0xfd, 0xf0, 0x1, 0x9e, 0xfc, 0x37, 0xf0,

    /* U+0065 "e" */
    0x0, 0x6, 0xdf, 0xe8, 0x0, 0x0, 0x9f, 0xc7,
    0x9f, 0xb0, 0x4, 0xf9, 0x0, 0x6, 0xf4, 0xb,
    0xf0, 0x0, 0x0, 0xf9, 0xf, 0xc0, 0x0, 0x0,
    0xdb, 0xf, 0xff, 0xff, 0xff, 0xfb, 0xf, 0xc3,
    0x33, 0x33, 0x32, 0xb, 0xf0, 0x0, 0x0, 0x0,
    0x5, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc8,
    0x7a, 0xe1, 0x0, 0x5, 0xcf, 0xfc, 0x50,

    /* U+0066 "f" */
    0x0, 0x9, 0xff, 0x80, 0x8, 0xfa, 0x75, 0x0,
    0xee, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0xfc,
    0x0, 0x6, 0xff, 0xff, 0xf0, 0x26, 0xfd, 0x66,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0xf, 0xc0, 0x0,

    /* U+0067 "g" */
    0x0, 0x1a, 0xef, 0xff, 0xff, 0x40, 0x1e, 0xe7,
    0x5b, 0xfa, 0x61, 0x7, 0xf4, 0x0, 0xd, 0xc0,
    0x0, 0xaf, 0x0, 0x0, 0x9f, 0x0, 0x8, 0xf2,
    0x0, 0xc, 0xd0, 0x0, 0x1e, 0xc2, 0x7, 0xf7,
    0x0, 0x0, 0x8f, 0xff, 0xf7, 0x0, 0x0, 0x3f,
    0x31, 0x20, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xc8, 0x77, 0x74, 0x0, 0x0,
    0xce, 0xff, 0xff, 0xfc, 0x0, 0x8d, 0x0, 0x0,
    0x1a, 0xf5, 0xf, 0x80, 0x0, 0x0, 0x4f, 0x60,
    0xfb, 0x0, 0x0, 0xa, 0xf2, 0x9, 0xfa, 0x54,
    0x5c, 0xf6, 0x0, 0x6, 0xcf, 0xfe, 0xa3, 0x0,

    /* U+0068 "h" */
    0x4f, 0x80, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0,
    0x0, 0x4f, 0x80, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x4, 0xf7,
    0x2b, 0xfe, 0x90, 0x4f, 0xbf, 0xb9, 0xef, 0x74,
    0xff, 0x40, 0x1, 0xfd, 0x4f, 0x80, 0x0, 0xc,
    0xf4, 0xf8, 0x0, 0x0, 0xbf, 0x4f, 0x80, 0x0,
    0xb, 0xf4, 0xf8, 0x0, 0x0, 0xbf, 0x4f, 0x80,
    0x0, 0xb, 0xf4, 0xf8, 0x0, 0x0, 0xbf, 0x4f,
    0x80, 0x0, 0xb, 0xf4, 0xf8, 0x0, 0x0, 0xbf,

    /* U+0069 "i" */
    0x4f, 0x87, 0xfb, 0x5, 0x10, 0x0, 0x0, 0x4,
    0xf8, 0x4f, 0x84, 0xf8, 0x4f, 0x84, 0xf8, 0x4f,
    0x84, 0xf8, 0x4f, 0x84, 0xf8, 0x4f, 0x84, 0xf8,

    /* U+006A "j" */
    0x0, 0x4f, 0x80, 0x7, 0xfb, 0x0, 0x5, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xf8, 0x0,
    0x3f, 0x80, 0x3, 0xf8, 0x0, 0x3f, 0x80, 0x3,
    0xf8, 0x0, 0x3f, 0x80, 0x3, 0xf8, 0x0, 0x3f,
    0x80, 0x3, 0xf8, 0x0, 0x3f, 0x80, 0x3, 0xf8,
    0x0, 0x3f, 0x80, 0x3, 0xf7, 0x0, 0x5f, 0x65,
    0x7d, 0xf2, 0xaf, 0xe5, 0x0,

    /* U+006B "k" */
    0x4f, 0x70, 0x0, 0x0, 0x0, 0x4f, 0x70, 0x0,
    0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0x0, 0x4f, 0x70, 0x0, 0x7f, 0x70, 0x4f, 0x70,
    0x3, 0xfa, 0x0, 0x4f, 0x70, 0x1e, 0xd0, 0x0,
    0x4f, 0x70, 0xbf, 0x20, 0x0, 0x4f, 0x77, 0xf9,
    0x0, 0x0, 0x4f, 0xbf, 0xef, 0x10, 0x0, 0x4f,
    0xfb, 0x2f, 0xa0, 0x0, 0x4f, 0xd1, 0x9, 0xf3,
    0x0, 0x4f, 0x70, 0x1, 0xec, 0x0, 0x4f, 0x70,
    0x0, 0x6f, 0x60, 0x4f, 0x70, 0x0, 0xd, 0xe1,

    /* U+006C "l" */
    0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80,
    0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80,
    0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80, 0x4f, 0x80,
    0x4f, 0x80, 0x3f, 0x80, 0x2f, 0xd3, 0xa, 0xf7,

    /* U+006D "m" */
    0x4f, 0x33, 0xcf, 0xe6, 0x1, 0xaf, 0xe8, 0x4,
    0xf9, 0xfb, 0xaf, 0xf5, 0xec, 0x9e, 0xf6, 0x4f,
    0xf4, 0x0, 0x5f, 0xf7, 0x0, 0x2f, 0xc4, 0xf8,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0xdf, 0x4f, 0x80,
    0x0, 0xf, 0xb0, 0x0, 0xc, 0xf4, 0xf8, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0xcf, 0x4f, 0x80, 0x0,
    0xf, 0xb0, 0x0, 0xc, 0xf4, 0xf8, 0x0, 0x0,
    0xfb, 0x0, 0x0, 0xcf, 0x4f, 0x80, 0x0, 0xf,
    0xb0, 0x0, 0xc, 0xf4, 0xf8, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0xcf, 0x4f, 0x80, 0x0, 0xf, 0xb0,
    0x0, 0xc, 0xf0,

    /* U+006E "n" */
    0x4f, 0x32, 0xbf, 0xe9, 0x4, 0xf9, 0xfb, 0x9e,
    0xf7, 0x4f, 0xf4, 0x0, 0x1f, 0xd4, 0xf8, 0x0,
    0x0, 0xcf, 0x4f, 0x80, 0x0, 0xb, 0xf4, 0xf8,
    0x0, 0x0, 0xbf, 0x4f, 0x80, 0x0, 0xb, 0xf4,
    0xf8, 0x0, 0x0, 0xbf, 0x4f, 0x80, 0x0, 0xb,
    0xf4, 0xf8, 0x0, 0x0, 0xbf, 0x4f, 0x80, 0x0,
    0xb, 0xf0,

    /* U+006F "o" */
    0x0, 0x6, 0xcf, 0xea, 0x20, 0x0, 0x9, 0xfd,
    0x8a, 0xff, 0x30, 0x5, 0xfb, 0x0, 0x3, 0xfd,
    0x0, 0xbf, 0x20, 0x0, 0x9, 0xf3, 0xf, 0xd0,
    0x0, 0x0, 0x5f, 0x70, 0xfc, 0x0, 0x0, 0x3,
    0xf8, 0xf, 0xd0, 0x0, 0x0, 0x5f, 0x70, 0xbf,
    0x20, 0x0, 0x9, 0xf4, 0x5, 0xfa, 0x0, 0x3,
    0xfd, 0x0, 0xa, 0xfd, 0x89, 0xff, 0x30, 0x0,
    0x6, 0xdf, 0xea, 0x20, 0x0,

    /* U+0070 "p" */
    0x4f, 0x34, 0xcf, 0xe9, 0x0, 0x4f, 0xcf, 0xa8,
    0xcf, 0xb0, 0x4f, 0xd2, 0x0, 0xb, 0xf4, 0x4f,
    0x80, 0x0, 0x4, 0xf9, 0x4f, 0x80, 0x0, 0x1,
    0xfb, 0x4f, 0x80, 0x0, 0x0, 0xfc, 0x4f, 0x80,
    0x0, 0x2, 0xfa, 0x4f, 0x80, 0x0, 0x6, 0xf7,
    0x4f, 0xb0, 0x0, 0x1e, 0xf1, 0x4f, 0xfe, 0x99,
    0xef, 0x60, 0x4f, 0x77, 0xef, 0xc4, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x4f, 0x80, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7d, 0xfd, 0x77, 0xf0, 0x9, 0xfd, 0x89,
    0xff, 0xf0, 0x5f, 0xc0, 0x0, 0x1d, 0xf0, 0xbf,
    0x20, 0x0, 0xb, 0xf0, 0xee, 0x0, 0x0, 0xb,
    0xf0, 0xfc, 0x0, 0x0, 0xb, 0xf0, 0xfd, 0x0,
    0x0, 0xb, 0xf0, 0xcf, 0x10, 0x0, 0xb, 0xf0,
    0x7f, 0xa0, 0x0, 0x3f, 0xf0, 0xd, 0xfc, 0x8a,
    0xfe, 0xf0, 0x1, 0x9e, 0xfc, 0x3a, 0xf0, 0x0,
    0x0, 0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0xb,
    0xf0, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0xb, 0xf0,

    /* U+0072 "r" */
    0x4f, 0x34, 0xdf, 0x54, 0xf7, 0xfc, 0xa2, 0x4f,
    0xf7, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x4, 0xf8, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x4,
    0xf8, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x6d, 0xfe, 0xa2, 0x0, 0x6f, 0xb6, 0x8e,
    0xa0, 0xc, 0xe0, 0x0, 0x1, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x4, 0xfe, 0x81, 0x0, 0x0, 0x2,
    0xbf, 0xfa, 0x10, 0x0, 0x0, 0x29, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xf2, 0x6, 0x0, 0x0, 0x9f,
    0x12, 0xfe, 0x86, 0x9f, 0xb0, 0x2, 0xae, 0xfe,
    0x90, 0x0,

    /* U+0074 "t" */
    0x0, 0xbc, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0xdc, 0x0, 0x7, 0xff, 0xff, 0xf9, 0x36, 0xfd,
    0x66, 0x30, 0xf, 0xc0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0x9f, 0xb7, 0x60, 0x1, 0xaf,
    0xf9,

    /* U+0075 "u" */
    0x6f, 0x50, 0x0, 0xe, 0xc6, 0xf5, 0x0, 0x0,
    0xec, 0x6f, 0x50, 0x0, 0xe, 0xc6, 0xf5, 0x0,
    0x0, 0xec, 0x6f, 0x50, 0x0, 0xe, 0xc6, 0xf5,
    0x0, 0x0, 0xec, 0x6f, 0x50, 0x0, 0xe, 0xc5,
    0xf6, 0x0, 0x0, 0xfc, 0x3f, 0xb0, 0x0, 0xbf,
    0xc0, 0xdf, 0xb9, 0xdb, 0xcc, 0x2, 0xcf, 0xe8,
    0xa, 0xc0,

    /* U+0076 "v" */
    0x9f, 0x30, 0x0, 0x3, 0xf7, 0x3f, 0x80, 0x0,
    0x8, 0xf2, 0xe, 0xd0, 0x0, 0xd, 0xd0, 0x9,
    0xf2, 0x0, 0x2f, 0x70, 0x3, 0xf7, 0x0, 0x7f,
    0x20, 0x0, 0xeb, 0x0, 0xcd, 0x0, 0x0, 0x8f,
    0x11, 0xf7, 0x0, 0x0, 0x3f, 0x55, 0xf2, 0x0,
    0x0, 0xd, 0xaa, 0xd0, 0x0, 0x0, 0x8, 0xef,
    0x80, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,

    /* U+0077 "w" */
    0x5f, 0x60, 0x0, 0x4f, 0xa0, 0x0, 0xf, 0xa1,
    0xfa, 0x0, 0x8, 0xfe, 0x0, 0x4, 0xf6, 0xd,
    0xe0, 0x0, 0xda, 0xf2, 0x0, 0x8f, 0x10, 0x9f,
    0x20, 0x1f, 0x4f, 0x60, 0xc, 0xd0, 0x5, 0xf5,
    0x5, 0xf0, 0xca, 0x0, 0xf9, 0x0, 0x1f, 0x90,
    0x9c, 0x8, 0xe0, 0x3f, 0x50, 0x0, 0xcd, 0xd,
    0x80, 0x4f, 0x27, 0xf1, 0x0, 0x8, 0xf1, 0xf4,
    0x0, 0xf6, 0xad, 0x0, 0x0, 0x4f, 0x8f, 0x0,
    0xc, 0xae, 0x90, 0x0, 0x0, 0xfe, 0xc0, 0x0,
    0x8e, 0xf5, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x4,
    0xff, 0x10, 0x0,

    /* U+0078 "x" */
    0x2f, 0xb0, 0x0, 0x2f, 0x90, 0x8, 0xf4, 0x0,
    0xbf, 0x10, 0x0, 0xed, 0x3, 0xf7, 0x0, 0x0,
    0x5f, 0x6b, 0xd0, 0x0, 0x0, 0xc, 0xff, 0x50,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x1f,
    0xcf, 0x60, 0x0, 0x0, 0xae, 0x1c, 0xf1, 0x0,
    0x3, 0xf7, 0x3, 0xfa, 0x0, 0xd, 0xe0, 0x0,
    0x9f, 0x40, 0x6f, 0x60, 0x0, 0x1e, 0xd0,

    /* U+0079 "y" */
    0x9f, 0x30, 0x0, 0x2, 0xf7, 0x3f, 0x80, 0x0,
    0x7, 0xf2, 0xd, 0xe0, 0x0, 0xc, 0xd0, 0x7,
    0xf3, 0x0, 0x1f, 0x80, 0x1, 0xf9, 0x0, 0x6f,
    0x30, 0x0, 0xbe, 0x0, 0xbe, 0x0, 0x0, 0x5f,
    0x30, 0xf9, 0x0, 0x0, 0xf, 0x83, 0xf4, 0x0,
    0x0, 0xa, 0xe8, 0xe0, 0x0, 0x0, 0x4, 0xfe,
    0x90, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x8, 0xaf,
    0xa0, 0x0, 0x0, 0x2f, 0xe9, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xc, 0xff, 0xff, 0xff, 0x40, 0x57, 0x77, 0x8f,
    0xe0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0x0, 0x2f, 0xc0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0x77, 0x77, 0x73, 0x5f, 0xff, 0xff,
    0xff, 0x70,

    /* U+007B "{" */
    0x0, 0xb, 0xfc, 0x0, 0x7f, 0x30, 0x0, 0x9d,
    0x0, 0x0, 0xac, 0x0, 0x0, 0x9d, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x7d, 0x0,
    0x1, 0xdb, 0x0, 0x4f, 0xe2, 0x0, 0x4, 0xe9,
    0x0, 0x0, 0x8d, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x9d, 0x0, 0x0, 0xac, 0x0,
    0x0, 0xac, 0x0, 0x0, 0x8f, 0x20, 0x0, 0x1d,
    0xfb, 0x0, 0x0, 0x11,

    /* U+007C "|" */
    0xf, 0x20, 0xf2, 0xf, 0x20, 0xf2, 0xf, 0x20,
    0xf2, 0xf, 0x20, 0xf2, 0xf, 0x20, 0xf2, 0xf,
    0x20, 0xf2, 0xf, 0x20, 0xf2, 0xf, 0x20, 0xf2,
    0xf, 0x20, 0xf2, 0xf, 0x20, 0xf2, 0xf, 0x20,
    0xf2,

    /* U+007D "}" */
    0x5f, 0xe4, 0x0, 0x1, 0xbe, 0x0, 0x0, 0x6f,
    0x0, 0x0, 0x5f, 0x10, 0x0, 0x6f, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0x9, 0xfb, 0x0, 0x2f,
    0x82, 0x0, 0x6f, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x6f, 0x10,
    0x0, 0x6f, 0x10, 0x0, 0xae, 0x0, 0x5f, 0xf6,
    0x0, 0x1, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc2,
    0x0, 0x21, 0xd, 0xa4, 0xce, 0x52, 0xd8, 0x7,
    0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x23,
    0x0,

    /* U+5496 "咖" */
    0x0, 0x0, 0x0, 0xe, 0x30, 0x0, 0x0, 0x0,
    0x3, 0x65, 0x55, 0x0, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xde, 0xe0, 0xf, 0x30, 0x1, 0x88,
    0x88, 0x59, 0x90, 0x4e, 0x12, 0xf5, 0x22, 0x3f,
    0x99, 0xda, 0x99, 0x4, 0xea, 0xff, 0xff, 0xf8,
    0xf0, 0x9, 0xa9, 0x90, 0x4e, 0x13, 0xf4, 0x2f,
    0x8f, 0x0, 0x9a, 0x99, 0x4, 0xe0, 0x1f, 0x20,
    0xf7, 0xf0, 0x9, 0xa9, 0x90, 0x4e, 0x2, 0xf1,
    0xf, 0x7f, 0x0, 0x9a, 0x99, 0x4, 0xe0, 0x3f,
    0x0, 0xf6, 0xf0, 0x9, 0xa9, 0x90, 0x4e, 0x5,
    0xe0, 0x1f, 0x5f, 0x0, 0x9a, 0x99, 0x4, 0xe0,
    0x7c, 0x2, 0xf5, 0xf0, 0x9, 0xa9, 0xa3, 0x7e,
    0x9, 0xa0, 0x3f, 0x4f, 0x0, 0x9a, 0x9f, 0xff,
    0xd0, 0xd6, 0x4, 0xf3, 0xf0, 0x9, 0xa9, 0x90,
    0x0, 0x2f, 0x20, 0x6e, 0x3f, 0x0, 0x9a, 0x77,
    0x0, 0x8, 0xc0, 0x8, 0xc3, 0xff, 0xff, 0xa0,
    0x0, 0x1, 0xf5, 0x44, 0xe9, 0x3f, 0x33, 0xaa,
    0x0, 0x0, 0xab, 0xc, 0xfd, 0x22, 0xd0, 0x6,
    0x70, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5561 "啡" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70, 0xbb,
    0x0, 0x0, 0x58, 0x88, 0x82, 0x0, 0xf, 0x70,
    0xbb, 0x0, 0x0, 0x9e, 0xcc, 0xf5, 0x0, 0xf,
    0x70, 0xbb, 0x0, 0x0, 0x9a, 0x0, 0xe5, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xf1, 0x9a, 0x0, 0xe5,
    0x55, 0x5f, 0x70, 0xbd, 0x55, 0x50, 0x9a, 0x0,
    0xe5, 0x0, 0xf, 0x70, 0xbb, 0x0, 0x0, 0x9a,
    0x0, 0xe5, 0x0, 0xf, 0x70, 0xbb, 0x0, 0x0,
    0x9a, 0x0, 0xe5, 0xdf, 0xff, 0x70, 0xbf, 0xff,
    0xe0, 0x9a, 0x0, 0xe5, 0x55, 0x5f, 0x70, 0xbd,
    0x55, 0x50, 0x9a, 0x0, 0xe5, 0x0, 0xf, 0x60,
    0xbb, 0x0, 0x0, 0x9d, 0x99, 0xf5, 0x0, 0x1f,
    0x50, 0xbb, 0x0, 0x0, 0x9e, 0xbb, 0xb6, 0x7b,
    0xff, 0x30, 0xbf, 0xff, 0xf5, 0x9a, 0x0, 0xa,
    0xf9, 0xaf, 0x0, 0xbd, 0x55, 0x51, 0x55, 0x0,
    0x1, 0x0, 0xda, 0x0, 0xbb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf3, 0x0, 0xbb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe7, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+5976 "奶" */
    0x0, 0x4e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0x0, 0x4, 0x88, 0x88, 0x88,
    0x83, 0x0, 0x0, 0x8b, 0x5, 0x17, 0xde, 0xfd,
    0xdd, 0xf3, 0x0, 0x0, 0xa8, 0xe, 0x50, 0x7,
    0xf0, 0x4, 0xf0, 0x0, 0x5d, 0xff, 0xff, 0xf4,
    0x7, 0xe0, 0x8, 0xd0, 0x0, 0x38, 0xf7, 0x5f,
    0x60, 0x8, 0xe0, 0xb, 0xa0, 0x0, 0x3, 0xf0,
    0xf, 0x30, 0x9, 0xd0, 0xe, 0x82, 0x31, 0x6,
    0xc0, 0xf, 0x20, 0xa, 0xb0, 0x2f, 0xff, 0xf8,
    0xa, 0x90, 0x2f, 0x10, 0xc, 0xa0, 0x3, 0x33,
    0xe7, 0xe, 0x50, 0x5e, 0x0, 0xe, 0x70, 0x0,
    0x0, 0xf6, 0xd, 0xc1, 0xaa, 0x0, 0x1f, 0x40,
    0x0, 0x0, 0xf5, 0x1, 0xcd, 0xf5, 0x0, 0x5f,
    0x10, 0x0, 0x1, 0xf4, 0x0, 0xc, 0xf3, 0x0,
    0xac, 0x0, 0x0, 0x3, 0xf2, 0x0, 0x1f, 0xde,
    0x21, 0xf7, 0x0, 0x0, 0x5, 0xf1, 0x0, 0xcc,
    0xb, 0xa9, 0xf1, 0x0, 0x0, 0x8, 0xe0, 0xb,
    0xe1, 0x0, 0x6f, 0x70, 0x1, 0x55, 0x5e, 0xa0,
    0x3d, 0x20, 0x0, 0xba, 0x0, 0x0, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x7, 0xf6,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0xf7, 0x0,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x70, 0x7, 0xf1, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x40, 0xf7, 0x0, 0x12, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x3, 0x10, 0x0, 0x0, 0x11, 0x11,
    0x8f, 0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0x30, 0x7, 0xf6, 0x66, 0x66,
    0x40, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x7f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x7,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xcf,
    0x30, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0xaf, 0x78, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf6, 0x0, 0x8f, 0xff, 0x97, 0x76, 0x66,
    0x67, 0x1, 0xea, 0x0, 0x0, 0x17, 0xbd, 0xff,
    0xff, 0xff, 0xd0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C23 "氣" */
    0x0, 0x0, 0x1d, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x1, 0xed, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0xef, 0x50, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x0, 0x7, 0x64, 0x44, 0x44, 0x44, 0x44, 0x45,
    0x40, 0x0, 0x0, 0x3, 0xdd, 0xde, 0xfd, 0xdd,
    0xdd, 0xef, 0x10, 0x0, 0x0, 0xb, 0x60, 0x3f,
    0x10, 0x7a, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x5f,
    0x23, 0xf1, 0x2f, 0x40, 0x4f, 0x10, 0x0, 0x0,
    0x0, 0x94, 0x3f, 0x19, 0x70, 0x4, 0xf1, 0x0,
    0x0, 0x6d, 0xdd, 0xde, 0xfe, 0xdd, 0xdd, 0x3f,
    0x20, 0x0, 0x2, 0x44, 0x45, 0x6f, 0x55, 0x44,
    0x42, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xe4, 0xf2,
    0xc9, 0x0, 0xf, 0x60, 0x62, 0x0, 0x8, 0xf4,
    0x3f, 0x12, 0xdc, 0x10, 0xca, 0xa, 0x60, 0x3c,
    0xf4, 0x3, 0xf1, 0x0, 0xbd, 0x6, 0xf8, 0xe4,
    0x3, 0xb2, 0x0, 0x3f, 0x10, 0x0, 0x40, 0x9,
    0xfc, 0x0,

    /* U+6C34 "水" */
    0x0, 0x0, 0x0, 0x0, 0x1e, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf7, 0x0, 0x0, 0x20, 0x0, 0x1, 0x11,
    0x11, 0x20, 0x1f, 0x70, 0x0, 0x1e, 0xa0, 0x0,
    0xaf, 0xff, 0xff, 0xa1, 0xfc, 0x0, 0xc, 0xe2,
    0x0, 0x3, 0x55, 0x56, 0xf8, 0x1f, 0xf3, 0xb,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x41, 0xff,
    0xca, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf0,
    0x1f, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf9, 0x1, 0xf7, 0x9e, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x30, 0x1f, 0x70, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x1, 0xf7, 0x4, 0xf9,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x1f, 0x70,
    0x7, 0xf9, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x1,
    0xf7, 0x0, 0x8, 0xfb, 0x10, 0xa, 0xfa, 0x0,
    0x0, 0x1f, 0x70, 0x0, 0x7, 0xff, 0x40, 0xa9,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x4, 0xc1,
    0x0, 0x0, 0x0, 0x78, 0x9f, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfe, 0xb1, 0x0,
    0x0, 0x0, 0x0,

    /* U+6C96 "沖" */
    0x0, 0xa5, 0x0, 0x0, 0x0, 0x7, 0xd0, 0x0,
    0x0, 0x1, 0x9f, 0xd4, 0x0, 0x0, 0x8, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0xcd, 0x0, 0x0, 0x8,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x14, 0x44,
    0x4a, 0xf4, 0x44, 0x42, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x8, 0x40, 0x0,
    0x5f, 0x11, 0x18, 0xf1, 0x11, 0xbb, 0x1b, 0xfc,
    0x40, 0x5f, 0x0, 0x8, 0xe0, 0x0, 0xab, 0x0,
    0x3c, 0xd0, 0x5f, 0x0, 0x8, 0xe0, 0x0, 0xab,
    0x0, 0x0, 0x10, 0x5f, 0x0, 0x8, 0xe0, 0x0,
    0xab, 0x0, 0x0, 0x0, 0x5f, 0x55, 0x5a, 0xf5,
    0x55, 0xcb, 0x0, 0x0, 0x43, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xda, 0x5f, 0x11,
    0x18, 0xf1, 0x11, 0xbb, 0x0, 0x7, 0xf2, 0x13,
    0x0, 0x8, 0xe0, 0x0, 0x0, 0x0, 0x2f, 0x80,
    0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x0, 0xce,
    0x0, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x6,
    0xf5, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0,
    0x2, 0x80, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0,
    0x0,

    /* U+6CE1 "泡" */
    0x0, 0x83, 0x0, 0x0, 0x2c, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xfa, 0x10, 0x8, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xea, 0x0, 0xed,
    0x66, 0x66, 0x66, 0x73, 0x0, 0x0, 0x1, 0x10,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0xf7, 0x0,
    0x64, 0x0, 0xc, 0xf4, 0x11, 0x11, 0x10, 0xf,
    0x70, 0xc, 0xfc, 0x26, 0xfe, 0xff, 0xff, 0xfe,
    0x0, 0xf6, 0x0, 0x4, 0xdb, 0x3, 0xac, 0x22,
    0x28, 0xe0, 0xf, 0x50, 0x0, 0x0, 0x10, 0xa,
    0xb0, 0x0, 0x6e, 0x1, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xab, 0x0, 0x6, 0xe0, 0x2f, 0x40, 0x0,
    0x0, 0x71, 0xa, 0xfe, 0xee, 0xfe, 0x4, 0xf2,
    0x0, 0x0, 0x2f, 0x60, 0xad, 0x66, 0x66, 0x83,
    0xaf, 0x0, 0x0, 0xa, 0xe0, 0xa, 0xb0, 0x0,
    0x7, 0xff, 0xa0, 0x0, 0x3, 0xf6, 0x0, 0xab,
    0x0, 0x0, 0x2, 0x10, 0x40, 0x0, 0xcd, 0x0,
    0xa, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0x40, 0x6f,
    0x50, 0x0, 0x9e, 0x65, 0x55, 0x55, 0x59, 0xf1,
    0x3, 0x90, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xe7, 0x0,

    /* U+6E05 "清" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,
    0x0, 0x0, 0x1a, 0x20, 0x0, 0x0, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x2, 0xef, 0x53, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x1, 0xcf, 0x32,
    0x22, 0x22, 0xf9, 0x22, 0x22, 0x10, 0x0, 0x0,
    0x70, 0x12, 0x22, 0x2f, 0x92, 0x22, 0x20, 0x0,
    0x0, 0x0, 0xa, 0xee, 0xee, 0xff, 0xee, 0xee,
    0x30, 0x4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x0, 0x0, 0x2, 0xed, 0x40, 0xce, 0xee, 0xee,
    0xff, 0xee, 0xee, 0xe2, 0x1, 0x8f, 0x22, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x0, 0x0, 0x10,
    0x0, 0xcf, 0xee, 0xee, 0xee, 0xea, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x93, 0x33, 0x33, 0x3c, 0xb0,
    0x0, 0x0, 0x18, 0x0, 0xe8, 0x11, 0x11, 0x11,
    0xbb, 0x0, 0x0, 0x7, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0xe8, 0x0, 0xe7,
    0x0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x8e, 0x10,
    0xf, 0xed, 0xdd, 0xdd, 0xdf, 0xb0, 0x0, 0x1f,
    0x70, 0x4, 0xf5, 0x33, 0x33, 0x33, 0xcb, 0x0,
    0xa, 0xe0, 0x0, 0xbe, 0x0, 0x0, 0x2, 0x2c,
    0xb0, 0x0, 0x54, 0x0, 0xa, 0x70, 0x0, 0x0,
    0xef, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6F54 "潔" */
    0x0, 0x50, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf9, 0x2, 0x48, 0xe4, 0x43, 0x88,
    0x88, 0x96, 0x0, 0x3e, 0x97, 0xcd, 0xfc, 0xc4,
    0x8d, 0xc8, 0xcb, 0x0, 0x3, 0x31, 0x27, 0xe2,
    0x20, 0xb, 0x70, 0x9a, 0x0, 0x0, 0x5, 0xce,
    0xfc, 0xb0, 0x1f, 0x30, 0xb9, 0x3, 0x10, 0x3,
    0x59, 0xe8, 0x91, 0x9d, 0x0, 0xd7, 0xc, 0xe3,
    0x7, 0x9b, 0xe5, 0x38, 0xf5, 0x8e, 0xf3, 0x0,
    0xbf, 0x40, 0x5, 0xd0, 0x86, 0x60, 0x13, 0x10,
    0x0, 0xa, 0x20, 0x0, 0x2d, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfc, 0x43, 0x8f,
    0x50, 0x0, 0x0, 0x1, 0x0, 0xbe, 0xdd, 0xff,
    0xa2, 0x28, 0x0, 0x0, 0xc, 0x60, 0x2, 0x8e,
    0xa4, 0x33, 0x5f, 0xa0, 0x0, 0x2f, 0x22, 0xdf,
    0xff, 0xff, 0xdc, 0xba, 0xe8, 0x0, 0x9c, 0x0,
    0x32, 0x40, 0x3f, 0x20, 0x30, 0x33, 0x1, 0xf6,
    0x0, 0x1b, 0xe1, 0x3f, 0x23, 0xea, 0x10, 0x7,
    0xe0, 0x7, 0xfa, 0x10, 0x3f, 0x20, 0x1b, 0xe4,
    0x8, 0x80, 0x6, 0x40, 0xe, 0xfc, 0x0, 0x0,
    0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+71B1 "熱" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x1, 0xdd, 0xef, 0xdd, 0xa0,
    0x0, 0xf3, 0x0, 0x0, 0x0, 0x3, 0x37, 0xf3,
    0x33, 0x17, 0x7f, 0x97, 0x72, 0x0, 0x2, 0x22,
    0x7f, 0x22, 0x24, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x60, 0x2f, 0x10, 0xf4,
    0x0, 0x0, 0xc, 0x60, 0xf2, 0x0, 0x33, 0xf0,
    0xf, 0x40, 0x0, 0x6, 0xe1, 0xd, 0xcb, 0x8f,
    0xce, 0x0, 0xf4, 0x0, 0xb, 0xb2, 0x28, 0x2,
    0x21, 0x4f, 0xe1, 0xf, 0x40, 0x0, 0x13, 0x37,
    0xf3, 0x32, 0x0, 0xcf, 0xe3, 0xf4, 0x0, 0x5,
    0xee, 0xff, 0xee, 0xd0, 0x1f, 0x4b, 0x8f, 0x49,
    0x20, 0x0, 0x5, 0xf0, 0x0, 0x8, 0xd0, 0x0,
    0xf4, 0xb4, 0x4, 0x56, 0xaf, 0xbc, 0xea, 0xf4,
    0x0, 0xf, 0x8d, 0x20, 0xdd, 0xca, 0x98, 0x65,
    0xd7, 0x0, 0x0, 0x8d, 0xb0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x2,
    0xf6, 0x5, 0xe0, 0x6, 0xf0, 0x4, 0xf5, 0x0,
    0x0, 0xbd, 0x0, 0x4f, 0x10, 0x1f, 0x60, 0x9,
    0xe1, 0x0, 0x8f, 0x30, 0x2, 0xf3, 0x0, 0xcb,
    0x0, 0xe, 0xa0, 0x3, 0x40, 0x0, 0x4, 0x0,
    0x2, 0x10, 0x0, 0x33, 0x0,

    /* U+8336 "茶" */
    0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xe0,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x44, 0x44, 0xf9,
    0x44, 0x4, 0x47, 0xf6, 0x44, 0x30, 0x0, 0x0,
    0xf, 0x70, 0x1c, 0x50, 0x4f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x21, 0x1d, 0xfc, 0x20, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0xd2, 0xbf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0x90, 0x0,
    0x6f, 0xd5, 0x0, 0x0, 0x0, 0x6c, 0xfc, 0x30,
    0x3d, 0x20, 0x19, 0xfe, 0x82, 0x0, 0xcf, 0xb4,
    0x0, 0x4, 0xf3, 0x0, 0x1, 0x8e, 0xd1, 0x1,
    0x16, 0x66, 0x66, 0x9f, 0x86, 0x66, 0x65, 0x1,
    0x0, 0x0, 0xee, 0xee, 0xee, 0xfe, 0xee, 0xee,
    0xd0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x4f, 0x30,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf6, 0x4,
    0xf3, 0xb, 0xe4, 0x0, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x4f, 0x30, 0x9, 0xf8, 0x0, 0x0, 0x1a,
    0xf9, 0x0, 0x4, 0xf3, 0x0, 0x6, 0xfa, 0x0,
    0x6, 0xe5, 0x0, 0x0, 0x4f, 0x30, 0x0, 0x4,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x0,

    /* U+84B8 "蒸" */
    0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x3,
    0xf1, 0x0, 0x0, 0xc, 0xdd, 0xdf, 0xed, 0xc0,
    0xdd, 0xef, 0xdd, 0xdb, 0x0, 0x56, 0x66, 0xfa,
    0x65, 0x6, 0x68, 0xf7, 0x66, 0x50, 0x0, 0x0,
    0x9, 0x40, 0x0, 0x0, 0x2a, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x55, 0x55, 0x55, 0x5d, 0xf8,
    0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x0, 0x6d,
    0xd3, 0x1, 0x30, 0x0, 0x5, 0xff, 0xff, 0xf9,
    0x3f, 0xe1, 0x2, 0xdd, 0x0, 0x0, 0x1, 0x11,
    0x8f, 0x23, 0xfc, 0xd7, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x50, 0x3f, 0x38, 0xfb, 0x10, 0x0,
    0x0, 0x4, 0xce, 0x41, 0x79, 0xf2, 0x4, 0xdf,
    0xa5, 0x10, 0xb, 0xf9, 0x10, 0x9, 0x96, 0x0,
    0x0, 0x4a, 0xf8, 0x0, 0x21, 0xe, 0xee, 0xee,
    0xee, 0xee, 0xec, 0x0, 0x0, 0x0, 0x0, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x41, 0x0, 0x0, 0x2,
    0xe4, 0x7, 0x80, 0x6, 0xd0, 0x9, 0xe2, 0x0,
    0x0, 0xcd, 0x0, 0x7f, 0x0, 0x1f, 0x70, 0xb,
    0xe2, 0x0, 0xaf, 0x20, 0x4, 0xf2, 0x0, 0x9c,
    0x0, 0xc, 0xc0, 0x1, 0x30, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+8A2D "設" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa7, 0x0, 0x0, 0x15, 0x44,
    0x44, 0x20, 0x0, 0x0, 0x6, 0xf2, 0x0, 0x3,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xa, 0x30,
    0x0, 0x3f, 0x20, 0xc, 0x90, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0x14, 0xf0, 0x0, 0xc9, 0x0, 0x0,
    0x33, 0x33, 0x33, 0x30, 0x8e, 0x0, 0xc, 0x90,
    0x0, 0x1, 0x33, 0x33, 0x31, 0x1e, 0x90, 0x0,
    0xba, 0x22, 0x0, 0x4e, 0xee, 0xee, 0x7d, 0xe1,
    0x0, 0x7, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x2,
    0xd3, 0x0, 0x0, 0x1, 0x11, 0x0, 0x28, 0x88,
    0x88, 0x36, 0x66, 0x66, 0x66, 0x65, 0x0, 0x3,
    0x99, 0x99, 0x94, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x3, 0x33, 0x33, 0x11, 0xf5, 0x0, 0x0,
    0xe9, 0x0, 0x5, 0xff, 0xff, 0xf5, 0x9, 0xe1,
    0x0, 0x8f, 0x10, 0x0, 0x5f, 0x0, 0xf, 0x50,
    0xd, 0xb0, 0x5f, 0x50, 0x0, 0x5, 0xf0, 0x0,
    0xf5, 0x0, 0x2e, 0xdf, 0x80, 0x0, 0x0, 0x5f,
    0x0, 0xf, 0x50, 0x1, 0xbf, 0xf5, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf5, 0x39, 0xfd, 0x49, 0xfd,
    0x72, 0x0, 0x5f, 0x44, 0x44, 0x8f, 0xb5, 0x0,
    0x2, 0x9e, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 68, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 98, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 28, .adv_w = 144, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 49, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 119, .adv_w = 169, .box_w = 9, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 205, .adv_w = 280, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 324, .adv_w = 207, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 415, .adv_w = 85, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 424, .adv_w = 103, .box_w = 5, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 477, .adv_w = 103, .box_w = 5, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 530, .adv_w = 142, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 555, .adv_w = 169, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 605, .adv_w = 85, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 619, .adv_w = 105, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 628, .adv_w = 85, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 633, .adv_w = 119, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 709, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 779, .adv_w = 169, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 842, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 912, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 982, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1052, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1122, .adv_w = 169, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1185, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1255, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1325, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1395, .adv_w = 85, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1412, .adv_w = 85, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1442, .adv_w = 169, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1492, .adv_w = 169, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1527, .adv_w = 169, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1577, .adv_w = 144, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1633, .adv_w = 288, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1777, .adv_w = 185, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1861, .adv_w = 200, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1938, .adv_w = 194, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2015, .adv_w = 209, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2092, .adv_w = 179, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2162, .adv_w = 168, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2225, .adv_w = 209, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2302, .adv_w = 221, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2379, .adv_w = 89, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2400, .adv_w = 163, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2463, .adv_w = 196, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2547, .adv_w = 165, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2610, .adv_w = 247, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2701, .adv_w = 220, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2778, .adv_w = 226, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2862, .adv_w = 192, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2939, .adv_w = 226, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3056, .adv_w = 193, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3133, .adv_w = 181, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3210, .adv_w = 182, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3287, .adv_w = 219, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3364, .adv_w = 175, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3441, .adv_w = 267, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3560, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3637, .adv_w = 161, .box_w = 12, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3721, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3798, .adv_w = 103, .box_w = 4, .box_h = 20, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 3838, .adv_w = 119, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3914, .adv_w = 103, .box_w = 5, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3964, .adv_w = 169, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 4005, .adv_w = 170, .box_w = 11, .box_h = 2, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4016, .adv_w = 184, .box_w = 5, .box_h = 6, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 4031, .adv_w = 171, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4081, .adv_w = 188, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4161, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4216, .adv_w = 188, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4296, .adv_w = 168, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4351, .adv_w = 99, .box_w = 7, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4407, .adv_w = 171, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4495, .adv_w = 185, .box_w = 9, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4567, .adv_w = 84, .box_w = 3, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4591, .adv_w = 84, .box_w = 5, .box_h = 21, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 4644, .adv_w = 168, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4724, .adv_w = 86, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4756, .adv_w = 282, .box_w = 15, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4839, .adv_w = 185, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4889, .adv_w = 184, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4950, .adv_w = 188, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5030, .adv_w = 188, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5110, .adv_w = 118, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5149, .adv_w = 142, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5199, .adv_w = 115, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5248, .adv_w = 185, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5298, .adv_w = 158, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5353, .adv_w = 244, .box_w = 15, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5436, .adv_w = 151, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5491, .adv_w = 158, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5571, .adv_w = 144, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5621, .adv_w = 103, .box_w = 6, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5681, .adv_w = 82, .box_w = 3, .box_h = 22, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 5714, .adv_w = 103, .box_w = 6, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5774, .adv_w = 169, .box_w = 10, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 5799, .adv_w = 304, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5952, .adv_w = 304, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6123, .adv_w = 304, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6285, .adv_w = 304, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6466, .adv_w = 304, .box_w = 19, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6628, .adv_w = 304, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6799, .adv_w = 304, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6952, .adv_w = 304, .box_w = 19, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7114, .adv_w = 304, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7295, .adv_w = 304, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7457, .adv_w = 304, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7638, .adv_w = 304, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7809, .adv_w = 304, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7990, .adv_w = 304, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xcb, 0x4e0, 0x704, 0x178d, 0x179e, 0x1800, 0x184b,
    0x196f, 0x1abe, 0x1d1b, 0x2ea0, 0x3022, 0x3597
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 21654, .range_length = 13720, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 14, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -40, 0, -40, 0,
    0, 0, 0, -19, 0, -33, -4, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -11, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 26, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -33, 0, -47,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -34, -8, -23, -12, 0,
    -32, 0, 0, 0, -5, 0, 0, 0,
    8, 0, 0, -16, 0, -12, -8, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    -7, -17, 0, -7, -4, -10, -23, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, -4, -28, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    -12, 0, -4, 8, 8, 0, 0, 2,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, -18, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -19, 0, -33,
    0, 0, 0, 0, 0, 0, -10, -3,
    -4, 0, 0, -19, -6, -5, 0, 1,
    -5, -3, -15, 7, 0, -4, 0, 0,
    0, 0, 7, -5, -3, -3, -2, -2,
    -3, 0, 0, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -5, -9, 0, -3, -2, -2, -5, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -5, -4, -4, -5, 0,
    0, 0, 0, 0, 0, -10, 0, 0,
    0, 0, 0, 0, -10, -4, -9, -7,
    -5, -2, -2, -2, -3, -4, 0, 0,
    0, 0, -8, 0, 0, 0, 0, -10,
    -4, -5, -4, 0, -5, 0, 0, 0,
    0, -12, 0, 0, 0, -7, 0, 0,
    0, -4, 0, -14, 0, -9, 0, -4,
    -3, -7, -8, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, -9, 0, -4, 0, -11,
    -4, 0, 0, 0, 0, 0, -25, 0,
    -25, -25, 0, 0, 0, -14, -4, -48,
    -8, 0, 0, 1, 1, -9, 0, -11,
    0, -12, -5, 0, -9, 0, 0, -8,
    -8, -4, -6, -8, -6, -10, -6, -11,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -8,
    0, -5, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, -9, 0, 0, 0,
    0, 0, 0, -15, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, -15,
    0, -11, 0, 0, 0, 0, -3, -4,
    -8, 0, -4, -7, -5, -5, -4, 0,
    -6, 0, 0, 0, -3, 0, 0, 0,
    -4, 0, 0, -12, -6, -8, -6, -6,
    -8, -5, 0, -30, 0, -52, 0, -19,
    0, 0, 0, 0, -12, 0, -10, 0,
    -9, -42, -10, -27, -20, 0, -26, 0,
    -28, 0, -5, -5, -2, 0, 0, 0,
    0, -8, -4, -13, -12, 0, -13, 0,
    0, 0, 0, 0, -39, -12, -39, -27,
    0, 0, 0, -18, 0, -51, -4, -9,
    0, 0, 0, -9, -4, -29, 0, -16,
    -9, 0, -11, 0, 0, 0, -4, 0,
    0, 0, 0, -5, 0, -8, 0, 0,
    0, -4, 0, -11, 0, 0, 0, 0,
    0, -2, 0, -7, -5, -5, 0, 1,
    1, -2, -1, -4, 0, -2, -4, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -3, 0, 0, 0, -6,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, -5, -5, -8, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -37, -26,
    -37, -32, -8, -8, 0, -15, -9, -44,
    -15, 0, 0, 0, 0, -8, -5, -19,
    0, -26, -23, -7, -26, 0, 0, -17,
    -21, -7, -17, -12, -12, -15, -12, -26,
    0, 0, 0, 0, -6, 0, -6, -12,
    0, 0, 0, -6, 0, -17, -4, 0,
    0, -2, 0, -4, -5, 0, 0, -2,
    0, 0, -4, 0, 0, 0, -2, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, -23, -7, -23, -17, 0, 0,
    0, -5, -4, -26, -4, 0, -4, 2,
    0, 0, 0, -7, 0, -8, -6, 0,
    -8, 0, 0, -8, -5, 0, -11, -4,
    -4, -6, -4, -9, 0, 0, 0, 0,
    -12, -4, -12, -11, 0, 0, 0, 0,
    -3, -23, -3, 0, 0, 0, 0, 0,
    0, -3, 0, -6, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -4, 0, -4, 0, -10,
    0, 0, 0, 0, 0, 0, -7, -1,
    -5, -8, -4, 0, 0, 0, 0, 0,
    0, -4, -3, -6, 0, 0, 0, 0,
    0, -6, -4, -6, -5, -4, -6, -5,
    0, 0, 0, 0, -31, -23, -31, -24,
    -9, -9, -3, -5, -5, -35, -6, -5,
    -4, 0, 0, 0, 0, -10, 0, -24,
    -15, 0, -21, 0, 0, -15, -15, -10,
    -12, -5, -9, -12, -5, -17, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, -3, -8, -12, -11, 0, -4, -3,
    -3, 0, -5, -6, 0, -6, -8, -8,
    -6, 0, 0, 0, 0, -5, -9, -6,
    -6, -9, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -30, -11, -19, -11, 0,
    -26, 0, 0, 0, 0, 0, 10, 0,
    24, 0, 0, 0, 0, -8, -4, 0,
    4, 0, 0, 0, 0, -19, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, -9, 0, -6, -2, 0, -9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, -11, 0, -10, -4, 1, -4, 0,
    0, 0, -5, 0, 0, 0, 0, -20,
    0, -7, 0, -2, -16, 0, -10, -6,
    0, -2, 0, 0, 0, 0, -2, -7,
    0, -2, -2, -7, -2, -3, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, -5,
    0, 0, -9, 0, 0, -4, -8, 0,
    -4, 0, 0, 0, 0, -4, 0, 1,
    1, 2, 1, 0, 0, 0, 0, -12,
    0, 2, 0, 0, 0, 0, -3, 0,
    0, -8, -8, -9, 0, -6, -4, 0,
    -10, 0, -8, -6, 0, -2, -4, 0,
    0, 0, 0, -4, 0, 1, 1, -3,
    1, -1, 4, 13, 16, 0, -18, -5,
    -18, -6, 0, 0, 8, 0, 0, 0,
    0, 15, 0, 22, 15, 10, 19, 0,
    20, -8, -4, 0, -6, 0, -4, 0,
    -2, 0, 0, 4, 0, -2, 0, -5,
    0, 0, 4, -12, 0, 0, 0, 16,
    0, 0, -13, 0, 0, 0, 0, -10,
    0, 0, 0, 0, -5, 0, 0, -6,
    -5, 0, 0, 0, 12, 0, 0, 0,
    0, -2, -2, 0, 4, -5, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, -9, 0, -4,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    4, -15, 4, 0, 4, 4, -5, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    -5, 0, 0, -4, -7, 0, -4, 0,
    -4, 0, 0, -8, -5, 0, 0, -3,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, -5, 0, 0, -11, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -19, -9, -19, -12, 8, 8,
    0, -5, 0, -19, 0, 0, 0, 0,
    0, 0, 0, -4, 4, -9, -4, 0,
    -4, 0, 0, 0, -2, 0, 0, 8,
    6, 0, 8, -2, 0, 0, 0, -18,
    0, 2, 0, 0, 0, 0, -5, 0,
    0, 0, 0, -9, 0, -4, 0, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 2, -10,
    2, 2, 4, 4, -10, 0, 0, 0,
    0, -5, 0, 0, 0, 0, -2, 0,
    0, -8, -5, 0, -4, 0, 0, 0,
    -4, -8, 0, 0, 0, -6, 0, 0,
    0, 0, 0, -5, -12, -3, -12, -8,
    0, 0, 0, -5, 0, -15, 0, -8,
    0, -4, 0, 0, -5, -4, 0, -8,
    -2, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, -5, -14, 0, -14, -3, 0, 0,
    0, -2, 0, -11, 0, -9, 0, -4,
    0, -5, -9, 0, 0, -4, -2, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, -7, -5, 0, 0, -9,
    2, -5, -3, 0, 0, 2, 0, 0,
    -4, 0, -2, -12, 0, -6, 0, -4,
    -12, 0, 0, -4, -7, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -12, 0,
    -12, -6, 0, 0, 0, 0, 0, -15,
    0, -8, 0, -2, 0, -2, -3, 0,
    0, -8, -2, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, -5, 0, -9,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, -11, 0, 0, -9,
    -4, 0, -3, 0, 0, 0, 0, 0,
    -4, -2, 0, 0, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_NS_19 = {
#else
lv_font_t ui_font_NS_19 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 24,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_NS_19*/


// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#ifndef _TEAFFIC_UI_H
#define _TEAFFIC_UI_H

#ifdef __cplusplus
extern "C" {
#endif

#if defined __has_include
#if __has_include("lvgl.h")
#include "lvgl.h"
#elif __has_include("lvgl/lvgl.h")
#include "lvgl/lvgl.h"
#else
#include "lvgl.h"
#endif
#else
#include "lvgl.h"
#endif

#include "ui_helpers.h"
#include "ui_events.h"

// SCREEN: ui_SC_PWR_ON
void ui_SC_PWR_ON_screen_init(void);
extern lv_obj_t * ui_SC_PWR_ON;
extern lv_obj_t * ui_BTN_PWR;
extern lv_obj_t * ui_Label1;
extern lv_obj_t * ui_PWR_ARC;
extern lv_obj_t * ui_Lab_wait_heater;
extern lv_obj_t * ui_Bar_Wait_Heater;
extern lv_obj_t * ui_Lab_Build_Time;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_PWR_ON;
extern lv_obj_t * uic_BTN_PWR;
extern lv_obj_t * uic_PWR_ARC;
extern lv_obj_t * uic_Lab_wait_heater;
extern lv_obj_t * uic_Bar_Wait_Heater;
extern lv_obj_t * uic_Lab_Build_Time;

// SCREEN: ui_SC_MAIN_MENU
void ui_SC_MAIN_MENU_screen_init(void);
extern lv_obj_t * ui_SC_MAIN_MENU;
extern lv_obj_t * ui_Label2;
extern lv_obj_t * ui_Panel1;
void ui_event_BTN_TEA(lv_event_t * e);
extern lv_obj_t * ui_BTN_TEA;
extern lv_obj_t * ui_Lab_Tea;
void ui_event_BTN_Coffee(lv_event_t * e);
extern lv_obj_t * ui_BTN_Coffee;
extern lv_obj_t * ui_Lab_Coffee;
void ui_event_BTN_SETTING(lv_event_t * e);
extern lv_obj_t * ui_BTN_SETTING;
extern lv_obj_t * ui_Lab_Setting;
void ui_event_BTN_WATER(lv_event_t * e);
extern lv_obj_t * ui_BTN_WATER;
extern lv_obj_t * ui_Lab_Water;
extern lv_obj_t * ui_BTN_Steam;
extern lv_obj_t * ui_Lab_Steam;
extern lv_obj_t * ui_BTN_Clean;
extern lv_obj_t * ui_Lab_Clean;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_MAIN_MENU;
extern lv_obj_t * uic_Lab_Tea;
extern lv_obj_t * uic_Lab_Setting;
extern lv_obj_t * uic_BTN_WATER;
extern lv_obj_t * uic_Lab_Water;
extern lv_obj_t * uic_BTN_Steam;
extern lv_obj_t * uic_Lab_Steam;
extern lv_obj_t * uic_BTN_Clean;
extern lv_obj_t * uic_Lab_Clean;

// SCREEN: ui_SC_TEA
void ui_SC_TEA_screen_init(void);
extern lv_obj_t * ui_SC_TEA;
extern lv_obj_t * ui_Lab_Tea1;
void ui_event_LAB_BACK_MENU(lv_event_t * e);
extern lv_obj_t * ui_LAB_BACK_MENU;
extern lv_obj_t * ui_Panel2;
extern lv_obj_t * ui_BTN_SMALL_TEA;
extern lv_obj_t * ui_Lab_Small_Tea;
extern lv_obj_t * ui_BTN_BIG_TEA;
extern lv_obj_t * ui_LAB_Big_Tea;
// CUSTOM VARIABLES
extern lv_obj_t * uic_LAB_BACK_MENU;
extern lv_obj_t * uic_BTN_SMALL_TEA;
extern lv_obj_t * uic_Lab_Small_Tea;

// SCREEN: ui_SC_Coffee
void ui_SC_Coffee_screen_init(void);
extern lv_obj_t * ui_SC_Coffee;
extern lv_obj_t * ui_LAB_Coffee1;
void ui_event_LAB_BACK_MENU1(lv_event_t * e);
extern lv_obj_t * ui_LAB_BACK_MENU1;
extern lv_obj_t * ui_Panel3;
void ui_event_BTN_SMALL_Coffee(lv_event_t * e);
extern lv_obj_t * ui_BTN_SMALL_Coffee;
extern lv_obj_t * ui_Lab_Small_Coffee;
void ui_event_BTN_BIG_Coffee(lv_event_t * e);
extern lv_obj_t * ui_BTN_BIG_Coffee;
extern lv_obj_t * ui_Lab_Big_Coffee;
// CUSTOM VARIABLES
extern lv_obj_t * uic_BTN_SMALL_Coffee;
extern lv_obj_t * uic_Lab_Small_Coffee;
extern lv_obj_t * uic_BTN_BIG_Coffee;
extern lv_obj_t * uic_Lab_Big_Coffee;

// SCREEN: ui_SC_Setting
void ui_SC_Setting_screen_init(void);
extern lv_obj_t * ui_SC_Setting;
extern lv_obj_t * ui_Lab_Setting1;
void ui_event_LAB_BACK_MENU2(lv_event_t * e);
extern lv_obj_t * ui_LAB_BACK_MENU2;
extern lv_obj_t * ui_Panel4;
void ui_event_BTN_DECA(lv_event_t * e);
extern lv_obj_t * ui_BTN_DECA;
extern lv_obj_t * ui_Lab_DeCa1;
void ui_event_BTN_set_Language(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_Language;
extern lv_obj_t * ui_Lab_Langauge1;
void ui_event_BTN_BIG_TEA1(lv_event_t * e);
extern lv_obj_t * ui_BTN_BIG_TEA1;
extern lv_obj_t * ui_Lab_Service1;
// CUSTOM VARIABLES
extern lv_obj_t * uic_Lab_Setting1;
extern lv_obj_t * uic_Lab_DeCa1;

// SCREEN: ui_SC_Small_Tea
void ui_SC_Small_Tea_screen_init(void);
extern lv_obj_t * ui_SC_Small_Tea;
extern lv_obj_t * ui_Lab_Tea2;
extern lv_obj_t * ui_Lab_Small_Tea1;
extern lv_obj_t * ui_Image2;
extern lv_obj_t * ui_BAR_Small_tea;
extern lv_obj_t * ui_Lab_Brewing;
extern lv_obj_t * ui_LAB__Small_Tea_progress;
extern lv_obj_t * ui_Lab_Wait1;
extern lv_obj_t * ui_BTN_Cancel1;
extern lv_obj_t * ui_Lab_Cancel1;
// CUSTOM VARIABLES
extern lv_obj_t * uic_Lab_Small_Tea1;
extern lv_obj_t * uic_BAR_Small_tea;
extern lv_obj_t * uic_Lab_Brewing;
extern lv_obj_t * uic_LAB__Small_Tea_progress;
extern lv_obj_t * uic_Lab_Wait1;
extern lv_obj_t * uic_BTN_Cancel1;

// SCREEN: ui_SC_Big_Tea
void ui_SC_Big_Tea_screen_init(void);
extern lv_obj_t * ui_SC_Big_Tea;
extern lv_obj_t * ui_Lab_Tea3;
extern lv_obj_t * ui_LAB_Big_Tea1;
extern lv_obj_t * ui_Image1;
extern lv_obj_t * ui_BAR_Big_tea;
extern lv_obj_t * ui_Lab_Brewing1;
extern lv_obj_t * ui_LAB__Big_Tea_progress;
extern lv_obj_t * ui_Lab_Wait2;
extern lv_obj_t * ui_BTN_Cancel2;
extern lv_obj_t * ui_Lab_Cancel2;
// CUSTOM VARIABLES
extern lv_obj_t * uic_BAR_Big_tea;
extern lv_obj_t * uic_LAB__Big_Tea_progress;

// SCREEN: ui_SC_Small_Coffee
void ui_SC_Small_Coffee_screen_init(void);
extern lv_obj_t * ui_SC_Small_Coffee;
extern lv_obj_t * ui_LAB_Coffee2;
extern lv_obj_t * ui_Lab_Small_Coffee1;
extern lv_obj_t * ui_Image3;
extern lv_obj_t * ui_BAR_Small_Coffee;
extern lv_obj_t * ui_Lab_Brewing2;
extern lv_obj_t * ui_LAB__Small_Coffee_progress;
extern lv_obj_t * ui_Lab_Wait3;
extern lv_obj_t * ui_BTN_Cancel3;
extern lv_obj_t * ui_Lab_Cancel3;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_Small_Coffee;
extern lv_obj_t * uic_BAR_Small_Coffee;
extern lv_obj_t * uic_LAB__Small_Coffee_progress;

// SCREEN: ui_SC_Big_Coffee
void ui_SC_Big_Coffee_screen_init(void);
extern lv_obj_t * ui_SC_Big_Coffee;
extern lv_obj_t * ui_LAB_Coffee3;
extern lv_obj_t * ui_Lab_Big_Coffee1;
extern lv_obj_t * ui_Image4;
extern lv_obj_t * ui_BAR_Big_Coffee;
extern lv_obj_t * ui_Lab_Brewing3;
extern lv_obj_t * ui_LAB__Big_Coffee_progress;
extern lv_obj_t * ui_Lab_Wait4;
extern lv_obj_t * ui_BTN_Cancel4;
extern lv_obj_t * ui_Lab_Cancel4;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_Big_Coffee;
extern lv_obj_t * uic_BAR_Big_Coffee;

// SCREEN: ui_SC_Language
void ui_SC_Language_screen_init(void);
extern lv_obj_t * ui_SC_Language;
extern lv_obj_t * ui_Lab_Langauge;
extern lv_obj_t * ui_Panel5;
extern lv_obj_t * ui_BTN_set_Chinese;
extern lv_obj_t * ui_Lab_Chinese;
extern lv_obj_t * ui_BTN_English;
extern lv_obj_t * ui_Lab_English;
void ui_event_LAB_BACK_MENU4(lv_event_t * e);
extern lv_obj_t * ui_LAB_BACK_MENU4;
// CUSTOM VARIABLES
extern lv_obj_t * uic_BTN_set_Chinese;
extern lv_obj_t * uic_BTN_English;

// SCREEN: ui_SC_Service
void ui_SC_Service_screen_init(void);
extern lv_obj_t * ui_SC_Service;
extern lv_obj_t * ui_Lab_Service;
void ui_event_LAB_BACK_MENU3(lv_event_t * e);
extern lv_obj_t * ui_LAB_BACK_MENU3;
extern lv_obj_t * ui_Image5;
extern lv_obj_t * ui_Image6;
extern lv_obj_t * ui_Lab_Service_Tel;
extern lv_obj_t * ui_Lab_Tel;
extern lv_obj_t * ui_Label5;
extern lv_obj_t * ui_Lab_eMail;
// OTA buttons
void ui_event_BTN_OTA1(lv_event_t * e);
extern lv_obj_t * ui_BTN_OTA1;
void ui_event_BTN_OTA2(lv_event_t * e);
extern lv_obj_t * ui_BTN_OTA2;
void ui_event_BTN_OTA3(lv_event_t * e);
extern lv_obj_t * ui_BTN_OTA3;
// CUSTOM VARIABLES
extern lv_obj_t * uic_Lab_Service;
extern lv_obj_t * uic_Lab_Service_Tel;

// SCREEN: ui_SC_DeCa
void ui_SC_DeCa_screen_init(void);
extern lv_obj_t * ui_SC_DeCa;
extern lv_obj_t * ui_Lab_DeCa;
extern lv_obj_t * ui_Image7;
extern lv_obj_t * ui_BAR_Big_Coffee1;
extern lv_obj_t * ui_Lab_DeCaing;
extern lv_obj_t * ui_LAB__Big_Coffee_progress1;
extern lv_obj_t * ui_Lab_Wait6;
extern lv_obj_t * ui_BTN_Cancel5;
extern lv_obj_t * ui_Lab_Cancel5;
// CUSTOM VARIABLES
extern lv_obj_t * uic_Lab_DeCaing;

// SCREEN: ui_SC_Water
void ui_SC_Water_screen_init(void);
extern lv_obj_t * ui_SC_Water;
extern lv_obj_t * ui_Lab_Water1;
extern lv_obj_t * ui_Lab_Water2;
extern lv_obj_t * ui_Image8;
extern lv_obj_t * ui_Lab_Water_ing;
extern lv_obj_t * ui_Label40;
extern lv_obj_t * ui_BTN_Cancel6;
extern lv_obj_t * ui_Lab_Stop;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_Water;
extern lv_obj_t * uic_Lab_Water1;
extern lv_obj_t * uic_Lab_Water_ing;

// SCREEN: ui_SC_Clean
void ui_SC_Clean_screen_init(void);
extern lv_obj_t * ui_SC_Clean;
extern lv_obj_t * ui_Lab_Clean1;
extern lv_obj_t * ui_Image9;
extern lv_obj_t * ui_BAR_Clean;
extern lv_obj_t * ui_Lab_Cleaning;
extern lv_obj_t * ui_LAB__Clean_progress;
extern lv_obj_t * ui_Lab_Wait5;
extern lv_obj_t * ui_BTN_Cancel8;
extern lv_obj_t * ui_Lab_Stop2;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_Clean;
extern lv_obj_t * uic_BAR_Clean;
extern lv_obj_t * uic_Lab_Cleaning;
extern lv_obj_t * uic_LAB__Clean_progress;

// SCREEN: ui_SC_Steam
void ui_SC_Steam_screen_init(void);
extern lv_obj_t * ui_SC_Steam;
extern lv_obj_t * ui_Lab_Steam1;
extern lv_obj_t * ui_Label47;
extern lv_obj_t * ui_Image10;
extern lv_obj_t * ui_Lab_Steam_des;
extern lv_obj_t * ui_BTN_Steam_COM;
extern lv_obj_t * ui_Lab_Steam_COM;
// CUSTOM VARIABLES
extern lv_obj_t * uic_SC_Steam;
extern lv_obj_t * uic_Lab_Steam1;
extern lv_obj_t * uic_Lab_Steam_des;
extern lv_obj_t * uic_BTN_Steam_COM;
extern lv_obj_t * uic_Lab_Steam_COM;

// EVENTS

extern lv_obj_t * ui____initial_actions0;

// IMAGES AND IMAGE SETS
LV_IMG_DECLARE(ui_img_bg_png);    // assets/bg.png
LV_IMG_DECLARE(ui_img_power_on_png);    // assets/power_on.png
LV_IMG_DECLARE(ui_img_tea_png);    // assets/tea.png
LV_IMG_DECLARE(ui_img_tea_active_png);    // assets/tea_active.png
LV_IMG_DECLARE(ui_img_coffee_png);    // assets/coffee.png
LV_IMG_DECLARE(ui_img_coffee_active_png);    // assets/coffee_active.png
LV_IMG_DECLARE(ui_img_settings_png);    // assets/settings.png
LV_IMG_DECLARE(ui_img_settings_active_png);    // assets/settings_active.png
LV_IMG_DECLARE(ui_img_water_png);    // assets/water.png
LV_IMG_DECLARE(ui_img_water_active_png);    // assets/water_active.png
LV_IMG_DECLARE(ui_img_steam_png);    // assets/steam.png
LV_IMG_DECLARE(ui_img_steam_active_png);    // assets/steam_active.png
LV_IMG_DECLARE(ui_img_clean_png);    // assets/clean.png
LV_IMG_DECLARE(ui_img_clean_active_png);    // assets/clean_active.png
LV_IMG_DECLARE(ui_img_tea_s_btn_png);    // assets/tea_s_BTN.png
LV_IMG_DECLARE(ui_img_tea_s_btn_active_png);    // assets/tea_s_BTN_active.png
LV_IMG_DECLARE(ui_img_tea_btn_png);    // assets/tea_BTN.png
LV_IMG_DECLARE(ui_img_tea_btn_active_png);    // assets/tea_BTN_active.png
LV_IMG_DECLARE(ui_img_coffee_s_btn_png);    // assets/coffee_s_BTN.png
LV_IMG_DECLARE(ui_img_coffee_s_btn_active_png);    // assets/coffee_s_BTN_active.png
LV_IMG_DECLARE(ui_img_coffee_btn_png);    // assets/coffee_BTN.png
LV_IMG_DECLARE(ui_img_coffee_btn_active_png);    // assets/coffee_BTN_active.png
LV_IMG_DECLARE(ui_img_language_png);    // assets/language.png
LV_IMG_DECLARE(ui_img_language_active_png);    // assets/language_active.png
LV_IMG_DECLARE(ui_img_service_png);    // assets/service.png
LV_IMG_DECLARE(ui_img_service_active_png);    // assets/service_active.png
LV_IMG_DECLARE(ui_img_tea_s_active_png);    // assets/tea_s_active.png
LV_IMG_DECLARE(ui_img_coffee_s_active_png);    // assets/coffee_s_active.png
LV_IMG_DECLARE(ui_img_chinese_png);    // assets/chinese.png
LV_IMG_DECLARE(ui_img_chinese_active_png);    // assets/chinese_active.png
LV_IMG_DECLARE(ui_img_english_png);    // assets/english.png
LV_IMG_DECLARE(ui_img_english_active_png);    // assets/english_active.png
LV_IMG_DECLARE(ui_img_phone_icon_png);    // assets/phone_icon.png
LV_IMG_DECLARE(ui_img_mail_icon_png);    // assets/mail_icon.png
LV_IMG_DECLARE(ui_img_remove_ca_icon_png);    // assets/remove_ca_icon.png
LV_IMG_DECLARE(ui_img_clean_icon_png);    // assets/clean_icon.png
LV_IMG_DECLARE(ui_img_coffee_machine_icon_png);    // assets/coffee_machine_icon.png

// FONTS
LV_FONT_DECLARE(ui_font_NS26);
LV_FONT_DECLARE(ui_font_NS_19);
LV_FONT_DECLARE(ui_font_NS_36);
LV_FONT_DECLARE(ui_font_NS_48);
LV_FONT_DECLARE(ui_font_NS_BL_56);

// UI INIT
void ui_init(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif

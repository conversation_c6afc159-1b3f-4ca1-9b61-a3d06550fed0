#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import datetime

# 獲取當前日期時間，格式為 yyyymmddhhmmss
now = datetime.datetime.now()
timestamp = now.strftime("%Y%m%d%H%M%S")

# 創建包含時間戳的標頭檔案
header_content = f'''#ifndef BUILD_TIMESTAMP_H
#define BUILD_TIMESTAMP_H

#define BUILD_TIMESTAMP "{timestamp}"
#define BUILD_YEAR {now.year}
#define BUILD_MONTH {now.month}
#define BUILD_DAY {now.day}
#define BUILD_HOUR {now.hour}
#define BUILD_MINUTE {now.minute}
#define BUILD_SECOND {now.second}

#endif // BUILD_TIMESTAMP_H
'''

# 確保src目錄存在
src_dir = "src"
if not os.path.exists(src_dir):
    os.makedirs(src_dir)

# 寫入標頭檔案
with open(os.path.join(src_dir, "build_timestamp.h"), "w", encoding="utf-8") as f:
    f.write(header_content)

print(f"Generated build timestamp: {timestamp}")

# 將時間戳保存到環境變數中，供後續腳本使用
env_file = ".build_timestamp"
with open(env_file, "w") as f:
    f.write(timestamp)

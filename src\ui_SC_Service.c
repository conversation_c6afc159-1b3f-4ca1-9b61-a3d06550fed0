// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Service_screen_init(void)
{
    ui_SC_Service = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Service, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Service, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Service = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_Lab_Service, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Service, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Service, 0);
    lv_obj_set_y(ui_Lab_Service, -134);
    lv_obj_set_align(ui_Lab_Service, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Service, "聯絡客服");
    lv_obj_set_style_text_color(ui_Lab_Service, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Service, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Service, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_BACK_MENU3 = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_LAB_BACK_MENU3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_BACK_MENU3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_BACK_MENU3, -172);
    lv_obj_set_y(ui_LAB_BACK_MENU3, -133);
    lv_obj_set_align(ui_LAB_BACK_MENU3, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_BACK_MENU3, "＜主選單");
    lv_obj_add_flag(ui_LAB_BACK_MENU3, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_clear_flag(ui_LAB_BACK_MENU3,
                      LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE);     /// Flags
    lv_obj_set_style_text_color(ui_LAB_BACK_MENU3, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_BACK_MENU3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_BACK_MENU3, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image5 = lv_img_create(ui_SC_Service);
    lv_img_set_src(ui_Image5, &ui_img_phone_icon_png);
    lv_obj_set_width(ui_Image5, LV_SIZE_CONTENT);   /// 30
    lv_obj_set_height(ui_Image5, LV_SIZE_CONTENT);    /// 30
    lv_obj_set_x(ui_Image5, -152);
    lv_obj_set_y(ui_Image5, -36);
    lv_obj_set_align(ui_Image5, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image5, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image6 = lv_img_create(ui_SC_Service);
    lv_img_set_src(ui_Image6, &ui_img_mail_icon_png);
    lv_obj_set_width(ui_Image6, LV_SIZE_CONTENT);   /// 33
    lv_obj_set_height(ui_Image6, LV_SIZE_CONTENT);    /// 26
    lv_obj_set_x(ui_Image6, -152);
    lv_obj_set_y(ui_Image6, 25);
    lv_obj_set_align(ui_Image6, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image6, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Lab_Service_Tel = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_Lab_Service_Tel, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Service_Tel, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Service_Tel, -58);
    lv_obj_set_y(ui_Lab_Service_Tel, -36);
    lv_obj_set_align(ui_Lab_Service_Tel, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Service_Tel, "客服電話 :");
    lv_obj_set_style_text_color(ui_Lab_Service_Tel, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Service_Tel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Service_Tel, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Tel = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_Lab_Tel, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Tel, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Tel, 253);
    lv_obj_set_y(ui_Lab_Tel, 109);
    lv_label_set_text(ui_Lab_Tel, "03-123-4567");
    lv_obj_set_style_text_color(ui_Lab_Tel, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Tel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Tel, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label5 = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_Label5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label5, -72);
    lv_obj_set_y(ui_Label5, 25);
    lv_obj_set_align(ui_Label5, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label5, "e-mail :");
    lv_obj_set_style_text_color(ui_Label5, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label5, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label5, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_eMail = lv_label_create(ui_SC_Service);
    lv_obj_set_width(ui_Lab_eMail, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_eMail, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_eMail, 219);
    lv_obj_set_y(ui_Lab_eMail, 169);
    lv_label_set_text(ui_Lab_eMail, "<EMAIL>");
    lv_obj_set_style_text_color(ui_Lab_eMail, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_eMail, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_eMail, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    // OTA Button 1 - 右上角 (初始完全透明，方形不帶圓角)
    ui_BTN_OTA1 = lv_btn_create(ui_SC_Service);
    lv_obj_set_width(ui_BTN_OTA1, 50);
    lv_obj_set_height(ui_BTN_OTA1, 50);
    lv_obj_set_x(ui_BTN_OTA1, 215);  // 右上角
    lv_obj_set_y(ui_BTN_OTA1, -135);
    lv_obj_set_align(ui_BTN_OTA1, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_BTN_OTA1, LV_OBJ_FLAG_SCROLLABLE);
    // 設置完全透明，方形不帶圓角
    lv_obj_set_style_bg_opa(ui_BTN_OTA1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_BTN_OTA1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_BTN_OTA1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_BTN_OTA1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);  // 方形不帶圓角

    // OTA Button 2 - 右下角 (完全透明，方形不帶圓角，初始disabled)
    ui_BTN_OTA2 = lv_btn_create(ui_SC_Service);
    lv_obj_set_width(ui_BTN_OTA2, 50);
    lv_obj_set_height(ui_BTN_OTA2, 50);
    lv_obj_set_x(ui_BTN_OTA2, 215);  // 右下角
    lv_obj_set_y(ui_BTN_OTA2, 135);
    lv_obj_set_align(ui_BTN_OTA2, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_BTN_OTA2, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_state(ui_BTN_OTA2, LV_STATE_DISABLED);  // 初始disabled
    // 設置完全透明，方形不帶圓角
    lv_obj_set_style_bg_opa(ui_BTN_OTA2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_BTN_OTA2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_BTN_OTA2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_BTN_OTA2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);  // 方形不帶圓角

    // OTA Button 3 - 左下角 (完全透明，方形不帶圓角，初始disabled)
    ui_BTN_OTA3 = lv_btn_create(ui_SC_Service);
    lv_obj_set_width(ui_BTN_OTA3, 50);
    lv_obj_set_height(ui_BTN_OTA3, 50);
    lv_obj_set_x(ui_BTN_OTA3, -215);  // 左下角
    lv_obj_set_y(ui_BTN_OTA3, 135);
    lv_obj_set_align(ui_BTN_OTA3, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_BTN_OTA3, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_add_state(ui_BTN_OTA3, LV_STATE_DISABLED);  // 初始disabled
    // 設置完全透明，方形不帶圓角
    lv_obj_set_style_bg_opa(ui_BTN_OTA3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_BTN_OTA3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_BTN_OTA3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_BTN_OTA3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);  // 方形不帶圓角

    // 添加事件處理
    lv_obj_add_event_cb(ui_LAB_BACK_MENU3, ui_event_LAB_BACK_MENU3, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_OTA1, ui_event_BTN_OTA1, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_OTA2, ui_event_BTN_OTA2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_OTA3, ui_event_BTN_OTA3, LV_EVENT_ALL, NULL);

    uic_Lab_Service = ui_Lab_Service;
    uic_Lab_Service_Tel = ui_Lab_Service_Tel;

}

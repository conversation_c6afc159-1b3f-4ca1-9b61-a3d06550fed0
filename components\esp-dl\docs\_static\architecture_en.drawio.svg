<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2321px" height="2102px" viewBox="-0.5 -0.5 2321 2102" content="&lt;mxfile&gt;&lt;diagram id=&quot;l9ZcNTGW7sTykj39cZzX&quot; name=&quot;Page-1&quot;&gt;7Zxtc6I6FIB/jR/b4V34aH3Zdqbterc7e2fut4hRmY2EG2Jt99ffAAEVQkEl6G1x2lFOQmJynnNy8oI9fbh++0ZAsHrCc4h6mjJ/6+mjnqYZlqWwt0jynkj0vs0lS+LNE5m6E7x4fyAXptk23hyGBxkpxoh6waHQxb4PXXogA4Tg7WG2BUaHtQZgCQuCFxegovRvb05XidQ2lZ38HnrLVVqzqvCUNUgzc0G4AnO83RPp454+JBjT5NP6bQhR1HtpvyT3TUpSsy9GoE/r3GAayR2vAG1443qahdi9dwvMimDfkL7zZlv/bnCacBPGShmwDKYVvO0S2aclf49LmZ1fBP8iLkaYHJTS0/T5AqrQLN57e3ub3s4an5SQpIxfpjejx73EWb6+3A1cUVkbNII3/hxGHaiy5O3Ko/AlAG6UumXAM9mKrhFPBsTl/BpKdIm8pc8uEFzETfMQGmbt0keTsTo2mTykBP+GaYqPfcgzf2cVeTSym4gpVt4sxGhD4SCrRuU9xi9NK2vCPhUclFdIKHzbE3FKvkG8hpS8syxpaj9lmButmlrxdmcBmp5yvdrH33S4FHC7W2bF79BkHzidJaSaVaQKAVnEr32REqb+JJWp5+JriPCNQHsYTdojjaPlMiVDUoTLXjiWMysiKWRNzJUUjmylNkhqAxzZ+v+MoykkXrCCBKCwY6mEpT10VFU3ZaFTOVheGTqDIECeC6iHfZbnES89t0PoACHNts02EUpHwgOEct28ZP0c1G9bFuaCWVqC8nGblZwLNjWRGVkCD2w00QeWXWFGwvCv0gRUrSSIBOsITX8WBh+xzo2N5CVhAPzTLTCKNJXB9GGv2qRAiZYXEcKsHg24BVIcnB9S5qy0iGdGdrXttYSZc/bUxlBEOuUT2hY0t/bmcwSLvZ9zpQYwoLngX39PPolf5/rUIxSbzvBzEZ7IvSi2rACvL3Kxx9lyrPdY4TnVP8MNi8NYjmdIt5j8LjXrjoI8BZpgvmhKi/L7aikE5xn/I3iHpDP+crVbyiX17oj0fun4Klu33HeATl/QB1lnnbVSYn2d+GqKAF1gsmYZhthnugvjuc6nD7bqT3RaYq4vyd8mWo0K+InxV4q6jl8NydxMGncJ3I54XU1rgoCqWd2pBPy1AT71/qSrGB0GFRjkB+B2Mci2uhrn4Oevp2vSuzoDKtSKelfiV0HvcYo1HkykKt+sVn3JzKsJ3TvaFQZfqmG0Gn31q5YdPlH0dQ/IfMu0yDLcYRzSisCrWP1nCMW0EnQvFYrZshzwy8PT6Jo8sOSRt75eS5a9BM43G5ub35uSteAxJRBhMGf1DzYUJx8vycD5us4furhTR+qoIQYsuwYEqjQIRANwExCMnqK0qRdA5LGe7QAoD791/ZJuwBFtUV86BHPsuhFYIxs//Sor+EQR2Ate0C8fgRkl5J4QgTUDYOURo1M3H/D25s6LCjhcFPkyMVl9TdePybL5YfObUOcfrhVz8LCODiizAZlgF4ah5y87Bj4YkgtLAkIKZB356VceXD31HAKgxGNy5XsASecJqigw0pNel6GgalPuZApiBJJRYQiQu0FXgEKrMfrRKGTbz5cZFWRtlQ3CEK5n6L3zAmcdSnHkHUqRtUUW1cz+7+K/Tvm1AwGh+nVpc/MvtDOQPvT3D8YVU9JPMAE9eptOV3PLhKIT2H1H1vw0pb5xP3S/WcfQTCL9aMoIUuhePhhp0ykdj4KWWy+0BNuV8p6GsmRtG+RQ+AFdvPS9DoaPYcif3WgZBlnrVkNAO69wllfoC5bQJYIga7Ka9F8h5rgSJBqftRoDY2xOJPmGdpGwZa1i/YAIgpDpsvMGparXc+e6WNBfU/dNPHlvy/IGI/gKEQ4uvnzd5mrV8crvq3WULzpT0ITyVUXWJsYz3EZBAQR0Qy58pKD1k53HP6fsOC26AHa5+wmaOG3vl3z08X8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="160" width="2320" height="1590" rx="20" ry="20" fill="#dfe1e5" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 2318px; height: 1px; padding-top: 955px; margin-left: 2px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 56px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 56px">
                                    <b style="font-size: 56px">
                                        <font color="#dfe1e5">
                                            ...
                                        </font>
                                        ESP-DL
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2" y="972" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="56px">
                    ...ESP-DL
                </text>
            </switch>
        </g>
        <rect x="0" y="1800" width="2320" height="110" rx="20" ry="20" fill="#8f969b" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 2318px; height: 1px; padding-top: 1855px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font size="1" color="#ffffff">
                                    <b style="font-size: 54px">
                                        ESP-IDF
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1160" y="1859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ESP-IDF
                </text>
            </switch>
        </g>
        <rect x="0" y="0" width="1135" height="110" rx="20" ry="20" fill="#8f969b" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1133px; height: 1px; padding-top: 55px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font size="1" color="#ffffff">
                                    <b style="font-size: 54px">
                                        Peripherals
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="568" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Peripherals
                </text>
            </switch>
        </g>
        <rect x="1185" y="0" width="1135" height="110" rx="20" ry="20" fill="#8f969b" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1133px; height: 1px; padding-top: 55px; margin-left: 1186px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font size="1" color="#ffffff">
                                    <b style="font-size: 54px">
                                        Application Logic
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1753" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Application Logic
                </text>
            </switch>
        </g>
        <rect x="300" y="520" width="1160" height="410" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1158px; height: 1px; padding-top: 527px; margin-left: 301px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font>
                                    <font style="font-size: 12px">
                                    </font>
                                    <br/>
                                    <span style="font-size: 54px">
                                        DL API
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="880" y="539" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="340" y="640" width="1080" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1078px; height: 1px; padding-top: 695px; margin-left: 341px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Model
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="880" y="699" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Model
                </text>
            </switch>
        </g>
        <rect x="340" y="780" width="520" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 518px; height: 1px; padding-top: 835px; margin-left: 341px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 40px;">
                                    Neural Network
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="839" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Neural Network
                </text>
            </switch>
        </g>
        <rect x="900" y="780" width="520" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 518px; height: 1px; padding-top: 835px; margin-left: 901px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Layer
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1160" y="839" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Layer
                </text>
            </switch>
        </g>
        <rect x="300" y="210" width="1970" height="260" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1968px; height: 1px; padding-top: 217px; margin-left: 301px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font>
                                    <font style="font-size: 12px">
                                    </font>
                                    <br/>
                                    <span style="font-size: 54px">
                                        Platform Conversion
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1285" y="229" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="1510" y="320" width="320" height="120" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 380px; margin-left: 1511px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Convert Tool
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1670" y="384" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Convert Tool
                </text>
            </switch>
        </g>
        <rect x="1900" y="320" width="320" height="120" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 380px; margin-left: 1901px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Quantization Tool
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2060" y="384" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Quantization Tool
                </text>
            </switch>
        </g>
        <rect x="350" y="320" width="1080" height="120" rx="20" ry="20" fill="#1ba1e2" stroke="#006eaf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1078px; height: 1px; padding-top: 380px; margin-left: 351px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    TVM
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="890" y="384" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TVM
                </text>
            </switch>
        </g>
        <rect x="300" y="1440" width="1970" height="260" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1968px; height: 1px; padding-top: 1447px; margin-left: 301px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font>
                                    <font style="font-size: 12px">
                                    </font>
                                    <br/>
                                    <span style="font-size: 54px">
                                        Hardware Boost
                                    </span>
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1285" y="1459" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="340" y="1550" width="600" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 598px; height: 1px; padding-top: 1605px; margin-left: 341px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    SIMD
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="640" y="1609" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SIMD
                </text>
            </switch>
        </g>
        <rect x="980" y="1550" width="610" height="110" rx="20" ry="20" fill="#db1d1d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 608px; height: 1px; padding-top: 1605px; margin-left: 981px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Preload/Autoload
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1285" y="1609" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Preload/Autoload
                </text>
            </switch>
        </g>
        <rect x="1630" y="1550" width="600" height="110" rx="20" ry="20" fill="#db1d1d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 598px; height: 1px; padding-top: 1605px; margin-left: 1631px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    DMA Pipeline
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1930" y="1609" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    DMA Pipeline
                </text>
            </switch>
        </g>
        <rect x="300" y="980" width="1970" height="410" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1968px; height: 1px; padding-top: 987px; margin-left: 301px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font>
                                    <font style="font-size: 12px">
                                    </font>
                                    <br/>
                                    <span style="font-size: 54px">
                                        Software Boost
                                    </span>
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1285" y="999" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="340" y="1090" width="440" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 438px; height: 1px; padding-top: 1145px; margin-left: 341px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Low-Bit Quantization
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="1149" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Low-Bit Quantization
                </text>
            </switch>
        </g>
        <rect x="1300" y="1090" width="445" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 443px; height: 1px; padding-top: 1145px; margin-left: 1301px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Image Processing
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1523" y="1149" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Image Processing
                </text>
            </switch>
        </g>
        <rect x="1785" y="1090" width="445" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 443px; height: 1px; padding-top: 1145px; margin-left: 1786px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Matrix Operation
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2008" y="1149" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Matrix Operation
                </text>
            </switch>
        </g>
        <rect x="820" y="1090" width="440" height="110" rx="20" ry="20" fill="#db1d1d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 438px; height: 1px; padding-top: 1145px; margin-left: 821px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Mix Bit Calculation
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1040" y="1149" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Mix Bit Calculation
                </text>
            </switch>
        </g>
        <rect x="340" y="1240" width="920" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 918px; height: 1px; padding-top: 1295px; margin-left: 341px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Assembly
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="800" y="1299" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Assembly
                </text>
            </switch>
        </g>
        <rect x="1300" y="1240" width="930" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 928px; height: 1px; padding-top: 1295px; margin-left: 1301px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    C/C++
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1765" y="1299" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    C/C++
                </text>
            </switch>
        </g>
        <rect x="1480" y="520" width="790" height="410" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 788px; height: 1px; padding-top: 527px; margin-left: 1481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font>
                                    <font style="font-size: 12px">
                                    </font>
                                    <br/>
                                    <span style="font-size: 54px">
                                        Model Zoo
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1875" y="539" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="1530" y="640" width="320" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 695px; margin-left: 1531px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Human Face Detection
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1690" y="699" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Human Face Detection
                </text>
            </switch>
        </g>
        <rect x="1900" y="640" width="320" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 695px; margin-left: 1901px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Human Face Recognition
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2060" y="699" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Human Face Recognition
                </text>
            </switch>
        </g>
        <rect x="1530" y="780" width="320" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 835px; margin-left: 1531px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Cat Face Detection
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1690" y="839" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cat Face Detection
                </text>
            </switch>
        </g>
        <rect x="1900" y="780" width="320" height="110" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 835px; margin-left: 1901px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Color
                                    <br/>
                                    Detection
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2060" y="839" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Color...
                </text>
            </switch>
        </g>
        <rect x="1650" y="2010" width="320" height="90" rx="20" ry="20" fill="#4a4e5f" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 2055px; margin-left: 1651px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Released
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1810" y="2059" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Released
                </text>
            </switch>
        </g>
        <rect x="2010" y="2010" width="310" height="90" rx="20" ry="20" fill="#db1d1d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 2055px; margin-left: 2011px;">
                        <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    Developing
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2165" y="2059" fill="#FFFFFF" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Developing
                </text>
            </switch>
        </g>
        <rect x="1290" y="2010" width="320" height="90" rx="20" ry="20" fill="#1ba1e2" stroke="#006eaf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 2055px; margin-left: 1291px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 40px">
                                    New Feature
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1450" y="2059" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    New Feature
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>

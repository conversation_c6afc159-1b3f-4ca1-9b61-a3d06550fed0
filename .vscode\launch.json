{"version": "0.2.0", "configurations": [{"name": "ESP32-S3 Debug (USB-JTAG)", "type": "espidf", "request": "launch", "debugPort": 43474, "logLevel": 2, "mode": "auto", "workspace": "${workspaceFolder}", "verifyAppBinBeforeDebug": false, "launchMonitorOnDebugSession": true, "monitorPort": "COM3", "monitorBaudRate": 115200, "openOcdConfig": {"debugLevel": 1, "logOutput": true, "configs": ["board/esp32s3-builtin.cfg"]}, "env": {"OPENOCD_SCRIPTS": "${env:IDF_PATH}/tools/openocd-esp32/share/openocd/scripts"}}, {"name": "ESP32-S3 Attach (USB-JTAG)", "type": "espidf", "request": "attach", "debugPort": 43474, "logLevel": 2, "mode": "manual", "workspace": "${workspaceFolder}", "openOcdConfig": {"debugLevel": 1, "logOutput": true, "configs": ["board/esp32s3-builtin.cfg"]}, "env": {"OPENOCD_SCRIPTS": "${env:IDF_PATH}/tools/openocd-esp32/share/openocd/scripts"}}, {"name": "ESP32-S3 GDB Debug (Manual OpenOCD)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/ameter_s3.elf", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "xtensa-esp32s3-elf-gdb", "miDebuggerServerAddress": "localhost:3333", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set hardware watchpoint limit", "text": "set remote hardware-watchpoint-limit 2", "ignoreFailures": false}], "customLaunchSetupCommands": [{"text": "target remote localhost:3333"}, {"text": "monitor reset halt"}, {"text": "flushregs"}, {"text": "thb app_main"}, {"text": "c"}], "logging": {"engineLogging": true}}]}
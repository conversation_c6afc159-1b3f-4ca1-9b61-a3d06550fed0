#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import datetime

# 讀取時間戳
timestamp = ""
env_file = ".build_timestamp"
if os.path.exists(env_file):
    with open(env_file, "r") as f:
        timestamp = f.read().strip()
else:
    # 如果沒有時間戳檔案，使用當前時間
    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")

# 原始韌體檔案路徑
source_firmware = ".pio/build/esp32-s3-devkitc-1-n16r8v/firmware.bin"

# 目標韌體檔案路徑
target_firmware = f"Teaffic_{timestamp}.bin"

# 複製韌體檔案
if os.path.exists(source_firmware):
    shutil.copy2(source_firmware, target_firmware)
    print(f"韌體檔案已複製到: {target_firmware}")
    
    # 顯示檔案大小
    file_size = os.path.getsize(target_firmware)
    print(f"韌體檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
else:
    print(f"錯誤: 找不到原始韌體檔案 {source_firmware}")

# 清理臨時檔案
if os.path.exists(env_file):
    os.remove(env_file)

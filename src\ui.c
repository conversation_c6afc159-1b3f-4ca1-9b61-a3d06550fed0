// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"
#include "ui_helpers.h"

///////////////////// VARIABLES ////////////////////

// SCREEN: ui_SC_PWR_ON
void ui_SC_PWR_ON_screen_init(void);
lv_obj_t * ui_SC_PWR_ON;
lv_obj_t * ui_BTN_PWR;
lv_obj_t * ui_Label1;
lv_obj_t * ui_PWR_ARC;
lv_obj_t * ui_Lab_wait_heater;
lv_obj_t * ui_Bar_Wait_Heater;
lv_obj_t * ui_Lab_Build_Time;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_PWR_ON;
lv_obj_t * uic_BTN_PWR;
lv_obj_t * uic_PWR_ARC;
lv_obj_t * uic_Lab_wait_heater;
lv_obj_t * uic_Bar_Wait_Heater;
lv_obj_t * uic_Lab_Build_Time;

// SCREEN: ui_SC_MAIN_MENU
void ui_SC_MAIN_MENU_screen_init(void);
lv_obj_t * ui_SC_MAIN_MENU;
lv_obj_t * ui_Label2;
lv_obj_t * ui_Panel1;
void ui_event_BTN_TEA(lv_event_t * e);
lv_obj_t * ui_BTN_TEA;
lv_obj_t * ui_Lab_Tea;
void ui_event_BTN_Coffee(lv_event_t * e);
lv_obj_t * ui_BTN_Coffee;
lv_obj_t * ui_Lab_Coffee;
void ui_event_BTN_SETTING(lv_event_t * e);
lv_obj_t * ui_BTN_SETTING;
lv_obj_t * ui_Lab_Setting;
void ui_event_BTN_WATER(lv_event_t * e);
lv_obj_t * ui_BTN_WATER;
lv_obj_t * ui_Lab_Water;
lv_obj_t * ui_BTN_Steam;
lv_obj_t * ui_Lab_Steam;
lv_obj_t * ui_BTN_Clean;
lv_obj_t * ui_Lab_Clean;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_MAIN_MENU;
lv_obj_t * uic_Lab_Tea;
lv_obj_t * uic_Lab_Setting;
lv_obj_t * uic_BTN_WATER;
lv_obj_t * uic_Lab_Water;
lv_obj_t * uic_BTN_Steam;
lv_obj_t * uic_Lab_Steam;
lv_obj_t * uic_BTN_Clean;
lv_obj_t * uic_Lab_Clean;

// SCREEN: ui_SC_TEA
void ui_SC_TEA_screen_init(void);
lv_obj_t * ui_SC_TEA;
lv_obj_t * ui_Lab_Tea1;
void ui_event_LAB_BACK_MENU(lv_event_t * e);
lv_obj_t * ui_LAB_BACK_MENU;
lv_obj_t * ui_Panel2;
lv_obj_t * ui_BTN_SMALL_TEA;
lv_obj_t * ui_Lab_Small_Tea;
lv_obj_t * ui_BTN_BIG_TEA;
lv_obj_t * ui_LAB_Big_Tea;
// CUSTOM VARIABLES
lv_obj_t * uic_LAB_BACK_MENU;
lv_obj_t * uic_BTN_SMALL_TEA;
lv_obj_t * uic_Lab_Small_Tea;

// SCREEN: ui_SC_Coffee
void ui_SC_Coffee_screen_init(void);
lv_obj_t * ui_SC_Coffee;
lv_obj_t * ui_LAB_Coffee1;
void ui_event_LAB_BACK_MENU1(lv_event_t * e);
lv_obj_t * ui_LAB_BACK_MENU1;
lv_obj_t * ui_Panel3;
void ui_event_BTN_SMALL_Coffee(lv_event_t * e);
lv_obj_t * ui_BTN_SMALL_Coffee;
lv_obj_t * ui_Lab_Small_Coffee;
void ui_event_BTN_BIG_Coffee(lv_event_t * e);
lv_obj_t * ui_BTN_BIG_Coffee;
lv_obj_t * ui_Lab_Big_Coffee;
// CUSTOM VARIABLES
lv_obj_t * uic_BTN_SMALL_Coffee;
lv_obj_t * uic_Lab_Small_Coffee;
lv_obj_t * uic_BTN_BIG_Coffee;
lv_obj_t * uic_Lab_Big_Coffee;

// SCREEN: ui_SC_Setting
void ui_SC_Setting_screen_init(void);
lv_obj_t * ui_SC_Setting;
lv_obj_t * ui_Lab_Setting1;
void ui_event_LAB_BACK_MENU2(lv_event_t * e);
lv_obj_t * ui_LAB_BACK_MENU2;
lv_obj_t * ui_Panel4;
void ui_event_BTN_DECA(lv_event_t * e);
lv_obj_t * ui_BTN_DECA;
lv_obj_t * ui_Lab_DeCa1;
void ui_event_BTN_set_Language(lv_event_t * e);
lv_obj_t * ui_BTN_set_Language;
lv_obj_t * ui_Lab_Langauge1;
void ui_event_BTN_BIG_TEA1(lv_event_t * e);
lv_obj_t * ui_BTN_BIG_TEA1;
lv_obj_t * ui_Lab_Service1;
// CUSTOM VARIABLES
lv_obj_t * uic_Lab_Setting1;
lv_obj_t * uic_Lab_DeCa1;

// SCREEN: ui_SC_Small_Tea
void ui_SC_Small_Tea_screen_init(void);
lv_obj_t * ui_SC_Small_Tea;
lv_obj_t * ui_Lab_Tea2;
lv_obj_t * ui_Lab_Small_Tea1;
lv_obj_t * ui_Image2;
lv_obj_t * ui_BAR_Small_tea;
lv_obj_t * ui_Lab_Brewing;
lv_obj_t * ui_LAB__Small_Tea_progress;
lv_obj_t * ui_Lab_Wait1;
lv_obj_t * ui_BTN_Cancel1;
lv_obj_t * ui_Lab_Cancel1;
// CUSTOM VARIABLES
lv_obj_t * uic_Lab_Small_Tea1;
lv_obj_t * uic_BAR_Small_tea;
lv_obj_t * uic_Lab_Brewing;
lv_obj_t * uic_LAB__Small_Tea_progress;
lv_obj_t * uic_Lab_Wait1;
lv_obj_t * uic_BTN_Cancel1;

// SCREEN: ui_SC_Big_Tea
void ui_SC_Big_Tea_screen_init(void);
lv_obj_t * ui_SC_Big_Tea;
lv_obj_t * ui_Lab_Tea3;
lv_obj_t * ui_LAB_Big_Tea1;
lv_obj_t * ui_Image1;
lv_obj_t * ui_BAR_Big_tea;
lv_obj_t * ui_Lab_Brewing1;
lv_obj_t * ui_LAB__Big_Tea_progress;
lv_obj_t * ui_Lab_Wait2;
lv_obj_t * ui_BTN_Cancel2;
lv_obj_t * ui_Lab_Cancel2;
// CUSTOM VARIABLES
lv_obj_t * uic_BAR_Big_tea;
lv_obj_t * uic_LAB__Big_Tea_progress;

// SCREEN: ui_SC_Small_Coffee
void ui_SC_Small_Coffee_screen_init(void);
lv_obj_t * ui_SC_Small_Coffee;
lv_obj_t * ui_LAB_Coffee2;
lv_obj_t * ui_Lab_Small_Coffee1;
lv_obj_t * ui_Image3;
lv_obj_t * ui_BAR_Small_Coffee;
lv_obj_t * ui_Lab_Brewing2;
lv_obj_t * ui_LAB__Small_Coffee_progress;
lv_obj_t * ui_Lab_Wait3;
lv_obj_t * ui_BTN_Cancel3;
lv_obj_t * ui_Lab_Cancel3;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_Small_Coffee;
lv_obj_t * uic_BAR_Small_Coffee;
lv_obj_t * uic_LAB__Small_Coffee_progress;

// SCREEN: ui_SC_Big_Coffee
void ui_SC_Big_Coffee_screen_init(void);
lv_obj_t * ui_SC_Big_Coffee;
lv_obj_t * ui_LAB_Coffee3;
lv_obj_t * ui_Lab_Big_Coffee1;
lv_obj_t * ui_Image4;
lv_obj_t * ui_BAR_Big_Coffee;
lv_obj_t * ui_Lab_Brewing3;
lv_obj_t * ui_LAB__Big_Coffee_progress;
lv_obj_t * ui_Lab_Wait4;
lv_obj_t * ui_BTN_Cancel4;
lv_obj_t * ui_Lab_Cancel4;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_Big_Coffee;
lv_obj_t * uic_BAR_Big_Coffee;

// SCREEN: ui_SC_Language
void ui_SC_Language_screen_init(void);
lv_obj_t * ui_SC_Language;
lv_obj_t * ui_Lab_Langauge;
lv_obj_t * ui_Panel5;
lv_obj_t * ui_BTN_set_Chinese;
lv_obj_t * ui_Lab_Chinese;
lv_obj_t * ui_BTN_English;
lv_obj_t * ui_Lab_English;
void ui_event_LAB_BACK_MENU4(lv_event_t * e);
lv_obj_t * ui_LAB_BACK_MENU4;
// CUSTOM VARIABLES
lv_obj_t * uic_BTN_set_Chinese;
lv_obj_t * uic_BTN_English;

// SCREEN: ui_SC_Service
void ui_SC_Service_screen_init(void);
lv_obj_t * ui_SC_Service;
lv_obj_t * ui_Lab_Service;
void ui_event_LAB_BACK_MENU3(lv_event_t * e);
lv_obj_t * ui_LAB_BACK_MENU3;
lv_obj_t * ui_Image5;
lv_obj_t * ui_Image6;
lv_obj_t * ui_Lab_Service_Tel;
lv_obj_t * ui_Lab_Tel;
lv_obj_t * ui_Label5;
lv_obj_t * ui_Lab_eMail;
// OTA buttons
void ui_event_BTN_OTA1(lv_event_t * e);
lv_obj_t * ui_BTN_OTA1;
void ui_event_BTN_OTA2(lv_event_t * e);
lv_obj_t * ui_BTN_OTA2;
void ui_event_BTN_OTA3(lv_event_t * e);
lv_obj_t * ui_BTN_OTA3;
// CUSTOM VARIABLES
lv_obj_t * uic_Lab_Service;
lv_obj_t * uic_Lab_Service_Tel;

// SCREEN: ui_SC_DeCa
void ui_SC_DeCa_screen_init(void);
lv_obj_t * ui_SC_DeCa;
lv_obj_t * ui_Lab_DeCa;
lv_obj_t * ui_Image7;
lv_obj_t * ui_BAR_Big_Coffee1;
lv_obj_t * ui_Lab_DeCaing;
lv_obj_t * ui_LAB__Big_Coffee_progress1;
lv_obj_t * ui_Lab_Wait6;
lv_obj_t * ui_BTN_Cancel5;
lv_obj_t * ui_Lab_Cancel5;
// CUSTOM VARIABLES
lv_obj_t * uic_Lab_DeCaing;

// SCREEN: ui_SC_Water
void ui_SC_Water_screen_init(void);
lv_obj_t * ui_SC_Water;
lv_obj_t * ui_Lab_Water1;
lv_obj_t * ui_Lab_Water2;
lv_obj_t * ui_Image8;
lv_obj_t * ui_Lab_Water_ing;
lv_obj_t * ui_Label40;
lv_obj_t * ui_BTN_Cancel6;
lv_obj_t * ui_Lab_Stop;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_Water;
lv_obj_t * uic_Lab_Water1;
lv_obj_t * uic_Lab_Water_ing;

// SCREEN: ui_SC_Clean
void ui_SC_Clean_screen_init(void);
lv_obj_t * ui_SC_Clean;
lv_obj_t * ui_Lab_Clean1;
lv_obj_t * ui_Image9;
lv_obj_t * ui_BAR_Clean;
lv_obj_t * ui_Lab_Cleaning;
lv_obj_t * ui_LAB__Clean_progress;
lv_obj_t * ui_Lab_Wait5;
lv_obj_t * ui_BTN_Cancel8;
lv_obj_t * ui_Lab_Stop2;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_Clean;
lv_obj_t * uic_BAR_Clean;
lv_obj_t * uic_Lab_Cleaning;
lv_obj_t * uic_LAB__Clean_progress;

// SCREEN: ui_SC_Steam
void ui_SC_Steam_screen_init(void);
lv_obj_t * ui_SC_Steam;
lv_obj_t * ui_Lab_Steam1;
lv_obj_t * ui_Label47;
lv_obj_t * ui_Image10;
lv_obj_t * ui_Lab_Steam_des;
lv_obj_t * ui_BTN_Steam_COM;
lv_obj_t * ui_Lab_Steam_COM;
// CUSTOM VARIABLES
lv_obj_t * uic_SC_Steam;
lv_obj_t * uic_Lab_Steam1;
lv_obj_t * uic_Lab_Steam_des;
lv_obj_t * uic_BTN_Steam_COM;
lv_obj_t * uic_Lab_Steam_COM;

// EVENTS
lv_obj_t * ui____initial_actions0;

// IMAGES AND IMAGE SETS

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 16
    #error "LV_COLOR_DEPTH should be 16bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// ANIMATIONS ////////////////////

///////////////////// FUNCTIONS ////////////////////
void ui_event_BTN_TEA(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_TEA, LV_SCR_LOAD_ANIM_NONE, 500, 0, &ui_SC_TEA_screen_init);
    }
}

void ui_event_BTN_Coffee(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Coffee, LV_SCR_LOAD_ANIM_NONE, 200, 0, &ui_SC_Coffee_screen_init);
    }
}

void ui_event_BTN_SETTING(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Setting, LV_SCR_LOAD_ANIM_NONE, 200, 0, &ui_SC_Setting_screen_init);
    }
}

void ui_event_BTN_WATER(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Water, LV_SCR_LOAD_ANIM_NONE, 500, 0, &ui_SC_Water_screen_init);
    }
}

void ui_event_LAB_BACK_MENU(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    }
}

void ui_event_LAB_BACK_MENU1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    }
}

void ui_event_BTN_SMALL_Coffee(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Small_Coffee, LV_SCR_LOAD_ANIM_NONE, 500, 0, &ui_SC_Small_Coffee_screen_init);
    }
}

void ui_event_BTN_BIG_Coffee(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Big_Coffee, LV_SCR_LOAD_ANIM_NONE, 500, 0, &ui_SC_Big_Coffee_screen_init);
    }
}

void ui_event_LAB_BACK_MENU2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    }
}

void ui_event_BTN_DECA(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_DeCa, LV_SCR_LOAD_ANIM_NONE, 200, 0, &ui_SC_DeCa_screen_init);
    }
}

void ui_event_BTN_set_Language(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Language, LV_SCR_LOAD_ANIM_NONE, 200, 0, &ui_SC_Language_screen_init);
    }
}

void ui_event_BTN_BIG_TEA1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Service, LV_SCR_LOAD_ANIM_NONE, 200, 0, &ui_SC_Service_screen_init);
    }
}

void ui_event_LAB_BACK_MENU4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    }
}

void ui_event_LAB_BACK_MENU3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_MAIN_MENU, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SC_MAIN_MENU_screen_init);
    }
}

// OTA按鍵事件處理函數在Main.cpp中實作

///////////////////// SCREENS ////////////////////

void ui_init(void)
{
    lv_disp_t * dispp = lv_disp_get_default();
    lv_theme_t * theme = lv_theme_default_init(dispp, lv_palette_main(LV_PALETTE_BLUE), lv_palette_main(LV_PALETTE_RED),
                                               true, LV_FONT_DEFAULT);
    lv_disp_set_theme(dispp, theme);
    ui_SC_PWR_ON_screen_init();
    ui_SC_MAIN_MENU_screen_init();
    ui_SC_TEA_screen_init();
    ui_SC_Coffee_screen_init();
    ui_SC_Setting_screen_init();
    ui_SC_Small_Tea_screen_init();
    ui_SC_Big_Tea_screen_init();
    ui_SC_Small_Coffee_screen_init();
    ui_SC_Big_Coffee_screen_init();
    ui_SC_Language_screen_init();
    ui_SC_Service_screen_init();
    ui_SC_DeCa_screen_init();
    ui_SC_Water_screen_init();
    ui_SC_Clean_screen_init();
    ui_SC_Steam_screen_init();
    ui____initial_actions0 = lv_obj_create(NULL);
    lv_disp_load_scr(ui_SC_PWR_ON);
}

// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Language_screen_init(void)
{
    ui_SC_Language = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Language, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Language, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Langauge = lv_label_create(ui_SC_Language);
    lv_obj_set_width(ui_Lab_Langauge, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Langauge, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Langauge, 0);
    lv_obj_set_y(ui_Lab_Langauge, -134);
    lv_obj_set_align(ui_Lab_Langauge, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Langauge, "語言");
    lv_obj_set_style_text_color(ui_Lab_Langauge, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Langauge, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Langauge, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel5 = lv_obj_create(ui_SC_Language);
    lv_obj_set_width(ui_Panel5, 480);
    lv_obj_set_height(ui_Panel5, 240);
    lv_obj_set_x(ui_Panel5, 0);
    lv_obj_set_y(ui_Panel5, 80);
    lv_obj_set_flex_flow(ui_Panel5, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_Panel5, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_Panel5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Panel5, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel5, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_set_Chinese = lv_imgbtn_create(ui_Panel5);
    lv_imgbtn_set_src(ui_BTN_set_Chinese, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_chinese_png, NULL);
    lv_imgbtn_set_src(ui_BTN_set_Chinese, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_chinese_active_png, NULL);
    lv_obj_set_width(ui_BTN_set_Chinese, 126);
    lv_obj_set_height(ui_BTN_set_Chinese, 126);
    lv_obj_set_align(ui_BTN_set_Chinese, LV_ALIGN_CENTER);

    ui_Lab_Chinese = lv_label_create(ui_BTN_set_Chinese);
    lv_obj_set_width(ui_Lab_Chinese, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Chinese, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Chinese, 0);
    lv_obj_set_y(ui_Lab_Chinese, 45);
    lv_obj_set_align(ui_Lab_Chinese, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Chinese, "中文");
    lv_obj_set_style_text_color(ui_Lab_Chinese, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Chinese, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Chinese, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_English = lv_imgbtn_create(ui_Panel5);
    lv_imgbtn_set_src(ui_BTN_English, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_english_png, NULL);
    lv_imgbtn_set_src(ui_BTN_English, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_english_active_png, NULL);
    lv_obj_set_width(ui_BTN_English, 126);
    lv_obj_set_height(ui_BTN_English, 126);
    lv_obj_set_align(ui_BTN_English, LV_ALIGN_CENTER);

    ui_Lab_English = lv_label_create(ui_BTN_English);
    lv_obj_set_width(ui_Lab_English, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_English, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_English, 0);
    lv_obj_set_y(ui_Lab_English, 45);
    lv_obj_set_align(ui_Lab_English, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_English, "English");
    lv_obj_set_style_text_color(ui_Lab_English, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_English, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_English, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_BACK_MENU4 = lv_label_create(ui_SC_Language);
    lv_obj_set_width(ui_LAB_BACK_MENU4, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_BACK_MENU4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_BACK_MENU4, -172);
    lv_obj_set_y(ui_LAB_BACK_MENU4, -133);
    lv_obj_set_align(ui_LAB_BACK_MENU4, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_BACK_MENU4, "＜主選單");
    lv_obj_add_flag(ui_LAB_BACK_MENU4, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_clear_flag(ui_LAB_BACK_MENU4,
                      LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE);     /// Flags
    lv_obj_set_style_text_color(ui_LAB_BACK_MENU4, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_BACK_MENU4, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_BACK_MENU4, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_LAB_BACK_MENU4, ui_event_LAB_BACK_MENU4, LV_EVENT_ALL, NULL);
    uic_BTN_set_Chinese = ui_BTN_set_Chinese;
    uic_BTN_English = ui_BTN_English;

}

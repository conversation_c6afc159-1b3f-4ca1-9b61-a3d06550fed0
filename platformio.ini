; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-devkitc-1-n16r8v]

platform = espressif32
board = esp32-s3-devkitc-1-n16r8v
framework = arduino
lib_extra_dirs = D:\Arduino\libraries
monitor_speed = 115200

; OTA分區表配置
board_build.partitions = partitions_ota.csv
board_build.filesystem = spiffs
lib_deps =
    tzapu/WiFiManager@^2.0.17
    ArduinoOTA
    HTTPClient
    Update
    bblanchon/ArduinoJson@^6.21.3

; 自動產生帶日期時間的韌體檔案
extra_scripts =
    pre:scripts/build_timestamp.py
    post:scripts/copy_firmware.py
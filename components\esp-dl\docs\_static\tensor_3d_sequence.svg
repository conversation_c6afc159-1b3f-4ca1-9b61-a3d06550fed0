<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="229px" height="302px" viewBox="-0.5 -0.5 229 302" content="&lt;mxfile&gt;&lt;diagram id=&quot;gNiFe_voYH61i-BdPS9y&quot; name=&quot;Page-1&quot;&gt;7VtNc6M4EP01rto9TAoJkMlxNjuTPezWHHLYswDZUMHII8uxM79+JEviIyiJwUaOi5CqWDwJIbpfq1uNmPl3q/09w+vsP5qSYga9dD/z/55BiDwk/kvgWQFhGCpgyfJUQaAGHvJfRIOeRrd5SjathpzSgufrNpjQsiQJb2GYMbprN1vQon3XNV6SDvCQ4KKL/p+nPFNoFHo1/g/Jl5m5M/B0zQqbxhrYZDiluwbkf5v5d4xSrkqr/R0ppOyMXNR131+prQbGSMmPuQCqC55wsdXPpsfFn83DiiGuZTHZxuLnr12Wc/KwxonEdkK9Asv4qhBnQBRjui1Tkv4bVwBOHpdMoj+2vMhLovEUs8cfopucSxJ4N17YBuEBlS03Sv+RJ8qLvCjuaEHZYWz+IpR/sg1n9JE0atDh0FfU95GaEBgteaOtOgTelZ4W6BNhnOwbkJbmPaErwtmzaGK47d+EgVcfWp6a6IFW+65mDTTcyBqMgZEGsWbqsrpRrUxR0Pq06zY8QrlSM+vjH7syKRybHrwTxBF1xQGgRRwVeIo4grlFHKjgkqOisOQHAihAEqQlKPRzS03FF0XIr6IBQOt9XWl6AaHpR4xJddXuXsCNW35Qe4PSUhjlmOe0lJUdY4IWiyQgDcncZpG3aO5jZLWyiqnvm1mDKsjCFHQOokTOiBJMlyiLBUFJYiNKOr+ND9P0SUTZm+m1NePA7owzGo1undHIny6NxGwTpYGNRhGMfXTyfLNvO+7X/fhYNDJBpQMawQnTCJNoYZ2NUBKReHEmGqE2jZA7Gon49uLBIAj6BsfjRYPAJo9xzMqbrlkNcvIVVa/AycsIzg2NbqfLokE+vj+LLufjQeiKRdGEWTTExfdn0eVcPLDle8ZxaWC6NBqU4OhBIxex4AdIDALvA8WCzjKD80+76Wc3HyoxCJwlBtF0eTJsyXBFeUHgLC844dcQw5YMV5QWNIQdn0UTfkcxbMlwTVlBYGGR40iw9xvz8QJB8/TjG9WE39gMCwSP34jhYvJ1ljyG0+XJsECw94adywWC0FnueMLpmmGBYG8WXTAQdJY7nvB7rGGBYP/NgxcLBMM3PZqFMbVCviRKGpI4bBn/AeVmXjEkcU/vRfnPLqnUs0mZi849vx+XGN1sYsze5xNTfNJnSolm5y60Kq+37t55ue1bHIs1kD1HHBu+7lk2a1yOqtDO5KMfzqg4tKu4wtQAR9J8mjOxMlGWv6FbqYhx2NB7p4OdCufggi3ddLWTMxg0N6d4k1UcOIetwzfVCyz7mseauE0fn+p1pl6XawR0RILmU73nVa/DsAu9mXAa0UsnGS7Lw0dZyisHTgKvBjmC+qudc7veFwv6OboJUDf2Mju3Toy9xGn9tdahrvHJm//tNw==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><path d="M 27.54 1 L 147.54 1 L 227.54 81 L 227.54 281 L 107.54 281 L 27.54 201 L 27.54 1 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/><path d="M 27.54 1 L 147.54 1 L 227.54 81 L 107.54 81 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/><path d="M 27.54 1 L 107.54 81 L 107.54 281 L 27.54 201 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="all"/><path d="M 107.54 281 L 107.54 81 L 27.54 1 M 107.54 81 L 227.54 81" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/><path d="M 27.54 41 L 67.54 41 L 87.54 61 L 87.54 101 L 47.54 101 L 27.54 81 L 27.54 41 Z" fill-opacity="0.2" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><path d="M 27.54 41 L 67.54 41 L 87.54 61 L 47.54 61 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 27.54 41 L 47.54 61 L 47.54 101 L 27.54 81 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 47.54 101 L 47.54 61 L 27.54 41 M 47.54 61 L 87.54 61" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 81px; margin-left: 49px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">15</font></b></div></div></div></foreignObject><text x="68" y="85" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">15</text></switch></g><path d="M 47.54 61 L 87.54 61 L 107.54 81 L 107.54 121 L 67.54 121 L 47.54 101 L 47.54 61 Z" fill-opacity="0.2" fill="#ffe6cc" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><path d="M 47.54 61 L 87.54 61 L 107.54 81 L 67.54 81 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 47.54 61 L 67.54 81 L 67.54 121 L 47.54 101 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 67.54 121 L 67.54 81 L 47.54 61 M 67.54 81 L 107.54 81" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 101px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">14</font></b></div></div></div></foreignObject><text x="88" y="105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">14</text></switch></g><path d="M 67.54 81 L 107.54 81 L 127.54 101 L 127.54 141 L 87.54 141 L 67.54 121 L 67.54 81 Z" fill-opacity="0.2" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><path d="M 67.54 81 L 107.54 81 L 127.54 101 L 87.54 101 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 67.54 81 L 87.54 101 L 87.54 141 L 67.54 121 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 141 L 87.54 101 L 67.54 81 M 87.54 101 L 127.54 101" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 121px; margin-left: 89px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">13</font></b></div></div></div></foreignObject><text x="108" y="125" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">13</text></switch></g><path d="M 87.54 101 L 127.54 101 L 147.54 121 L 147.54 161 L 107.54 161 L 87.54 141 L 87.54 101 Z" fill-opacity="0.2" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><path d="M 87.54 101 L 127.54 101 L 147.54 121 L 107.54 121 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 101 L 107.54 121 L 107.54 161 L 87.54 141 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 161 L 107.54 121 L 87.54 101 M 107.54 121 L 147.54 121" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 141px; margin-left: 109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">12</font></b></div></div></div></foreignObject><text x="128" y="145" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">12</text></switch></g><path d="M 127.54 21 L 167.54 21 L 187.54 41 L 187.54 81 L 147.54 81 L 127.54 61 L 127.54 21 Z" fill-opacity="0.2" fill="#ffe6cc" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><path d="M 127.54 21 L 167.54 21 L 187.54 41 L 147.54 41 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 127.54 21 L 147.54 41 L 147.54 81 L 127.54 61 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 147.54 81 L 147.54 41 L 127.54 21 M 147.54 41 L 187.54 41" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 61px; margin-left: 149px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">10</font></b></div></div></div></foreignObject><text x="168" y="65" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><path d="M 147.54 41 L 187.54 41 L 207.54 61 L 207.54 101 L 167.54 101 L 147.54 81 L 147.54 41 Z" fill-opacity="0.2" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><path d="M 147.54 41 L 187.54 41 L 207.54 61 L 167.54 61 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 147.54 41 L 167.54 61 L 167.54 101 L 147.54 81 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 167.54 101 L 167.54 61 L 147.54 41 M 167.54 61 L 207.54 61" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 81px; margin-left: 169px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">9</font></b></div></div></div></foreignObject><text x="188" y="85" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">9</text></switch></g><path d="M 167.54 61 L 207.54 61 L 227.54 81 L 227.54 121 L 187.54 121 L 167.54 101 L 167.54 61 Z" fill-opacity="0.2" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><path d="M 167.54 61 L 207.54 61 L 227.54 81 L 187.54 81 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 167.54 61 L 187.54 81 L 187.54 121 L 167.54 101 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 187.54 121 L 187.54 81 L 167.54 61 M 187.54 81 L 227.54 81" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 101px; margin-left: 189px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">8</font></b></div></div></div></foreignObject><text x="208" y="105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">8</text></switch></g><path d="M 107.54 1 L 147.54 1 L 167.54 21 L 167.54 61 L 127.54 61 L 107.54 41 L 107.54 1 Z" fill-opacity="0.2" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><path d="M 107.54 1 L 147.54 1 L 167.54 21 L 127.54 21 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 1 L 127.54 21 L 127.54 61 L 107.54 41 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 127.54 61 L 127.54 21 L 107.54 1 M 127.54 21 L 167.54 21" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 41px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">11</font></b></div></div></div></foreignObject><text x="148" y="45" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><path d="M 67.54 1 L 107.54 1 L 127.54 21 L 127.54 61 L 87.54 61 L 67.54 41 L 67.54 1 Z" fill-opacity="0.2" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><path d="M 67.54 1 L 107.54 1 L 127.54 21 L 87.54 21 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 67.54 1 L 87.54 21 L 87.54 61 L 67.54 41 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 61 L 87.54 21 L 67.54 1 M 87.54 21 L 127.54 21" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 41px; margin-left: 89px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">7</font></b></div></div></div></foreignObject><text x="108" y="45" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">7</text></switch></g><path d="M 87.54 21 L 127.54 21 L 147.54 41 L 147.54 81 L 107.54 81 L 87.54 61 L 87.54 21 Z" fill-opacity="0.2" fill="#ffe6cc" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><path d="M 87.54 21 L 127.54 21 L 147.54 41 L 107.54 41 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 21 L 107.54 41 L 107.54 81 L 87.54 61 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 81 L 107.54 41 L 87.54 21 M 107.54 41 L 147.54 41" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 61px; margin-left: 109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">6</font></b></div></div></div></foreignObject><text x="128" y="65" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">6</text></switch></g><path d="M 107.54 41 L 147.54 41 L 167.54 61 L 167.54 101 L 127.54 101 L 107.54 81 L 107.54 41 Z" fill-opacity="0.2" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><path d="M 107.54 41 L 147.54 41 L 167.54 61 L 127.54 61 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 41 L 127.54 61 L 127.54 101 L 107.54 81 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 127.54 101 L 127.54 61 L 107.54 41 M 127.54 61 L 167.54 61" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 81px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">5</font></b></div></div></div></foreignObject><text x="148" y="85" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">5</text></switch></g><path d="M 127.54 61 L 167.54 61 L 187.54 81 L 187.54 121 L 147.54 121 L 127.54 101 L 127.54 61 Z" fill-opacity="0.2" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><path d="M 127.54 61 L 167.54 61 L 187.54 81 L 147.54 81 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 127.54 61 L 147.54 81 L 147.54 121 L 127.54 101 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 147.54 121 L 147.54 81 L 127.54 61 M 147.54 81 L 187.54 81" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 101px; margin-left: 149px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">4</font></b></div></div></div></foreignObject><text x="168" y="105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">4</text></switch></g><path d="M 27.54 1 L 67.54 1 L 87.54 21 L 87.54 61 L 47.54 61 L 27.54 41 L 27.54 1 Z" fill-opacity="0.2" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><path d="M 27.54 1 L 67.54 1 L 87.54 21 L 47.54 21 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 27.54 1 L 47.54 21 L 47.54 61 L 27.54 41 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 47.54 61 L 47.54 21 L 27.54 1 M 47.54 21 L 87.54 21" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 41px; margin-left: 49px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">3</font></b></div></div></div></foreignObject><text x="68" y="45" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">3</text></switch></g><path d="M 47.54 21 L 87.54 21 L 107.54 41 L 107.54 81 L 67.54 81 L 47.54 61 L 47.54 21 Z" fill-opacity="0.2" fill="#ffe6cc" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><path d="M 47.54 21 L 87.54 21 L 107.54 41 L 67.54 41 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 47.54 21 L 67.54 41 L 67.54 81 L 47.54 61 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 67.54 81 L 67.54 41 L 47.54 21 M 67.54 41 L 107.54 41" fill="none" stroke="#d79b00" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 61px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">2</font></b></div></div></div></foreignObject><text x="88" y="65" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">2</text></switch></g><path d="M 67.54 41 L 107.54 41 L 127.54 61 L 127.54 101 L 87.54 101 L 67.54 81 L 67.54 41 Z" fill-opacity="0.2" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><path d="M 67.54 41 L 107.54 41 L 127.54 61 L 87.54 61 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 67.54 41 L 87.54 61 L 87.54 101 L 67.54 81 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 101 L 87.54 61 L 67.54 41 M 87.54 61 L 127.54 61" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 81px; margin-left: 89px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">1</font></b></div></div></div></foreignObject><text x="108" y="85" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">1</text></switch></g><path d="M 87.54 61 L 127.54 61 L 147.54 81 L 147.54 121 L 107.54 121 L 87.54 101 L 87.54 61 Z" fill-opacity="0.2" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><path d="M 87.54 61 L 127.54 61 L 147.54 81 L 107.54 81 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 61 L 107.54 81 L 107.54 121 L 87.54 101 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 121 L 107.54 81 L 87.54 61 M 107.54 81 L 147.54 81" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 101px; margin-left: 109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b><font style="font-size: 16px">0</font></b></div></div></div></foreignObject><text x="128" y="105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">0</text></switch></g><path d="M 107.54 281 L 107.54 301 M 227.54 281 L 227.54 301 M 107.54 291 L 227.54 291" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 291px; margin-left: 109px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b style="background-color: rgb(255 , 255 , 255)">width = 3</b></div></div></div></foreignObject><text x="168" y="295" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">width = 3</text></switch></g><path d="M -82.46 91 L -82.46 111 M 117.54 91 L 117.54 111 M -82.46 101 L 117.54 101" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" transform="rotate(90,17.54,101)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 101px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><span style="background-color: rgb(255 , 255 , 255)"><b>height = 5</b></span></div></div></div></foreignObject><text x="18" y="105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">hei...</text></switch></g><path d="M 87.54 141 L 127.54 141 L 147.54 161 L 147.54 201 L 107.54 201 L 87.54 181 L 87.54 141 Z" fill-opacity="0.01" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 87.54 141 L 127.54 141 L 147.54 161 L 107.54 161 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 141 L 107.54 161 L 107.54 201 L 87.54 181 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 201 L 107.54 161 L 87.54 141 M 107.54 161 L 147.54 161" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 87.54 181 L 127.54 181 L 147.54 201 L 147.54 241 L 107.54 241 L 87.54 221 L 87.54 181 Z" fill-opacity="0.01" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 87.54 181 L 127.54 181 L 147.54 201 L 107.54 201 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 181 L 107.54 201 L 107.54 241 L 87.54 221 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 241 L 107.54 201 L 87.54 181 M 107.54 201 L 147.54 201" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 87.54 221 L 127.54 221 L 147.54 241 L 147.54 281 L 107.54 281 L 87.54 261 L 87.54 221 Z" fill-opacity="0.01" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 87.54 221 L 127.54 221 L 147.54 241 L 107.54 241 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="none"/><path d="M 87.54 221 L 107.54 241 L 107.54 281 L 87.54 261 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="none"/><path d="M 107.54 281 L 107.54 241 L 87.54 221 M 107.54 241 L 147.54 241" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 4 237.46 L 4 257.46 M 118 237.46 L 118 257.46 M 4 247.46 L 118 247.46" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" transform="rotate(45,61,247.46)" pointer-events="none"/><g transform="translate(-0.5 -0.5)rotate(45 61 247.46000000000004)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 112px; height: 1px; padding-top: 247px; margin-left: 5px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; "><b style="background-color: rgb(255 , 255 , 255)">channel = 4</b></div></div></div></foreignObject><text x="61" y="251" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">channel = 4</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>
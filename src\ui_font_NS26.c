/*******************************************************************************
 * Size: 26 px
 * Bpp: 4
 * Opts: --bpp 4 --size 26 --font D:/Fusiontech/GUI/Coffee/assets/NotoSansTC-Regular.ttf -o D:/Fusiontech/GUI/Coffee/assets\ui_font_NS26.c --format lvgl -r 0x20-0x7f --symbols ＜主選單大小杯濃縮美式除鈣語言聯絡客服沖泡中，請稍候取消文出水心燙手電話熱停止清潔將右側旋鈕調先至蒸氣位置下一步將從噴氣口出等待加 --no-compress --no-prefilter
 ******************************************************************************/

#include "ui.h"

#ifndef UI_FONT_NS26
#define UI_FONT_NS26 1
#endif

#if UI_FONT_NS26

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0xf5, 0xf, 0xf5, 0xf, 0xf5, 0xe, 0xf4,
    0xe, 0xf4, 0xe, 0xf4, 0xd, 0xf3, 0xd, 0xf3,
    0xc, 0xf2, 0xc, 0xf2, 0xb, 0xf1, 0xb, 0xf1,
    0xa, 0xf1, 0xa, 0xf0, 0x2, 0x30, 0x0, 0x0,
    0x8, 0xa2, 0x6f, 0xfc, 0x7f, 0xfd, 0x1d, 0xf5,

    /* U+0022 "\"" */
    0xaf, 0xd0, 0x8, 0xff, 0xaf, 0xc0, 0x8, 0xfe,
    0x9f, 0xc0, 0x8, 0xfe, 0x8f, 0xb0, 0x7, 0xfd,
    0x7f, 0xa0, 0x5, 0xfb, 0x5f, 0x80, 0x3, 0xfa,
    0x4f, 0x70, 0x2, 0xf8, 0x2c, 0x40, 0x0, 0xc5,

    /* U+0023 "#" */
    0x0, 0x1, 0xf6, 0x0, 0xb, 0xd0, 0x0, 0x0,
    0x3f, 0x40, 0x0, 0xdb, 0x0, 0x0, 0x5, 0xf3,
    0x0, 0xf, 0xa0, 0x0, 0x0, 0x7f, 0x10, 0x0,
    0xf8, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x2f, 0x60,
    0x0, 0x0, 0xad, 0x0, 0x4, 0xf4, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x94, 0xbb, 0xfe,
    0xbb, 0xbd, 0xfb, 0xb6, 0x0, 0xf, 0x80, 0x0,
    0x9f, 0x0, 0x0, 0x2, 0xf6, 0x0, 0xc, 0xd0,
    0x0, 0x0, 0x4f, 0x40, 0x0, 0xeb, 0x0, 0x0,
    0x6, 0xf2, 0x0, 0xf, 0x90, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa, 0xbe, 0xfb, 0xbb,
    0xcf, 0xcb, 0xb0, 0x0, 0xcc, 0x0, 0x5, 0xf3,
    0x0, 0x0, 0xe, 0xa0, 0x0, 0x7f, 0x10, 0x0,
    0x0, 0xf9, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0xbd, 0x0, 0x0, 0x3, 0xf5, 0x0,
    0xc, 0xc0, 0x0, 0x0, 0x5f, 0x30, 0x0, 0xea,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf3,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xfe, 0xa3, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0x70, 0x2, 0xff,
    0xb3, 0x12, 0x8f, 0xf1, 0x8, 0xfe, 0x0, 0x0,
    0x4, 0x40, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x81, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xfe, 0x70, 0x0, 0x0, 0x0,
    0x6, 0xef, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0xb, 0x60, 0x0, 0x0, 0x1f, 0xf6,
    0x4f, 0xfb, 0x41, 0x14, 0xcf, 0xe0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x18, 0xcf, 0xfe,
    0x91, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf3, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x8e, 0xfd, 0x50, 0x0, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0xb, 0xfb, 0x8d, 0xf6, 0x0,
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x5f, 0x90,
    0x1, 0xef, 0x10, 0x0, 0x0, 0xeb, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x7f, 0x60, 0x0, 0x7,
    0xf2, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x3f,
    0x90, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x0, 0xfd,
    0x0, 0x0, 0x2f, 0xa0, 0x0, 0x7f, 0x20, 0x0,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x2f, 0xa0, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x4f, 0x90, 0x8, 0xf1, 0x0, 0x58, 0x61, 0x0,
    0xaf, 0x20, 0x0, 0x7f, 0x50, 0x1f, 0x80, 0x1d,
    0xff, 0xfe, 0x30, 0x5f, 0xa0, 0x1, 0xef, 0x0,
    0x9f, 0x10, 0xcf, 0x60, 0x4e, 0xe1, 0xb, 0xfb,
    0x8d, 0xf6, 0x2, 0xf7, 0x3, 0xf9, 0x0, 0x6,
    0xf7, 0x0, 0x8e, 0xfd, 0x50, 0xa, 0xe0, 0x7,
    0xf4, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x60, 0xa, 0xf1, 0x0, 0x0, 0xed, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x3, 0xf6, 0x0,
    0xa, 0xf1, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0,
    0xc, 0xd0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x5, 0xf6,
    0x0, 0x3, 0xf9, 0x0, 0x0, 0x0, 0xcc, 0x0,
    0x0, 0x0, 0xfd, 0x10, 0xb, 0xf3, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x6f, 0xd8, 0xcf,
    0x90, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0, 0x0,
    0x5, 0xdf, 0xe7, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5, 0xcf, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfc, 0xdf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0xc, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x6,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x7, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf9,
    0x0, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x5d, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x3c, 0xa0, 0x0, 0x2e, 0xfe, 0xfd, 0x10, 0x0,
    0x0, 0x9f, 0x90, 0x1, 0xef, 0xb1, 0xcf, 0xb0,
    0x0, 0x0, 0xef, 0x30, 0x9, 0xfd, 0x0, 0x2f,
    0xf9, 0x0, 0x6, 0xfd, 0x0, 0xe, 0xf6, 0x0,
    0x4, 0xff, 0x80, 0xe, 0xf5, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0x6f, 0xf9, 0x9f, 0xc0, 0x0, 0xf,
    0xf6, 0x0, 0x0, 0x7, 0xff, 0xff, 0x20, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0xbf, 0xfe, 0x40,
    0x0, 0x4, 0xff, 0xc3, 0x0, 0x3b, 0xff, 0xff,
    0xfb, 0x40, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf5,
    0x19, 0xff, 0xf1, 0x0, 0x3, 0xae, 0xff, 0xc8,
    0x10, 0x0, 0x29, 0xb0,

    /* U+0027 "'" */
    0xaf, 0xda, 0xfc, 0x9f, 0xc8, 0xfb, 0x7f, 0xa5,
    0xf8, 0x4f, 0x72, 0xc4,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xd4, 0x0, 0x9,
    0xf3, 0x0, 0x2f, 0xb0, 0x0, 0x8f, 0x40, 0x0,
    0xee, 0x0, 0x5, 0xf9, 0x0, 0xa, 0xf4, 0x0,
    0xe, 0xf1, 0x0, 0x2f, 0xd0, 0x0, 0x4f, 0xb0,
    0x0, 0x7f, 0x80, 0x0, 0x8f, 0x70, 0x0, 0x9f,
    0x60, 0x0, 0x9f, 0x60, 0x0, 0x9f, 0x60, 0x0,
    0x8f, 0x70, 0x0, 0x7f, 0x80, 0x0, 0x5f, 0xa0,
    0x0, 0x2f, 0xc0, 0x0, 0xf, 0xf0, 0x0, 0xb,
    0xf3, 0x0, 0x6, 0xf8, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0xaf, 0x20, 0x0, 0x3f, 0x90, 0x0, 0xc,
    0xf1, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x10,

    /* U+0029 ")" */
    0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0xe, 0xe0, 0x0, 0x8, 0xf5, 0x0, 0x2,
    0xfc, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x7f, 0x60,
    0x0, 0x4f, 0xb0, 0x0, 0xf, 0xe0, 0x0, 0xd,
    0xf1, 0x0, 0xb, 0xf3, 0x0, 0xa, 0xf4, 0x0,
    0x9, 0xf5, 0x0, 0x8, 0xf6, 0x0, 0x9, 0xf5,
    0x0, 0x9, 0xf5, 0x0, 0xb, 0xf3, 0x0, 0xd,
    0xf1, 0x0, 0xf, 0xf0, 0x0, 0x3f, 0xc0, 0x0,
    0x6f, 0x80, 0x0, 0xbf, 0x30, 0x0, 0xfd, 0x0,
    0x6, 0xf7, 0x0, 0xd, 0xf1, 0x0, 0x5f, 0x80,
    0x0, 0xaf, 0x10, 0x0, 0x1, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0x0, 0xce,
    0x0, 0x0, 0x6, 0x20, 0xdf, 0x2, 0x60, 0x2f,
    0xfe, 0xff, 0xef, 0xf4, 0x3, 0xaf, 0xff, 0xfb,
    0x40, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x5f,
    0xcb, 0xf6, 0x0, 0x0, 0xde, 0x10, 0xde, 0x10,
    0x0, 0x63, 0x0, 0x27, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x7a, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0x0, 0xd, 0xdd, 0xdd, 0xff, 0xdd, 0xdd,
    0xd6, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0x0,

    /* U+002C "," */
    0x1, 0x63, 0x0, 0xdf, 0xf4, 0xf, 0xff, 0xa0,
    0x8f, 0xfb, 0x0, 0x1f, 0xa0, 0x6, 0xf6, 0x2,
    0xee, 0x5, 0xff, 0x30, 0x4c, 0x20, 0x0, 0x0,
    0x0,

    /* U+002D "-" */
    0xbe, 0xee, 0xee, 0xcc, 0xff, 0xff, 0xfe,

    /* U+002E "." */
    0x4, 0xb7, 0x0, 0xff, 0xf3, 0x1f, 0xff, 0x40,
    0x8f, 0xb0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0x0,
    0x5, 0xf5, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x8f, 0x20, 0x0, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x0, 0xc,
    0xf0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0x0, 0x5f, 0x50, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x4,
    0xf7, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x0,
    0x0, 0xd, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0x7f, 0x40, 0x0, 0x0, 0x0,
    0xcf, 0x0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0xa, 0xf1,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x0, 0x9f, 0x20, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x3, 0xbf, 0xfd, 0x70, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x2, 0xff, 0xa1,
    0x5, 0xff, 0x90, 0x0, 0xaf, 0xc0, 0x0, 0x5,
    0xff, 0x10, 0xf, 0xf5, 0x0, 0x0, 0xd, 0xf7,
    0x4, 0xff, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x7f,
    0xd0, 0x0, 0x0, 0x5, 0xfe, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0xaf, 0xa0, 0x0, 0x0,
    0x2, 0xff, 0x1b, 0xf9, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0xbf, 0x90, 0x0, 0x0, 0x2, 0xff, 0x2a,
    0xfa, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x9f, 0xb0,
    0x0, 0x0, 0x3, 0xff, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0x6f, 0xe0, 0x4f, 0xf0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0xff, 0x50, 0x0, 0x0, 0xef, 0x70,
    0x9, 0xfd, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x2f,
    0xfa, 0x10, 0x5f, 0xf8, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xd7,
    0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x3, 0xaf, 0xe0, 0x0, 0x0, 0x9e, 0xff,
    0xfe, 0x0, 0x0, 0xd, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xbb, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+0032 "2" */
    0x0, 0x3a, 0xef, 0xfb, 0x50, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x9f, 0xf7, 0x10,
    0x2a, 0xff, 0x60, 0x5, 0xe3, 0x0, 0x0, 0xb,
    0xfd, 0x0, 0x1, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2,

    /* U+0033 "3" */
    0x0, 0x2, 0x9e, 0xff, 0xc7, 0x10, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x4, 0xff, 0x82,
    0x1, 0x6f, 0xfd, 0x0, 0x6, 0x20, 0x0, 0x0,
    0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0x30, 0x0, 0x0, 0x5e, 0xff,
    0xfa, 0x20, 0x0, 0x0, 0x6, 0xff, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x25, 0xbf, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x11, 0x0, 0x0, 0x0, 0xa, 0xfd,
    0xb, 0xd2, 0x0, 0x0, 0x3, 0xff, 0x81, 0xef,
    0xf7, 0x21, 0x26, 0xff, 0xe1, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x5b, 0xef, 0xfc,
    0x70, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x3f, 0xf1, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x6f, 0xe0, 0x2f, 0xf1,
    0x0, 0x0, 0x1, 0xef, 0x50, 0x2f, 0xf1, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x2f, 0xf1, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0xcf,
    0x80, 0x0, 0x2f, 0xf1, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x1e, 0xf4, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf1, 0x0,

    /* U+0035 "5" */
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x4f,
    0xe1, 0x11, 0x11, 0x11, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xab, 0xef, 0xe9, 0x20, 0x0,
    0x0, 0xbf, 0xff, 0xee, 0xff, 0xf5, 0x0, 0x0,
    0x5d, 0x60, 0x0, 0x4e, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xb0,
    0xa, 0xa0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x1f,
    0xfd, 0x62, 0x2, 0x8f, 0xfb, 0x0, 0x2, 0xdf,
    0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x5, 0xbe,
    0xff, 0xb5, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x3a, 0xef, 0xea, 0x30, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0x60, 0x0, 0x7f, 0xf9,
    0x30, 0x28, 0xf7, 0x0, 0x2f, 0xf7, 0x0, 0x0,
    0x2, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x1,
    0x8c, 0xca, 0x40, 0x0, 0x7f, 0xc4, 0xff, 0xff,
    0xff, 0x90, 0x8, 0xfe, 0xfb, 0x30, 0x18, 0xff,
    0x60, 0x8f, 0xf9, 0x0, 0x0, 0xa, 0xfd, 0x8,
    0xfd, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x7f, 0xc0,
    0x0, 0x0, 0x0, 0xff, 0x44, 0xfe, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x1f, 0xf2, 0x0, 0x0, 0x0,
    0xff, 0x30, 0xcf, 0x70, 0x0, 0x0, 0x4f, 0xf1,
    0x5, 0xfe, 0x10, 0x0, 0xb, 0xfb, 0x0, 0xc,
    0xfd, 0x30, 0x9, 0xff, 0x30, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x7, 0xdf, 0xfb,
    0x30, 0x0,

    /* U+0037 "7" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x11, 0x11, 0x11,
    0x11, 0x1c, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x4, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0x8,
    0xff, 0xed, 0xff, 0xf4, 0x0, 0x4, 0xff, 0x60,
    0x1, 0xaf, 0xe0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0xdf, 0x50, 0xd, 0xf5, 0x0, 0x0, 0x8, 0xf8,
    0x0, 0xdf, 0x50, 0x0, 0x0, 0x7f, 0x80, 0xa,
    0xf9, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x2f, 0xf6,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x20,
    0xaf, 0x40, 0x0, 0x0, 0x9f, 0xff, 0xdf, 0x50,
    0x0, 0x0, 0xbf, 0x97, 0xef, 0xfc, 0x10, 0x0,
    0xaf, 0x80, 0x0, 0x6f, 0xfe, 0x10, 0x4f, 0xd0,
    0x0, 0x0, 0x2e, 0xfa, 0xa, 0xf7, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xcf, 0x50, 0x0, 0x0, 0x1,
    0xff, 0x3b, 0xf6, 0x0, 0x0, 0x0, 0x1f, 0xf3,
    0x8f, 0xd0, 0x0, 0x0, 0x7, 0xff, 0x1, 0xef,
    0xc3, 0x0, 0x6, 0xff, 0x70, 0x3, 0xef, 0xfe,
    0xdf, 0xff, 0x90, 0x0, 0x1, 0x7c, 0xff, 0xea,
    0x40, 0x0,

    /* U+0039 "9" */
    0x0, 0x18, 0xdf, 0xea, 0x30, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xd, 0xfd, 0x20,
    0x7, 0xff, 0x50, 0x5, 0xff, 0x10, 0x0, 0x7,
    0xfe, 0x0, 0xbf, 0x90, 0x0, 0x0, 0xe, 0xf5,
    0xd, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0x90, 0xdf,
    0x60, 0x0, 0x0, 0x6, 0xfd, 0xc, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0x8f, 0xd0, 0x0, 0x0,
    0x1d, 0xff, 0x2, 0xff, 0xa1, 0x0, 0x4d, 0xff,
    0xf0, 0x6, 0xff, 0xfd, 0xef, 0xd5, 0xff, 0x0,
    0x3, 0xbe, 0xfd, 0x70, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0,
    0x3, 0x10, 0x0, 0x1, 0xef, 0x80, 0x2, 0xfe,
    0x61, 0x15, 0xdf, 0xd0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xd2, 0x0, 0x0, 0x7, 0xcf, 0xfd, 0x70,
    0x0, 0x0,

    /* U+003A ":" */
    0x8, 0xfb, 0x1, 0xff, 0xf4, 0xf, 0xff, 0x30,
    0x4b, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0x70, 0xf, 0xff,
    0x31, 0xff, 0xf4, 0x8, 0xfb, 0x0,

    /* U+003B ";" */
    0x8, 0xfb, 0x1, 0xff, 0xf4, 0xf, 0xff, 0x30,
    0x4b, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0x30, 0xd, 0xff,
    0x40, 0xff, 0xfa, 0x8, 0xff, 0xb0, 0x1, 0xfa,
    0x0, 0x6f, 0x60, 0x2e, 0xe0, 0x5f, 0xf3, 0x4,
    0xc2, 0x0, 0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xa7, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xdf, 0xf7, 0x0, 0x0, 0x0,
    0x39, 0xff, 0xfd, 0x71, 0x0, 0x0, 0x6c, 0xff,
    0xf9, 0x30, 0x0, 0x2, 0x9f, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0xf, 0xfe, 0x82, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x9f, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6d, 0xff, 0xf9, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xfd, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x17, 0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003D "=" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xd6, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff,
    0xc6, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xf9, 0x20, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xf7,
    0x0, 0x0, 0x0, 0x39, 0xef, 0xfc, 0x60, 0x0,
    0x1, 0x6c, 0xff, 0xf9, 0x30, 0x0, 0x4, 0xaf,
    0xff, 0xd6, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x6c, 0xff, 0xd8, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xd0, 0x9f, 0xc4, 0x11, 0x8f, 0xf8, 0x6,
    0x0, 0x0, 0xa, 0xfd, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x8f, 0xd0,
    0x0, 0x0, 0x5, 0xff, 0x30, 0x0, 0x0, 0x2f,
    0xf5, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0,
    0x3, 0xff, 0x10, 0x0, 0x0, 0x6, 0xfc, 0x0,
    0x0, 0x0, 0x5, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xa8, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x50, 0x0, 0x0, 0xe, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xfc, 0x10, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x49, 0xcf, 0xff, 0xda,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf,
    0xfd, 0xba, 0xbd, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x82, 0x0, 0x0, 0x2, 0x9f,
    0xf4, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0xa,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xc0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x40, 0x0, 0xef, 0x10,
    0x0, 0x1, 0x8b, 0x92, 0x47, 0x0, 0x2, 0xfa,
    0x0, 0x7f, 0x70, 0x0, 0x4, 0xff, 0xff, 0xec,
    0xe0, 0x0, 0xd, 0xe0, 0xd, 0xf1, 0x0, 0x2,
    0xff, 0x60, 0x2d, 0xfb, 0x0, 0x0, 0xaf, 0x1,
    0xfb, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6f, 0x80,
    0x0, 0x9, 0xf1, 0x4f, 0x70, 0x0, 0x3f, 0xb0,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x9f, 0x17, 0xf5,
    0x0, 0x8, 0xf6, 0x0, 0x0, 0xaf, 0x20, 0x0,
    0xa, 0xf0, 0x8f, 0x30, 0x0, 0xbf, 0x30, 0x0,
    0xd, 0xf0, 0x0, 0x0, 0xdd, 0x8, 0xf3, 0x0,
    0xc, 0xf2, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x3f,
    0x90, 0x7f, 0x40, 0x0, 0xbf, 0x40, 0x0, 0x5f,
    0xc0, 0x0, 0xb, 0xf2, 0x5, 0xf6, 0x0, 0x8,
    0xfc, 0x10, 0x6f, 0xef, 0x10, 0x8, 0xf8, 0x0,
    0x2f, 0xa0, 0x0, 0x1e, 0xff, 0xff, 0x87, 0xfe,
    0xbe, 0xfa, 0x0, 0x0, 0xef, 0x10, 0x0, 0x2b,
    0xfd, 0x60, 0x8, 0xef, 0xc6, 0x0, 0x0, 0x7,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfb, 0x30, 0x0, 0x0,
    0x17, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xeb, 0xaa, 0xcf, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x9c, 0xef, 0xfd, 0xa5,
    0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfb, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf6, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x6f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xd0, 0x2f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x90, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x8, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x8, 0xfb, 0x0, 0x0, 0xff, 0x40, 0x0,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x0, 0x6f, 0xe0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x0, 0x7, 0xfe, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x1f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf8,

    /* U+0042 "B" */
    0x6f, 0xff, 0xff, 0xfe, 0xc8, 0x20, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x6f, 0xf0,
    0x0, 0x1, 0x5d, 0xff, 0x30, 0x6f, 0xf0, 0x0,
    0x0, 0x1, 0xef, 0x90, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xaf,
    0xa0, 0x6f, 0xf0, 0x0, 0x0, 0x2, 0xff, 0x50,
    0x6f, 0xf0, 0x0, 0x2, 0x6e, 0xfb, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x6f, 0xf0, 0x0,
    0x0, 0x26, 0xef, 0xd1, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x1e, 0xf9, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x3f, 0xf8, 0x6f,
    0xf0, 0x0, 0x1, 0x38, 0xff, 0xe1, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0x6f, 0xff, 0xff,
    0xff, 0xda, 0x50, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x4, 0xad, 0xff, 0xd8, 0x10, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x1d, 0xff, 0xc6, 0x33, 0x6d, 0xfe, 0x0, 0xb,
    0xff, 0x60, 0x0, 0x0, 0xa, 0x40, 0x4, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0xcf, 0xf5, 0x0, 0x0, 0x0, 0x8e, 0x20,
    0x1, 0xef, 0xfb, 0x53, 0x36, 0xdf, 0xf5, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x5a, 0xef, 0xfd, 0x81, 0x0,

    /* U+0044 "D" */
    0x6f, 0xff, 0xff, 0xfd, 0x93, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x6f,
    0xf0, 0x0, 0x15, 0xbf, 0xfe, 0x10, 0x6, 0xff,
    0x0, 0x0, 0x0, 0x6f, 0xfc, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x7f, 0xf5, 0x6, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xb0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x6, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x46, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf5, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x56, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf4, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x26, 0xff, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xfa,
    0x6, 0xff, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x6f, 0xf0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x6,
    0xff, 0x10, 0x12, 0x6c, 0xff, 0xd1, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xd9, 0x30, 0x0, 0x0,

    /* U+0045 "E" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x6f, 0xf1, 0x11, 0x11,
    0x11, 0x10, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x22, 0x22, 0x22, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x22, 0x22, 0x22, 0x21, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+0046 "F" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x6f, 0xf1, 0x11, 0x11,
    0x11, 0x10, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x6f, 0xf1, 0x11, 0x11, 0x11, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xea, 0x40, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xc, 0xff, 0xd7, 0x43, 0x49, 0xff, 0x70, 0xa,
    0xff, 0x80, 0x0, 0x0, 0x2, 0x90, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x7f, 0xf0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xf6, 0xff, 0x10, 0x0, 0x0, 0x11,
    0x14, 0xff, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf1, 0xff, 0x80, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0, 0x5f, 0xf0,
    0x1, 0xdf, 0xfc, 0x63, 0x34, 0x9f, 0xff, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x4a, 0xdf, 0xfe, 0xb5, 0x0,

    /* U+0048 "H" */
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x56,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x56, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x56, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf5, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x56, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf5, 0x6f, 0xf2, 0x22, 0x22,
    0x22, 0x23, 0xff, 0x56, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x56, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf5, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x56, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf5, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x56, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x56,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x56, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,

    /* U+0049 "I" */
    0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0,
    0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0,
    0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0,
    0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0,
    0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0, 0x6f, 0xf0,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0x20, 0x0, 0x0, 0x3f, 0xf4,
    0x8, 0xf4, 0x0, 0x0, 0x8f, 0xf0, 0xb, 0xff,
    0x73, 0x38, 0xff, 0x90, 0x1, 0xcf, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x6, 0xcf, 0xfd, 0x80, 0x0,

    /* U+004B "K" */
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x5f, 0xf6, 0x6,
    0xff, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x1d, 0xfc, 0x0, 0x6, 0xff,
    0x0, 0x0, 0xb, 0xfe, 0x10, 0x0, 0x6f, 0xf0,
    0x0, 0x8, 0xff, 0x30, 0x0, 0x6, 0xff, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x6f, 0xf0, 0x2,
    0xff, 0xa0, 0x0, 0x0, 0x6, 0xff, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0xbf, 0xff,
    0x40, 0x0, 0x0, 0x6, 0xff, 0x8f, 0xfe, 0xfd,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9, 0x3f, 0xf6,
    0x0, 0x0, 0x6, 0xff, 0xfb, 0x0, 0xaf, 0xe0,
    0x0, 0x0, 0x6f, 0xfe, 0x10, 0x2, 0xff, 0x80,
    0x0, 0x6, 0xff, 0x30, 0x0, 0x8, 0xff, 0x10,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x1e, 0xfa, 0x0,
    0x6, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x6,
    0xff, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x6, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6,

    /* U+004C "L" */
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x22, 0x22, 0x22, 0x20, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+004D "M" */
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x76, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf7, 0x6f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x76, 0xfc, 0xfb, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0xf7, 0x6f, 0x9e, 0xf1, 0x0, 0x0, 0x0,
    0xee, 0x8f, 0x76, 0xfa, 0x9f, 0x60, 0x0, 0x0,
    0x4f, 0xa9, 0xf7, 0x6f, 0xb4, 0xfb, 0x0, 0x0,
    0x9, 0xf4, 0xaf, 0x76, 0xfc, 0xe, 0xf1, 0x0,
    0x0, 0xee, 0xb, 0xf7, 0x6f, 0xc0, 0x9f, 0x60,
    0x0, 0x4f, 0x90, 0xbf, 0x76, 0xfc, 0x3, 0xfc,
    0x0, 0xa, 0xf4, 0xc, 0xf7, 0x6f, 0xc0, 0xe,
    0xf1, 0x0, 0xfe, 0x0, 0xcf, 0x76, 0xfc, 0x0,
    0x8f, 0x70, 0x4f, 0x90, 0xc, 0xf7, 0x6f, 0xc0,
    0x2, 0xfc, 0x9, 0xf3, 0x0, 0xcf, 0x76, 0xfc,
    0x0, 0xd, 0xf1, 0xee, 0x0, 0xc, 0xf7, 0x6f,
    0xc0, 0x0, 0x7f, 0xaf, 0x80, 0x0, 0xcf, 0x76,
    0xfc, 0x0, 0x2, 0xff, 0xf3, 0x0, 0xc, 0xf7,
    0x6f, 0xc0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0xcf,
    0x76, 0xfc, 0x0, 0x0, 0x7f, 0x70, 0x0, 0xc,
    0xf7, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x76, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf7,

    /* U+004E "N" */
    0x6f, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xff, 0x26,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x6f,
    0xff, 0x70, 0x0, 0x0, 0x1, 0xff, 0x26, 0xfd,
    0xfe, 0x10, 0x0, 0x0, 0x1f, 0xf2, 0x6f, 0xab,
    0xf8, 0x0, 0x0, 0x1, 0xff, 0x26, 0xfb, 0x2f,
    0xf2, 0x0, 0x0, 0x1f, 0xf2, 0x6f, 0xc0, 0xaf,
    0xa0, 0x0, 0x1, 0xff, 0x26, 0xfd, 0x2, 0xff,
    0x30, 0x0, 0x1f, 0xf2, 0x6f, 0xe0, 0x9, 0xfc,
    0x0, 0x1, 0xff, 0x26, 0xfe, 0x0, 0x1f, 0xf5,
    0x0, 0x1f, 0xf2, 0x6f, 0xe0, 0x0, 0x8f, 0xd0,
    0x1, 0xff, 0x26, 0xfe, 0x0, 0x0, 0xef, 0x60,
    0x1f, 0xf2, 0x6f, 0xe0, 0x0, 0x6, 0xfe, 0x0,
    0xff, 0x26, 0xfe, 0x0, 0x0, 0xd, 0xf7, 0xf,
    0xf2, 0x6f, 0xe0, 0x0, 0x0, 0x5f, 0xe0, 0xff,
    0x26, 0xfe, 0x0, 0x0, 0x0, 0xcf, 0x7e, 0xf2,
    0x6f, 0xe0, 0x0, 0x0, 0x3, 0xfe, 0xdf, 0x26,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf2, 0x6f,
    0xe0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x26, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,

    /* U+004F "O" */
    0x0, 0x0, 0x6, 0xbe, 0xff, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x2e, 0xff, 0xa5, 0x34, 0x8f, 0xff,
    0x50, 0x0, 0xc, 0xff, 0x40, 0x0, 0x0, 0x1d,
    0xff, 0x20, 0x6, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x2f, 0xfa, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf1, 0x1f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x64, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0x6f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xb7, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfc, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xc6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x4f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x91, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0xc,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x5f, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0xcf, 0xf5, 0x0, 0x0, 0x2, 0xdf, 0xf1,
    0x0, 0x1, 0xef, 0xfa, 0x53, 0x48, 0xff, 0xf5,
    0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x5b, 0xef, 0xfc, 0x71,
    0x0, 0x0,

    /* U+0050 "P" */
    0x6f, 0xff, 0xff, 0xfe, 0xc8, 0x20, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x6f, 0xf0,
    0x0, 0x2, 0x6d, 0xff, 0x50, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0xd0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x6f, 0xf0, 0x0, 0x0, 0x1, 0xef, 0xb0, 0x6f,
    0xf0, 0x0, 0x2, 0x6e, 0xff, 0x20, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x6f, 0xff, 0xff,
    0xfe, 0xc7, 0x10, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x6, 0xbe, 0xff, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xa5, 0x34, 0x8f,
    0xff, 0x50, 0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0,
    0x1, 0xdf, 0xf2, 0x0, 0x6, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x2f, 0xfa, 0x0, 0xc, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x10, 0x1f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60, 0x4f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x6f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xb0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xc0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x6f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xb0, 0x4f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x90, 0x1f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0xc, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x10, 0x5,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0xcf, 0xf3, 0x0, 0x0, 0x1, 0xcf, 0xf1,
    0x0, 0x0, 0x1e, 0xff, 0x83, 0x12, 0x6e, 0xff,
    0x50, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xff,
    0xc7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0x83, 0x12, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbe, 0xfe,
    0xc1,

    /* U+0052 "R" */
    0x6f, 0xff, 0xff, 0xff, 0xd9, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x6f, 0xf0,
    0x0, 0x1, 0x5c, 0xff, 0x60, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0xe0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x3f,
    0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x6f, 0xf0, 0x0, 0x0, 0x1, 0xef, 0xb0, 0x6f,
    0xf0, 0x0, 0x1, 0x6d, 0xff, 0x30, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf8, 0x10, 0x0, 0x6f, 0xf0, 0x0, 0x3f,
    0xf6, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0xa, 0xfe,
    0x10, 0x0, 0x6f, 0xf0, 0x0, 0x2, 0xff, 0x90,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x8f, 0xf2, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0xe, 0xfb, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x5, 0xff, 0x50, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0xcf, 0xd0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x3f, 0xf7,

    /* U+0053 "S" */
    0x0, 0x1, 0x8d, 0xff, 0xea, 0x30, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x2, 0xff,
    0xe7, 0x33, 0x5a, 0xff, 0x80, 0x9, 0xff, 0x10,
    0x0, 0x0, 0x3b, 0x0, 0xd, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xe7, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xcf, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf5, 0x4, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x3f, 0xc2, 0x0, 0x0, 0x0, 0xbf, 0xf0, 0x5f,
    0xff, 0xa5, 0x33, 0x6c, 0xff, 0x70, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xbe,
    0xff, 0xd9, 0x30, 0x0,

    /* U+0054 "T" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x1,
    0x11, 0x11, 0x7f, 0xf1, 0x11, 0x11, 0x10, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x37,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x37, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x7f, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x37, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x37, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf3, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x37, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x37, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x35, 0xff, 0x10, 0x0, 0x0, 0x0, 0x3f,
    0xf1, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xcf, 0xb0,
    0x8, 0xff, 0x40, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x1e, 0xff, 0x94, 0x35, 0xaf, 0xfc, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x17, 0xcf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0056 "V" */
    0xdf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc8,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x3f,
    0xf4, 0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x9, 0xfd,
    0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0x40, 0x0, 0xff, 0x60,
    0x0, 0x0, 0x5f, 0xf0, 0x0, 0xb, 0xfb, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0xef, 0x60, 0x0, 0x1, 0xff, 0x40, 0x0,
    0x3f, 0xf1, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x7,
    0xfc, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0xcf,
    0x70, 0x0, 0x0, 0x2, 0xff, 0x10, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0xd, 0xf6, 0x4, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xa0, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xef, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0,

    /* U+0057 "W" */
    0x3f, 0xf3, 0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0xff, 0x60, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0xd,
    0xf9, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0,
    0xa, 0xfa, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x7f,
    0xdf, 0x70, 0x0, 0x0, 0xdf, 0x70, 0x6, 0xff,
    0x0, 0x0, 0xb, 0xf7, 0xfa, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0xfe, 0x1f,
    0xe0, 0x0, 0x3, 0xff, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x3f, 0xb0, 0xef, 0x20, 0x0, 0x6f, 0xd0,
    0x0, 0xd, 0xf8, 0x0, 0x7, 0xf7, 0xb, 0xf6,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0xaf, 0x40, 0x7f, 0xa0, 0x0, 0xcf, 0x70, 0x0,
    0x6, 0xfe, 0x0, 0xe, 0xf0, 0x3, 0xfe, 0x0,
    0xf, 0xf4, 0x0, 0x0, 0x3f, 0xf1, 0x2, 0xfc,
    0x0, 0xf, 0xf2, 0x2, 0xff, 0x10, 0x0, 0x0,
    0xff, 0x40, 0x6f, 0x90, 0x0, 0xbf, 0x50, 0x5f,
    0xd0, 0x0, 0x0, 0xc, 0xf6, 0x9, 0xf5, 0x0,
    0x8, 0xf9, 0x7, 0xfa, 0x0, 0x0, 0x0, 0x9f,
    0x90, 0xcf, 0x10, 0x0, 0x4f, 0xc0, 0xaf, 0x70,
    0x0, 0x0, 0x6, 0xfc, 0xf, 0xd0, 0x0, 0x0,
    0xff, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x3f, 0xf3,
    0xfa, 0x0, 0x0, 0xc, 0xf3, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xff, 0x9f, 0x60, 0x0, 0x0, 0x8f,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf2,
    0x0, 0x0, 0x5, 0xfe, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x6, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xdf, 0xf4, 0x0, 0x0,

    /* U+0058 "X" */
    0xe, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x6f, 0xf3, 0x0, 0x0, 0x3, 0xff, 0x40, 0x0,
    0xdf, 0xb0, 0x0, 0x0, 0xbf, 0xc0, 0x0, 0x4,
    0xff, 0x30, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0xc,
    0xfc, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x3f,
    0xf4, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0xbf,
    0xc0, 0xaf, 0xa0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xbf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xb1, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0x8, 0xfe, 0x0,
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x1f, 0xf7, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x0, 0x8f, 0xf1, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0x1, 0xef, 0x90, 0x0,
    0x3f, 0xf4, 0x0, 0x0, 0x7, 0xff, 0x20, 0xc,
    0xfb, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x7f, 0xf3,

    /* U+0059 "Y" */
    0xc, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x90,
    0x5f, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0xdf, 0x90, 0x0, 0x0, 0xb, 0xfa, 0x0, 0x6,
    0xff, 0x10, 0x0, 0x2, 0xff, 0x30, 0x0, 0xe,
    0xf7, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x7f,
    0xe0, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0, 0xff,
    0x50, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0xef, 0x50, 0x0, 0x0, 0x0, 0x1f, 0xf2,
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x9c,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x10, 0x0, 0x0,

    /* U+005A "Z" */
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x1, 0x11,
    0x11, 0x11, 0x12, 0xef, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf9, 0x22, 0x22, 0x22, 0x22, 0x20, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7,

    /* U+005B "[" */
    0x4f, 0xff, 0xfe, 0x4f, 0xb7, 0x76, 0x4f, 0x80,
    0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f,
    0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0,
    0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80,
    0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f,
    0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0,
    0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80,
    0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f,
    0x80, 0x0, 0x4f, 0x80, 0x0, 0x4f, 0x80, 0x0,
    0x4f, 0x91, 0x11, 0x4f, 0xff, 0xfe, 0x14, 0x44,
    0x44,

    /* U+005C "\\" */
    0x7f, 0x40, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x0,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0x0, 0x9,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x10, 0x0, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0,
    0x2, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xed, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x20, 0x0, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x0, 0xf, 0xb0, 0x0,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x6,
    0xf5, 0x0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xce, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0,
    0x0, 0xe, 0xd0, 0x0, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x0, 0x0, 0x4, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xfb,

    /* U+005D "]" */
    0x1f, 0xff, 0xff, 0x10, 0x77, 0x7d, 0xf1, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0xb, 0xf1, 0x0, 0x0, 0xbf,
    0x10, 0x0, 0xb, 0xf1, 0x0, 0x0, 0xbf, 0x10,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0xbf, 0x10, 0x0,
    0xb, 0xf1, 0x0, 0x0, 0xbf, 0x10, 0x0, 0xb,
    0xf1, 0x0, 0x0, 0xbf, 0x10, 0x0, 0xb, 0xf1,
    0x0, 0x0, 0xbf, 0x10, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0xb, 0xf1, 0x0, 0x0, 0xbf,
    0x10, 0x0, 0xb, 0xf1, 0x1, 0x11, 0xcf, 0x11,
    0xff, 0xff, 0xf1, 0x4, 0x44, 0x44, 0x0,

    /* U+005E "^" */
    0x0, 0x0, 0x7, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xfe, 0x7f, 0x60, 0x0,
    0x0, 0x5, 0xf8, 0x1f, 0xc0, 0x0, 0x0, 0xc,
    0xf3, 0xc, 0xf3, 0x0, 0x0, 0x2f, 0xd0, 0x6,
    0xf9, 0x0, 0x0, 0x8f, 0x70, 0x1, 0xfe, 0x0,
    0x0, 0xef, 0x10, 0x0, 0xaf, 0x50, 0x4, 0xfc,
    0x0, 0x0, 0x4f, 0xb0, 0xa, 0xf6, 0x0, 0x0,
    0xe, 0xf1, 0x1f, 0xf0, 0x0, 0x0, 0x9, 0xf7,

    /* U+005F "_" */
    0x69, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x1a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+0060 "`" */
    0x0, 0x30, 0x0, 0x0, 0x9f, 0x30, 0x0, 0xd,
    0xfe, 0x10, 0x0, 0x1d, 0xfc, 0x0, 0x0, 0x1d,
    0xfa, 0x0, 0x0, 0x1d, 0xf4, 0x0, 0x0, 0x17,
    0x0,

    /* U+0061 "a" */
    0x0, 0x5, 0xbe, 0xfe, 0xb3, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x8, 0xfc, 0x51, 0x14,
    0xdf, 0xe0, 0x1, 0x40, 0x0, 0x0, 0x3f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0x4e, 0xf8, 0x0, 0x1, 0x7b, 0xff,
    0xff, 0xf9, 0x0, 0x8f, 0xfe, 0xa7, 0x5d, 0xf9,
    0xa, 0xfe, 0x50, 0x0, 0xd, 0xf9, 0x3f, 0xf3,
    0x0, 0x0, 0xd, 0xf9, 0x7f, 0xd0, 0x0, 0x0,
    0xd, 0xf9, 0x6f, 0xe0, 0x0, 0x0, 0x4f, 0xf9,
    0x3f, 0xf9, 0x10, 0x29, 0xff, 0xf9, 0xa, 0xff,
    0xff, 0xff, 0x88, 0xf9, 0x0, 0x7d, 0xfe, 0xa2,
    0x7, 0xf9,

    /* U+0062 "b" */
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xb0, 0x3b, 0xff, 0xd7, 0x0, 0x9, 0xfb, 0x9f,
    0xff, 0xff, 0xfc, 0x0, 0x9f, 0xff, 0xb4, 0x13,
    0x9f, 0xf9, 0x9, 0xff, 0x60, 0x0, 0x0, 0xaf,
    0xf1, 0x9f, 0xc0, 0x0, 0x0, 0x2, 0xff, 0x69,
    0xfc, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0xcf, 0xb9, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xfb, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0xdf, 0xa9, 0xfc, 0x0, 0x0, 0x0, 0xf, 0xf8,
    0x9f, 0xc0, 0x0, 0x0, 0x5, 0xff, 0x39, 0xfe,
    0x10, 0x0, 0x1, 0xdf, 0xd0, 0x9f, 0xfe, 0x72,
    0x14, 0xdf, 0xf3, 0x9, 0xfa, 0xdf, 0xff, 0xff,
    0xf5, 0x0, 0x9f, 0x50, 0x7d, 0xfe, 0xa2, 0x0,
    0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x7c, 0xff, 0xd7, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xd1, 0x2, 0xef, 0xf7, 0x21,
    0x5d, 0xa0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfe, 0x20, 0x0, 0x0, 0x30,
    0x3, 0xff, 0xe6, 0x11, 0x4b, 0xf2, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x1, 0x8d, 0xff,
    0xc7, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb, 0x0,
    0x1, 0x9e, 0xfe, 0x91, 0x9f, 0xb0, 0x4, 0xff,
    0xff, 0xff, 0xec, 0xfb, 0x2, 0xff, 0xe6, 0x12,
    0x6d, 0xff, 0xb0, 0xbf, 0xf2, 0x0, 0x0, 0xc,
    0xfb, 0x2f, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xb6,
    0xff, 0x10, 0x0, 0x0, 0xa, 0xfb, 0x9f, 0xe0,
    0x0, 0x0, 0x0, 0xaf, 0xb9, 0xfd, 0x0, 0x0,
    0x0, 0xa, 0xfb, 0x9f, 0xe0, 0x0, 0x0, 0x0,
    0xaf, 0xb7, 0xff, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0xef,
    0xd0, 0x0, 0x0, 0x3f, 0xfb, 0x6, 0xff, 0xc4,
    0x12, 0x8f, 0xff, 0xb0, 0x9, 0xff, 0xff, 0xff,
    0xc7, 0xfb, 0x0, 0x5, 0xbf, 0xfd, 0x60, 0x4f,
    0xb0,

    /* U+0065 "e" */
    0x0, 0x1, 0x8d, 0xff, 0xb4, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x2, 0xff, 0xb3,
    0x0, 0x6f, 0xf5, 0x0, 0xbf, 0xb0, 0x0, 0x0,
    0x6f, 0xc0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0xff,
    0x16, 0xfc, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x9f,
    0xec, 0xcc, 0xcc, 0xcc, 0xff, 0x4a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xe5, 0x0, 0x15, 0xd3, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x1, 0x7d, 0xff, 0xea,
    0x30, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x4c, 0xfe, 0xb1, 0x0, 0x4, 0xff,
    0xff, 0xf0, 0x0, 0xd, 0xfd, 0x20, 0x20, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x20,
    0x2e, 0xff, 0xff, 0xee, 0x20, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x6, 0xcf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xbf, 0xfe, 0xdf, 0xff, 0xee, 0xe0, 0x9, 0xff,
    0x40, 0x2, 0xcf, 0x90, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x2f, 0xf2, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x1f, 0xf3, 0x0, 0x0, 0xe,
    0xf3, 0x0, 0xd, 0xf9, 0x0, 0x0, 0x4f, 0xf0,
    0x0, 0x3, 0xff, 0x81, 0x5, 0xef, 0x80, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x3,
    0xfb, 0x59, 0xa8, 0x20, 0x0, 0x0, 0xb, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xd8, 0x0, 0x1, 0xdf, 0xef, 0xff, 0xff, 0xff,
    0xc0, 0x1e, 0xe1, 0x0, 0x0, 0x2, 0x9f, 0xf3,
    0x8f, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x7f, 0xf5,
    0x0, 0x0, 0x6, 0xff, 0x70, 0xb, 0xff, 0xec,
    0xbc, 0xff, 0xf6, 0x0, 0x0, 0x5a, 0xef, 0xfe,
    0xc7, 0x10, 0x0,

    /* U+0068 "h" */
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x2a, 0xef,
    0xe8, 0x0, 0x9f, 0xb5, 0xff, 0xff, 0xff, 0xa0,
    0x9f, 0xef, 0xd5, 0x24, 0xdf, 0xf2, 0x9f, 0xfa,
    0x0, 0x0, 0x2f, 0xf7, 0x9f, 0xd0, 0x0, 0x0,
    0xd, 0xf9, 0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,
    0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0,
    0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,
    0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0,
    0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,
    0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,

    /* U+0069 "i" */
    0x8f, 0xb0, 0xff, 0xf2, 0xbf, 0xd0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x9f, 0xc0,
    0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0,
    0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0,
    0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0, 0x9f, 0xc0,
    0x9f, 0xc0,

    /* U+006A "j" */
    0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0, 0xff, 0xf2,
    0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0xbf, 0xa0,
    0x1, 0x3, 0xff, 0x70, 0xd, 0xff, 0xfe, 0x10,
    0xd, 0xff, 0xb2, 0x0,

    /* U+006B "k" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xb0, 0x0, 0x0, 0x9f, 0xf2, 0x9, 0xfb, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x9f, 0xb0, 0x0, 0x2f,
    0xf8, 0x0, 0x9, 0xfb, 0x0, 0xc, 0xfb, 0x0,
    0x0, 0x9f, 0xb0, 0x9, 0xfe, 0x10, 0x0, 0x9,
    0xfb, 0x5, 0xff, 0x30, 0x0, 0x0, 0x9f, 0xb2,
    0xff, 0xf1, 0x0, 0x0, 0x9, 0xfc, 0xdf, 0xff,
    0xa0, 0x0, 0x0, 0x9f, 0xff, 0xc3, 0xff, 0x30,
    0x0, 0x9, 0xff, 0xe1, 0x9, 0xfc, 0x0, 0x0,
    0x9f, 0xf3, 0x0, 0x1f, 0xf6, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x7f, 0xe1, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0xef, 0x90, 0x9, 0xfb, 0x0, 0x0, 0x5,
    0xff, 0x30, 0x9f, 0xb0, 0x0, 0x0, 0xc, 0xfc,
    0x0,

    /* U+006C "l" */
    0x9f, 0xc0, 0x9, 0xfc, 0x0, 0x9f, 0xc0, 0x9,
    0xfc, 0x0, 0x9f, 0xc0, 0x9, 0xfc, 0x0, 0x9f,
    0xc0, 0x9, 0xfc, 0x0, 0x9f, 0xc0, 0x9, 0xfc,
    0x0, 0x9f, 0xc0, 0x9, 0xfc, 0x0, 0x9f, 0xc0,
    0x9, 0xfc, 0x0, 0x9f, 0xc0, 0x9, 0xfc, 0x0,
    0x9f, 0xc0, 0x9, 0xfc, 0x0, 0x9f, 0xe1, 0x5,
    0xff, 0xf0, 0xa, 0xff, 0x20,

    /* U+006D "m" */
    0x9f, 0x60, 0x2b, 0xff, 0xc4, 0x0, 0x6, 0xdf,
    0xe9, 0x10, 0x9f, 0x75, 0xff, 0xff, 0xff, 0x40,
    0xbf, 0xff, 0xff, 0xc0, 0x9f, 0xdf, 0xc5, 0x26,
    0xff, 0xdc, 0xf8, 0x33, 0xbf, 0xf5, 0x9f, 0xfa,
    0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0xe, 0xfa,
    0x9f, 0xd0, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0xa, 0xfc, 0x9f, 0xc0, 0x0, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0x8, 0xfd, 0x9f, 0xc0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0x8, 0xfd, 0x9f, 0xc0,
    0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x8, 0xfd,
    0x9f, 0xc0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0x8, 0xfd, 0x9f, 0xc0, 0x0, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0x8, 0xfd, 0x9f, 0xc0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0x8, 0xfd, 0x9f, 0xc0,
    0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x8, 0xfd,
    0x9f, 0xc0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0x8, 0xfd, 0x9f, 0xc0, 0x0, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0x8, 0xfd, 0x9f, 0xc0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0x8, 0xfd,

    /* U+006E "n" */
    0x9f, 0x60, 0x2a, 0xef, 0xe8, 0x0, 0x9f, 0x75,
    0xff, 0xff, 0xff, 0xa0, 0x9f, 0xdf, 0xd5, 0x24,
    0xdf, 0xf2, 0x9f, 0xfa, 0x0, 0x0, 0x2f, 0xf7,
    0x9f, 0xd0, 0x0, 0x0, 0xd, 0xf9, 0x9f, 0xc0,
    0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,
    0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0,
    0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa,
    0x9f, 0xc0, 0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0,
    0x0, 0x0, 0xb, 0xfa, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfa,

    /* U+006F "o" */
    0x0, 0x1, 0x8d, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x2, 0xff,
    0xe5, 0x12, 0x7f, 0xfd, 0x0, 0xc, 0xfe, 0x10,
    0x0, 0x3, 0xff, 0x70, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x9f, 0xe0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x4f, 0xf3, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x1f,
    0xf5, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x9f, 0xd0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x3f, 0xf6,
    0x0, 0x0, 0x0, 0x9f, 0xe0, 0xc, 0xfe, 0x10,
    0x0, 0x3, 0xff, 0x80, 0x2, 0xff, 0xd5, 0x11,
    0x6f, 0xfd, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0x8d, 0xff, 0xc6, 0x0,
    0x0,

    /* U+0070 "p" */
    0x9f, 0x60, 0x3b, 0xff, 0xd7, 0x0, 0x9, 0xf8,
    0x9f, 0xff, 0xff, 0xfc, 0x0, 0x9f, 0xff, 0xb4,
    0x13, 0x9f, 0xfa, 0x9, 0xff, 0x60, 0x0, 0x0,
    0xaf, 0xf1, 0x9f, 0xc0, 0x0, 0x0, 0x2, 0xff,
    0x69, 0xfc, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x9f,
    0xc0, 0x0, 0x0, 0x0, 0xcf, 0xb9, 0xfc, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0xdf, 0xa9, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf8, 0x9f, 0xc0, 0x0, 0x0, 0x5, 0xff, 0x39,
    0xfe, 0x10, 0x0, 0x1, 0xdf, 0xd0, 0x9f, 0xfe,
    0x72, 0x14, 0xdf, 0xf3, 0x9, 0xfd, 0xdf, 0xff,
    0xff, 0xf5, 0x0, 0x9f, 0xb0, 0x7d, 0xfe, 0xa2,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0071 "q" */
    0x0, 0x1, 0x9e, 0xfe, 0xa2, 0x3f, 0xb0, 0x4,
    0xff, 0xff, 0xff, 0xfa, 0xfb, 0x2, 0xff, 0xe6,
    0x12, 0x6d, 0xff, 0xb0, 0xbf, 0xf2, 0x0, 0x0,
    0xc, 0xfb, 0x2f, 0xf7, 0x0, 0x0, 0x0, 0xaf,
    0xb6, 0xff, 0x10, 0x0, 0x0, 0xa, 0xfb, 0x9f,
    0xe0, 0x0, 0x0, 0x0, 0xaf, 0xb9, 0xfd, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x9f, 0xe0, 0x0, 0x0,
    0x0, 0xaf, 0xb7, 0xff, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0xaf, 0xb0,
    0xef, 0xd0, 0x0, 0x0, 0x3f, 0xfb, 0x6, 0xff,
    0xc4, 0x12, 0x8f, 0xff, 0xb0, 0x9, 0xff, 0xff,
    0xff, 0xca, 0xfb, 0x0, 0x5, 0xbf, 0xfd, 0x60,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb0,

    /* U+0072 "r" */
    0x9f, 0x60, 0x3c, 0xff, 0x9, 0xf7, 0x5f, 0xff,
    0xd0, 0x9f, 0xaf, 0xe6, 0x34, 0x9, 0xff, 0xd1,
    0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x9, 0xfd,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f,
    0xc0, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x4, 0xbe, 0xfe, 0xa3, 0x0, 0x0, 0x6f,
    0xff, 0xef, 0xff, 0x80, 0x1, 0xff, 0x90, 0x1,
    0x7e, 0x30, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xd5, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x2, 0x30, 0x0, 0x0, 0x2f, 0xf2,
    0xc, 0xf9, 0x20, 0x2, 0xcf, 0xc0, 0x7, 0xff,
    0xff, 0xef, 0xff, 0x30, 0x0, 0x18, 0xdf, 0xfd,
    0x91, 0x0,

    /* U+0074 "t" */
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf0, 0x4e, 0xff, 0xfe, 0xee, 0xe0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0x20, 0x30, 0x0, 0x6, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x6d, 0xff, 0xc3,

    /* U+0075 "u" */
    0xdf, 0x90, 0x0, 0x0, 0xf, 0xf6, 0xdf, 0x90,
    0x0, 0x0, 0xf, 0xf6, 0xdf, 0x90, 0x0, 0x0,
    0xf, 0xf6, 0xdf, 0x90, 0x0, 0x0, 0xf, 0xf6,
    0xdf, 0x90, 0x0, 0x0, 0xf, 0xf6, 0xdf, 0x90,
    0x0, 0x0, 0xf, 0xf6, 0xdf, 0x90, 0x0, 0x0,
    0xf, 0xf6, 0xdf, 0x90, 0x0, 0x0, 0xf, 0xf6,
    0xdf, 0x90, 0x0, 0x0, 0xf, 0xf6, 0xcf, 0x90,
    0x0, 0x0, 0xf, 0xf6, 0xcf, 0xb0, 0x0, 0x0,
    0x2f, 0xf6, 0x9f, 0xf0, 0x0, 0x1, 0xdf, 0xf6,
    0x4f, 0xfb, 0x32, 0x6e, 0xed, 0xf6, 0xc, 0xff,
    0xff, 0xfe, 0x2a, 0xf6, 0x0, 0x9e, 0xfe, 0x91,
    0x9, 0xf6,

    /* U+0076 "v" */
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x2f,
    0xf4, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0xef, 0x50, 0x7, 0xfe, 0x0,
    0x0, 0x3, 0xff, 0x10, 0x2, 0xff, 0x30, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0xcf, 0x80, 0x0, 0xd,
    0xf6, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0xc, 0xf7, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x6, 0xfb, 0x1, 0xff, 0x10, 0x0, 0x0, 0x1,
    0xff, 0x15, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x5a, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xaf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x60, 0x0,
    0x0,

    /* U+0077 "w" */
    0x2f, 0xf4, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0,
    0x3, 0xff, 0x0, 0xef, 0x70, 0x0, 0x1, 0xff,
    0xf0, 0x0, 0x0, 0x7f, 0xc0, 0xa, 0xfb, 0x0,
    0x0, 0x5f, 0xef, 0x40, 0x0, 0xb, 0xf8, 0x0,
    0x6f, 0xf0, 0x0, 0x9, 0xf7, 0xf8, 0x0, 0x0,
    0xef, 0x40, 0x2, 0xff, 0x30, 0x0, 0xdf, 0x1f,
    0xc0, 0x0, 0x2f, 0xf0, 0x0, 0xd, 0xf7, 0x0,
    0x1f, 0xb0, 0xef, 0x0, 0x6, 0xfc, 0x0, 0x0,
    0x9f, 0xb0, 0x5, 0xf7, 0xa, 0xf4, 0x0, 0xaf,
    0x80, 0x0, 0x5, 0xfe, 0x0, 0x9f, 0x40, 0x6f,
    0x90, 0xe, 0xf4, 0x0, 0x0, 0x1f, 0xf2, 0xd,
    0xf0, 0x2, 0xfd, 0x2, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0x61, 0xfc, 0x0, 0xe, 0xf1, 0x5f, 0xc0,
    0x0, 0x0, 0x8, 0xf9, 0x4f, 0x80, 0x0, 0xaf,
    0x48, 0xf8, 0x0, 0x0, 0x0, 0x4f, 0xc8, 0xf4,
    0x0, 0x6, 0xf8, 0xcf, 0x40, 0x0, 0x0, 0x0,
    0xff, 0xcf, 0x0, 0x0, 0x2f, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0xef,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0,
    0x0, 0xa, 0xff, 0x70, 0x0, 0x0,

    /* U+0078 "x" */
    0xe, 0xf9, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x5f,
    0xf3, 0x0, 0x1, 0xff, 0x50, 0x0, 0xcf, 0xc0,
    0x0, 0x9f, 0xb0, 0x0, 0x2, 0xff, 0x50, 0x2f,
    0xf2, 0x0, 0x0, 0x9, 0xfd, 0xa, 0xf9, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x5f, 0xd4, 0xff, 0x30,
    0x0, 0x0, 0xe, 0xf5, 0xa, 0xfc, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x2f, 0xf6, 0x0, 0x2, 0xff,
    0x30, 0x0, 0x8f, 0xe1, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xef, 0xa0, 0x5f, 0xf2, 0x0, 0x0, 0x5,
    0xff, 0x40,

    /* U+0079 "y" */
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x1f,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0xb, 0xfb,
    0x0, 0x0, 0x0, 0xef, 0x50, 0x5, 0xff, 0x10,
    0x0, 0x3, 0xff, 0x10, 0x0, 0xef, 0x60, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0xd,
    0xf6, 0x0, 0x0, 0x2f, 0xf1, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x6, 0xfc, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xff, 0x20, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x9f, 0x74, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xc9, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x1, 0x3,
    0xdf, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0xd, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xb0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x1f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+007B "{" */
    0x0, 0x0, 0x7d, 0xfe, 0x0, 0x5, 0xfe, 0x86,
    0x0, 0xa, 0xf6, 0x0, 0x0, 0xc, 0xf3, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0xa, 0xf4, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0xf, 0xfe, 0x30, 0x0, 0x9, 0xef, 0x90, 0x0,
    0x0, 0x1e, 0xf1, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x9, 0xf4, 0x0, 0x0, 0xa, 0xf3, 0x0,
    0x0, 0xb, 0xf3, 0x0, 0x0, 0xc, 0xf2, 0x0,
    0x0, 0xc, 0xf3, 0x0, 0x0, 0xa, 0xf5, 0x0,
    0x0, 0x7, 0xfc, 0x31, 0x0, 0x0, 0xcf, 0xfe,
    0x0, 0x0, 0x2, 0x44,

    /* U+007C "|" */
    0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55,
    0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f,
    0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5,
    0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55,
    0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f,
    0x55, 0xf5, 0x5f, 0x55, 0xf5,

    /* U+007D "}" */
    0x1f, 0xfd, 0x50, 0x0, 0x7, 0x9f, 0xf2, 0x0,
    0x0, 0x9, 0xf7, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x7, 0xf7, 0x0, 0x0, 0x7, 0xf6, 0x0,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x7, 0xf7, 0x0, 0x0, 0x3, 0xfe, 0x40,
    0x0, 0x0, 0x6f, 0xfc, 0x0, 0x0, 0xbf, 0xd7,
    0x0, 0x4, 0xfb, 0x0, 0x0, 0x7, 0xf6, 0x0,
    0x0, 0x8, 0xf5, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x6, 0xf8, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x1, 0x4e, 0xf4, 0x0, 0x1f, 0xff, 0x90, 0x0,
    0x4, 0x41, 0x0, 0x0,

    /* U+007E "~" */
    0x3, 0xcf, 0xe8, 0x0, 0x0, 0x1, 0x5, 0xff,
    0xef, 0xfe, 0x40, 0x9, 0xd2, 0x9e, 0x30, 0x1a,
    0xff, 0xef, 0xfb, 0x0, 0x10, 0x0, 0x4, 0xcf,
    0xe8, 0x0,

    /* U+4E00 "一" */
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe,

    /* U+4E0B "下" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x9e, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x8f, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x1,
    0xaf, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x4, 0xef, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xba, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xbf, 0xed, 0xdd, 0xdd, 0xde, 0xff,
    0xdd, 0xdd, 0xdd, 0xdf, 0xf1, 0xbf, 0x30, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0xbf, 0x30, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0xbf, 0x30, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xf1, 0xbf, 0x30,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0xbf, 0x30, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0xe, 0xf1, 0xbf, 0x30, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xf1, 0xbf,
    0x52, 0x22, 0x22, 0x24, 0xff, 0x22, 0x22, 0x22,
    0x2f, 0xf1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xbf, 0xdc, 0xcc,
    0xcc, 0xcc, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xf1,
    0xbf, 0x30, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x46, 0x10, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x9f, 0xa8, 0x88, 0x88, 0x88, 0x20,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x2, 0x66, 0x66, 0x66,
    0x66, 0x6f, 0xf6, 0x66, 0x66, 0x66, 0x66, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x1e, 0xee, 0xee, 0xee, 0xef, 0xfe, 0xee,
    0xee, 0xee, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xe9,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa,

    /* U+4F4D "位" */
    0x0, 0x0, 0x0, 0x46, 0x10, 0x0, 0x0, 0x8d,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x4f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x50, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0x8, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf5, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x2d,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,
    0x0, 0x16, 0x30, 0x0, 0x0, 0x8, 0x92, 0x0,
    0x0, 0xbf, 0xef, 0xd0, 0x0, 0x5, 0xf8, 0x0,
    0x0, 0x0, 0xdf, 0x30, 0x0, 0x8f, 0xf4, 0xfd,
    0x0, 0x0, 0x2f, 0xc0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x4, 0xf5, 0x1f, 0xd0, 0x0, 0x0, 0xef,
    0x0, 0x0, 0x2, 0xfd, 0x0, 0x0, 0x4, 0x1,
    0xfd, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x4f,
    0xa0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x0,
    0xaf, 0x40, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0xd, 0xf0, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x1, 0xfd, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xdf,
    0x10, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x9, 0x91, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xd0, 0x4e, 0xee, 0xee, 0xee, 0xee, 0xff,
    0xee, 0xee, 0xe0, 0x0, 0x1, 0xfd, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+5019 "候" */
    0x0, 0x0, 0x9, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x0, 0x0, 0x6a, 0xaa, 0xaa, 0xaa, 0xab, 0x70,
    0x0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x80, 0x0, 0x0, 0x4, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0,
    0xcf, 0x40, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x3f, 0xd0, 0x1f, 0xa3,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xfd, 0xdd, 0x10,
    0xc, 0xfc, 0x1, 0xfa, 0x3e, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xe2, 0x5, 0xff, 0xc0, 0x1f,
    0xa0, 0x0, 0x4d, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfc, 0x1, 0xfa, 0x0, 0xa, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xdf, 0xc0,
    0x1f, 0xa0, 0x1, 0xff, 0x77, 0x77, 0x77, 0x77,
    0x72, 0xa, 0xf3, 0xfc, 0x1, 0xfa, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x25, 0x1f,
    0xc0, 0x1f, 0xa0, 0x3f, 0xe3, 0x24, 0xfe, 0x22,
    0x22, 0x20, 0x0, 0x1, 0xfc, 0x1, 0xfa, 0x1e,
    0xf6, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x1f, 0xa0, 0x8a, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x1, 0xfa,
    0x16, 0x66, 0x66, 0x7f, 0xe6, 0x66, 0x66, 0x60,
    0x0, 0x1f, 0xc0, 0x1f, 0xa3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x1, 0xfc, 0x1,
    0xfa, 0x4, 0x44, 0x44, 0xaf, 0xa4, 0x54, 0x44,
    0x40, 0x0, 0x1f, 0xc0, 0x1f, 0xa0, 0x0, 0x0,
    0xe, 0xf3, 0xcd, 0x20, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x6, 0xfe,
    0x30, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0,
    0x1b, 0xfd, 0x10, 0x5, 0xff, 0x40, 0x0, 0x1,
    0xfc, 0x0, 0x0, 0x0, 0x7e, 0xfc, 0x10, 0x0,
    0x4, 0xff, 0x40, 0x0, 0x1f, 0xc0, 0x0, 0x9,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x1, 0xfc, 0x0, 0x0, 0x3d, 0x81, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+505C "停" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xb4, 0x0, 0x0, 0x4, 0xd9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0x0,
    0xe, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xe5, 0x99, 0x99, 0x99, 0xdf, 0xd9, 0x99,
    0x99, 0x99, 0x0, 0x0, 0x8, 0xf8, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0x50, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0xb, 0xff, 0x0,
    0x0, 0xaf, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x6, 0xff, 0xf0, 0x0, 0xa, 0xf1, 0x0,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x2, 0xff, 0xff,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xef, 0xad, 0xf0, 0x0, 0x4, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x75, 0x0, 0xa, 0xd0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x22, 0xd, 0xf0, 0xa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x0,
    0x0, 0xdf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xd, 0xf0, 0xf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0xdf, 0x0, 0xfc, 0x47, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x1d, 0xf0, 0x0, 0xd, 0xf0,
    0x8, 0x7a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x78, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x12, 0x22,
    0x2c, 0xf4, 0x22, 0x22, 0x0, 0x0, 0x0, 0xd,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf0, 0x0, 0x0, 0x4a, 0xaa, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0,
    0x0, 0x1, 0xff, 0xfe, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5074 "側" */
    0x0, 0x0, 0x1, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x57, 0x0, 0x0, 0x0, 0x6f,
    0x82, 0x44, 0x44, 0x44, 0x40, 0x0, 0x0, 0xb,
    0xf1, 0x0, 0x0, 0xc, 0xf2, 0x9f, 0xff, 0xff,
    0xff, 0x0, 0x22, 0x0, 0xbf, 0x10, 0x0, 0x2,
    0xfc, 0x9, 0xf6, 0x55, 0x5d, 0xf0, 0xf, 0xd0,
    0xb, 0xf1, 0x0, 0x0, 0x9f, 0x60, 0x9f, 0x10,
    0x0, 0xbf, 0x0, 0xfd, 0x0, 0xbf, 0x10, 0x0,
    0x1f, 0xf0, 0x9, 0xf1, 0x0, 0xb, 0xf0, 0xf,
    0xd0, 0xb, 0xf1, 0x0, 0x9, 0xfd, 0x0, 0x9f,
    0x77, 0x77, 0xdf, 0x0, 0xfd, 0x0, 0xbf, 0x10,
    0x2, 0xff, 0xd0, 0x9, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xd0, 0xb, 0xf1, 0x0, 0xbf, 0xfd, 0x0,
    0x9f, 0x21, 0x11, 0xbf, 0x0, 0xfd, 0x0, 0xbf,
    0x10, 0x6f, 0xef, 0xd0, 0x9, 0xf1, 0x0, 0xb,
    0xf0, 0xf, 0xd0, 0xb, 0xf1, 0x3f, 0xf4, 0xfd,
    0x0, 0x9f, 0x10, 0x0, 0xbf, 0x0, 0xfd, 0x0,
    0xbf, 0x13, 0xf9, 0xf, 0xd0, 0x9, 0xfa, 0xaa,
    0xae, 0xf0, 0xf, 0xd0, 0xb, 0xf1, 0x7, 0x0,
    0xfd, 0x0, 0x9f, 0xee, 0xee, 0xff, 0x0, 0xfd,
    0x0, 0xbf, 0x10, 0x0, 0xf, 0xd0, 0x9, 0xf1,
    0x0, 0xb, 0xf0, 0xf, 0xd0, 0xb, 0xf1, 0x0,
    0x0, 0xfd, 0x0, 0x9f, 0x10, 0x0, 0xbf, 0x0,
    0xfd, 0x0, 0xbf, 0x10, 0x0, 0xf, 0xd0, 0x9,
    0xf1, 0x0, 0xb, 0xf0, 0xf, 0xd0, 0xb, 0xf1,
    0x0, 0x0, 0xfd, 0x0, 0x9f, 0xdd, 0xdd, 0xff,
    0x0, 0xfd, 0x0, 0xbf, 0x10, 0x0, 0xf, 0xd0,
    0x7, 0xcc, 0xcc, 0xcc, 0xc0, 0xf, 0xd0, 0xb,
    0xf1, 0x0, 0x0, 0xfd, 0x0, 0x2, 0x70, 0x3,
    0x70, 0x0, 0x22, 0x0, 0xbf, 0x10, 0x0, 0xf,
    0xd0, 0x0, 0xbf, 0x60, 0xaf, 0x40, 0x0, 0x0,
    0xb, 0xf1, 0x0, 0x0, 0xfd, 0x0, 0x5f, 0xc0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0xbf, 0x10, 0x0,
    0xf, 0xd0, 0x2f, 0xf2, 0x0, 0x8, 0xf6, 0x0,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0xfd, 0xe, 0xf4,
    0x0, 0x0, 0x1f, 0x70, 0xa, 0xdd, 0xff, 0x0,
    0x0, 0xf, 0xc0, 0x46, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x7f, 0xfd, 0x50,

    /* U+5148 "先" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xed, 0xdd, 0xdf, 0xfd, 0xdd,
    0xdd, 0xdd, 0xd7, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0xb, 0xf6, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x50, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x79, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5d, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdf, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd2,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xb,
    0xf5, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x80, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0xb3, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x8f, 0xf2, 0x0, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0x1, 0xfa, 0x0, 0x1b, 0xff, 0x50,
    0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x5, 0xf8,
    0x29, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfd, 0xdd, 0xdf, 0xf4, 0x4f, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x80,
    0x5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+51FA "出" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x99, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x44,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x1, 0xff, 0x10,
    0x0, 0x0, 0xef, 0x30, 0x0, 0xff, 0x0, 0x0,
    0x1, 0xff, 0x10, 0x0, 0x0, 0xef, 0x30, 0x0,
    0xff, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0xef, 0x30, 0x0, 0xff, 0x0, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x0, 0xef, 0x30, 0x0, 0xff, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0xef, 0x30,
    0x0, 0xff, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x0, 0xef, 0x30, 0x0, 0xff, 0x33, 0x33, 0x34,
    0xff, 0x43, 0x33, 0x33, 0xff, 0x30, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xbb,
    0xbb, 0xbb, 0xbb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0x90, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0x9, 0xa2, 0x3f, 0xe0, 0x0, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x0, 0xe, 0xf3, 0x3f, 0xe0, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0xe, 0xf3,
    0x3f, 0xe0, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x0, 0xe, 0xf3, 0x3f, 0xe0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0xe, 0xf3, 0x3f, 0xe0,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0xe,
    0xf3, 0x3f, 0xe0, 0x0, 0x0, 0x1, 0xff, 0x10,
    0x0, 0x0, 0xe, 0xf3, 0x3f, 0xf3, 0x33, 0x33,
    0x34, 0xff, 0x43, 0x33, 0x33, 0x3e, 0xf3, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf3,

    /* U+52A0 "加" */
    0x0, 0x0, 0x7, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x9, 0x99, 0x99, 0x99, 0x98,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfd, 0x9, 0xee, 0xef, 0xfe,
    0xee, 0xef, 0xe0, 0xf, 0xf4, 0x44, 0x45, 0xfd,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0xf, 0xd0,
    0x0, 0xf, 0xe0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0xf, 0xe0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0xf, 0xe0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x2f, 0xb0, 0x0, 0xf, 0xd0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0xf, 0xd0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0xf, 0xd0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0x1f, 0xc0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0x1f, 0xc0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0xdf, 0x20,
    0x0, 0x2f, 0xb0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x3f, 0xb0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x5, 0xfa, 0x0,
    0x0, 0x4f, 0xa0, 0xf, 0xe0, 0x0, 0x1, 0xfd,
    0x0, 0xb, 0xf6, 0x0, 0x0, 0x5f, 0x90, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x7f, 0x80, 0xf, 0xe0, 0x0, 0x2, 0xfd,
    0x0, 0x9f, 0xa0, 0x0, 0x0, 0x9f, 0x60, 0xf,
    0xff, 0xff, 0xff, 0xfd, 0x3, 0xff, 0x30, 0x0,
    0x0, 0xef, 0x30, 0xf, 0xfd, 0xdd, 0xdd, 0xfd,
    0x1e, 0xfa, 0x0, 0x7f, 0xff, 0xfe, 0x0, 0xf,
    0xe0, 0x0, 0x1, 0xfd, 0x8, 0xe1, 0x0, 0x2e,
    0xff, 0xc3, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x87,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+53D6 "取" */
    0x3, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xdf, 0xfd, 0xdd, 0xdd, 0xff,
    0xdb, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0xe, 0xf0, 0x0, 0x0, 0xef, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xe, 0xf0, 0x0,
    0x0, 0xef, 0xb, 0xde, 0xdd, 0xdd, 0xdd, 0xff,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0xef, 0x3,
    0xea, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0x1, 0xfe, 0x0, 0x0,
    0x5, 0xf9, 0x0, 0x0, 0xe, 0xfd, 0xdd, 0xdd,
    0xff, 0x0, 0xdf, 0x20, 0x0, 0x8, 0xf6, 0x0,
    0x0, 0xe, 0xf0, 0x0, 0x0, 0xef, 0x0, 0x8f,
    0x60, 0x0, 0xc, 0xf2, 0x0, 0x0, 0xe, 0xf0,
    0x0, 0x0, 0xef, 0x0, 0x4f, 0xb0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0xef,
    0x0, 0xf, 0xf1, 0x0, 0x7f, 0x90, 0x0, 0x0,
    0xe, 0xfb, 0xbb, 0xbb, 0xff, 0x0, 0xa, 0xf6,
    0x0, 0xdf, 0x40, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x4, 0xfd, 0x4, 0xfd, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0xef, 0x0,
    0x0, 0xdf, 0x4c, 0xf6, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0x0, 0x0, 0xef, 0x0, 0x0, 0x6f, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x0,
    0xef, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x35, 0x8a, 0xff, 0x0, 0x0,
    0xa, 0xff, 0x20, 0x0, 0x0, 0x28, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x7f, 0xff, 0xc0,
    0x0, 0x0, 0x4f, 0xff, 0xfd, 0xb8, 0x53, 0xef,
    0x0, 0x6, 0xff, 0x6e, 0xfa, 0x0, 0x0, 0x17,
    0x52, 0x0, 0x0, 0x0, 0xef, 0x0, 0x8f, 0xf6,
    0x3, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x2c, 0xff, 0x60, 0x0, 0x3e, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x8f,
    0xe3, 0x0, 0x0, 0x2, 0xdf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x20,

    /* U+53E3 "口" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xdf, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xfa, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa,
    0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa,
    0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa,
    0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa,
    0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfa,
    0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x21,

    /* U+53F3 "右" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x1e, 0xee, 0xee, 0xef, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xe9, 0x0, 0x0, 0x0, 0x7,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xf8, 0x0,
    0x0, 0xa, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x9f, 0xf3, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0xa, 0xff, 0x40, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0xbf, 0xf4, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x2d, 0x30, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xde, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+55AE "單" */
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xd0, 0xf, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0xbf, 0x66, 0x66,
    0x6f, 0xe0, 0xf, 0xd6, 0x66, 0x68, 0xfb, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0xf, 0xe0, 0xf, 0xb0,
    0x0, 0x3, 0xfb, 0x0, 0x0, 0xbf, 0x21, 0x11,
    0x1f, 0xe0, 0xf, 0xc1, 0x11, 0x14, 0xfb, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xe0, 0xf, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x24, 0x44, 0x44,
    0x44, 0x30, 0x4, 0x44, 0x44, 0x44, 0x42, 0x0,
    0x0, 0x9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x90, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf8, 0x88, 0x88, 0xaf, 0xe8, 0x88,
    0x88, 0x8f, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0xa, 0xaa, 0xaa,
    0xaa, 0xbf, 0xfa, 0xaa, 0xaa, 0xaa, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x4f, 0xd1, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x4a, 0xaa, 0xaa, 0xaa,
    0xaa, 0xbf, 0xfa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+5674 "噴" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0xe, 0xee, 0xee,
    0xef, 0xfe, 0xee, 0xee, 0xc0, 0x0, 0xff, 0xff,
    0xff, 0xa0, 0x77, 0x77, 0x78, 0xfe, 0x77, 0x77,
    0x76, 0x0, 0xf, 0xeb, 0xbb, 0xfa, 0x0, 0x1c,
    0x80, 0xf, 0xc0, 0x1c, 0x80, 0x0, 0x0, 0xf9,
    0x0, 0x1f, 0xa0, 0x1, 0xfb, 0x0, 0x87, 0x1,
    0xfa, 0x0, 0x0, 0xf, 0x90, 0x1, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0xf9, 0x0, 0x1f, 0xb7, 0x78, 0xfd, 0x77, 0x77,
    0x78, 0xfc, 0x77, 0x70, 0xf, 0x90, 0x1, 0xfa,
    0x0, 0x1f, 0xb0, 0x0, 0x0, 0x1f, 0xa0, 0x0,
    0x0, 0xf9, 0x0, 0x1f, 0xa0, 0x35, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x51, 0x0, 0xf, 0x90, 0x1,
    0xfa, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xf9, 0x0, 0x1f, 0xa0, 0xbf, 0x10,
    0x0, 0x0, 0x0, 0x8, 0xf4, 0x0, 0xf, 0x90,
    0x1, 0xfa, 0xb, 0xf4, 0x33, 0x33, 0x33, 0x33,
    0xaf, 0x40, 0x0, 0xf9, 0x0, 0x1f, 0xa0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xf,
    0x90, 0x1, 0xfa, 0xb, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x40, 0x0, 0xff, 0xff, 0xff, 0xa0,
    0xbf, 0x54, 0x44, 0x44, 0x44, 0x4a, 0xf4, 0x0,
    0xf, 0xec, 0xcc, 0xc8, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xf9, 0x0, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x8, 0xf4,
    0x0, 0xf, 0x90, 0x0, 0x0, 0xb, 0xf5, 0x44,
    0x44, 0x44, 0x44, 0xaf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd9, 0x0, 0x4, 0xea, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xfe, 0x40, 0x0, 0x19,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x29, 0xef,
    0xe7, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xcd, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5e, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x22, 0x22, 0x24, 0xff, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x10, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x2, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xfd,
    0xdd, 0xdd, 0xdd, 0xdd, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x90, 0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0x6, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x6f,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xa0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xe1, 0x0, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xb1, 0x0, 0x0, 0x7,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xe6, 0x0, 0x4e, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfc, 0x0,
    0xbe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0,

    /* U+5BA2 "客" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xef, 0xeb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xfe, 0x11, 0x11, 0x13, 0x63, 0x11,
    0x11, 0x11, 0x11, 0x11, 0xff, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0xfe, 0x0, 0x0, 0xbf,
    0xf7, 0x77, 0x77, 0x77, 0x83, 0x0, 0xff, 0x0,
    0x0, 0x32, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x23, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x9f, 0xf9, 0xef, 0x70,
    0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfd, 0x40, 0x2e, 0xfa, 0x10, 0x9f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x70, 0x0,
    0x2, 0xdf, 0xee, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6d, 0xff, 0xcb, 0xff, 0xf9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff,
    0xb4, 0x0, 0x3a, 0xff, 0xff, 0xb7, 0x41, 0x0,
    0x6, 0x9d, 0xff, 0xfe, 0x82, 0x0, 0x0, 0x0,
    0x17, 0xcf, 0xff, 0xff, 0xb0, 0x9, 0xff, 0xd8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x89,
    0xcf, 0x20, 0x1, 0x51, 0x0, 0xff, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xef, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x99, 0x99, 0x99, 0x99, 0x99, 0xef,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5C07 "將" */
    0x0, 0x0, 0x0, 0x9, 0x80, 0x0, 0x0, 0x0,
    0x28, 0x30, 0x0, 0x0, 0x0, 0x0, 0x88, 0x0,
    0xf, 0xe0, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x0, 0xf, 0xe0, 0x0,
    0x0, 0x2d, 0xfd, 0x77, 0x77, 0x75, 0x0, 0x0,
    0xde, 0x0, 0xf, 0xe0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xde, 0x0, 0xf,
    0xe0, 0x2, 0xbf, 0xff, 0x60, 0x0, 0x7, 0xf9,
    0x0, 0x0, 0xde, 0x0, 0xf, 0xe2, 0x9f, 0xfa,
    0x1b, 0xf7, 0x0, 0x3f, 0xe1, 0x0, 0x0, 0xde,
    0x0, 0xf, 0xe1, 0xcc, 0x30, 0x0, 0xbf, 0x62,
    0xef, 0x30, 0x0, 0x0, 0xde, 0x11, 0x1f, 0xe0,
    0x0, 0x24, 0x64, 0xc, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xe0, 0xcf, 0xff, 0xf9,
    0x9, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8b, 0x99,
    0x9f, 0xe0, 0x67, 0x42, 0x5, 0xdf, 0xc3, 0x64,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0x17, 0xdf, 0xe6, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xe0, 0x6c, 0xff, 0xe7, 0x0,
    0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xe0, 0x5f, 0xa5, 0x0, 0x0, 0x2, 0xfb, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xe0, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbc, 0xfe, 0xbb, 0xb1, 0xb, 0xef,
    0xcb, 0xbf, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x8f, 0x30, 0xf, 0xe0,
    0x0, 0x2, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x0, 0x9f, 0x20, 0xf, 0xe0, 0x0, 0xbe, 0x20,
    0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0xf, 0xe0, 0x0, 0x6f, 0xe1, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x0, 0xbf, 0x0, 0xf, 0xe0, 0x0,
    0x9, 0xfb, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0xdd, 0x0, 0xf, 0xe0, 0x0, 0x0, 0xcf, 0x60,
    0x2, 0xfb, 0x0, 0x0, 0x1, 0xfa, 0x0, 0xf,
    0xe0, 0x0, 0x0, 0x2f, 0x50, 0x2, 0xfb, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0xf, 0xe0, 0x0, 0x0,
    0x1, 0x0, 0x2, 0xfb, 0x0, 0x0, 0xb, 0xf1,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0xbc, 0xcd,
    0xfa, 0x0, 0x0, 0xa, 0x90, 0x0, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5C0F "小" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x30, 0x0, 0x0, 0xff, 0x20, 0x0, 0x5,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0xff, 0x20, 0x0, 0x7f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0xff, 0x20,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xc0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x5, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x0, 0xff, 0x20, 0x0,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x7, 0xfc, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0, 0x5f,
    0xf0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0xdf, 0x80, 0x0, 0x0,
    0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0xaf, 0xa0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0xa, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x9, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xb9,
    0x0, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xc0, 0x3f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x3e, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x2d,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x16, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x8e, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x9, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x9, 0xee,
    0xee, 0xff, 0xee, 0xee, 0xb0, 0x7f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0xa,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x5,
    0x10, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x22,
    0x0, 0xff, 0x20, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xf, 0xf4, 0x8b, 0xef, 0x90, 0xa, 0xf9, 0x0,
    0xc, 0xf0, 0x0, 0x14, 0x7a, 0xff, 0xff, 0xfe,
    0xa5, 0x0, 0x3f, 0xf3, 0x0, 0xee, 0x4, 0xef,
    0xff, 0xff, 0xc8, 0x51, 0x0, 0x0, 0x0, 0xaf,
    0xe3, 0x3f, 0xb0, 0x1f, 0xea, 0x73, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf5, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x9e, 0xf9, 0x0,

    /* U+5F85 "待" */
    0x0, 0x0, 0x0, 0xaa, 0x20, 0x0, 0x0, 0x3,
    0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xe1, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf5, 0x8, 0xaa, 0xaa, 0xaa, 0xbf, 0xea, 0xaa,
    0xaa, 0xa7, 0x0, 0x8f, 0xf6, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x8f,
    0xf5, 0x0, 0x1, 0x22, 0x22, 0x22, 0x5f, 0xc2,
    0x22, 0x22, 0x21, 0x1, 0xe4, 0x0, 0x1e, 0x90,
    0x0, 0x0, 0x4, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0, 0x4f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfc,
    0x2, 0x22, 0x22, 0x26, 0xfc, 0x22, 0x22, 0x22,
    0x0, 0x0, 0x5, 0xff, 0x20, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x4, 0xff,
    0xc0, 0x7, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa9, 0x0, 0x5, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x10, 0x0, 0x8, 0xff,
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0xcf, 0x71, 0xfc, 0xb, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xcc, 0xc8, 0x4,
    0x60, 0x1f, 0xc0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x2c, 0xb0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x1, 0xef,
    0x50, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0xa, 0xd3, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0,
    0x1, 0xfc, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x5, 0xcc, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5F9E "從" */
    0x0, 0x0, 0x0, 0x54, 0x0, 0x0, 0x18, 0x50,
    0x0, 0x1, 0x85, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfe, 0x10, 0x0, 0x4f, 0xa0, 0x0, 0x4, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xf3, 0x0, 0x0,
    0x9f, 0x60, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x2, 0xef, 0x50, 0x0, 0x0, 0xdf, 0x10, 0x0,
    0xb, 0xf3, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0x7, 0xff, 0x50, 0x2, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x6, 0xe3,
    0x0, 0x5f, 0xa0, 0x2f, 0xea, 0xfc, 0x11, 0xef,
    0xdf, 0x90, 0x0, 0x0, 0x10, 0x1, 0xef, 0x30,
    0xcf, 0x60, 0x9f, 0x6a, 0xf8, 0x1d, 0xf9, 0x0,
    0x0, 0x0, 0xb, 0xf8, 0x8, 0xfd, 0x0, 0x6,
    0x8f, 0xd0, 0x1, 0xdf, 0x80, 0x0, 0x0, 0x9f,
    0xd0, 0x7f, 0xf2, 0x0, 0x6, 0xff, 0x20, 0x0,
    0x2e, 0xf2, 0x0, 0x8, 0xff, 0xa0, 0x2d, 0x40,
    0x0, 0x0, 0xba, 0x0, 0x0, 0x3, 0x40, 0x0,
    0x8f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x10, 0x0, 0x0, 0x0, 0xa, 0xff, 0x6f, 0xa0,
    0x0, 0x7, 0x70, 0x0, 0xbf, 0x10, 0x0, 0x0,
    0x0, 0xc, 0xf3, 0x2f, 0xa0, 0x0, 0xe, 0xf0,
    0x0, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x2, 0x20,
    0x2f, 0xa0, 0x0, 0xf, 0xe0, 0x0, 0xbf, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xa0, 0x0,
    0x2f, 0xb0, 0x0, 0xbf, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x2f, 0xa0, 0x0, 0x5f, 0xb0, 0x0,
    0xbf, 0xaa, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x2f,
    0xa0, 0x0, 0x8f, 0xe0, 0x0, 0xbf, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xa0, 0x0, 0xdf,
    0xf6, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xa0, 0x2, 0xfd, 0xee, 0x10, 0xbf,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xa0,
    0xa, 0xf6, 0x6f, 0xc1, 0xbf, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xa0, 0x5f, 0xd0, 0xa,
    0xfe, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xa3, 0xff, 0x30, 0x0, 0x8f, 0xff, 0xed,
    0xcc, 0xcc, 0xc0, 0x0, 0x0, 0x2f, 0xa1, 0xb5,
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xff, 0xff, 0xb0,

    /* U+5FC3 "心" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30,
    0x0, 0x2, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x1, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xc2, 0x0,
    0x0, 0x5, 0xf8, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x8f, 0x60,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0xb, 0xf4, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0, 0xef,
    0x10, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xc0, 0x0, 0x1f, 0xe0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x5,
    0xfb, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x9f, 0x70, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0,
    0xe, 0xf3, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x3, 0xd5, 0x0, 0xff, 0x13, 0xff, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0xc,
    0xf5, 0x6f, 0xa0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x5, 0xf8, 0x0, 0x54, 0x0, 0x12, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfd, 0xdd, 0xdd, 0xdf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0,

    /* U+624B "手" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x7b, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x35, 0x79, 0xbe, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x8a, 0xbc, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xda, 0x73, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xed,
    0xca, 0x9f, 0xf4, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaf, 0xfb, 0xaa, 0xaa, 0xaa, 0xaa, 0x30,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x33, 0x33, 0x33,
    0x33, 0x3f, 0xf4, 0x33, 0x33, 0x33, 0x33, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x2f, 0xf3, 0x22, 0x22, 0x22, 0x22, 0x21,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x8c, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcf, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xee, 0xee, 0xef, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xeb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xc7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x8e, 0xee, 0xff, 0xfe, 0xee,
    0xee, 0xee, 0xee, 0xef, 0xff, 0xee, 0xec, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x1, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xf5,
    0x0, 0x1e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf3, 0xb, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xeb, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0xfe, 0xbf,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xbf, 0xfb, 0x10, 0x8f, 0xfe, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xf6, 0x0,
    0x0, 0x4d, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x16,
    0xcf, 0xff, 0x91, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfe, 0x82, 0x0, 0x8f, 0xff, 0xf9, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x8e, 0xff, 0xfd, 0x2,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbf, 0x50, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+65CB "旋" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0x90, 0x0, 0x0, 0x0, 0x9e, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x9, 0xfd, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb0, 0x9, 0xdd, 0xdd, 0xee, 0xdd, 0xd9, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x8f, 0x91, 0x11, 0x11,
    0x11, 0x11, 0x10, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xee, 0x0, 0x0, 0xd, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x4, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xef, 0x80, 0x0, 0x0, 0xfe, 0x22, 0x23, 0x10,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xd0, 0x1, 0x11, 0x1b,
    0xf2, 0x11, 0xcf, 0x10, 0x0, 0x0, 0xfe, 0xbb,
    0xbf, 0xd0, 0x0, 0x0, 0xb, 0xf1, 0x2, 0xfa,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0xf, 0xd0, 0x6,
    0x80, 0xb, 0xf1, 0x5, 0xf3, 0x0, 0x0, 0x2,
    0xfa, 0x0, 0xf, 0xc0, 0xb, 0xf0, 0xb, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xf9, 0x0, 0xf,
    0xc0, 0xc, 0xe0, 0xb, 0xfb, 0xaa, 0xaa, 0x0,
    0x0, 0x6, 0xf7, 0x0, 0x1f, 0xb0, 0xe, 0xd0,
    0xb, 0xff, 0xff, 0xff, 0x10, 0x0, 0x8, 0xf4,
    0x0, 0x1f, 0xb0, 0xf, 0xd0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x2f, 0xa0,
    0x1f, 0xf1, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xe0, 0x0, 0x3f, 0x90, 0x4f, 0xf8, 0xb,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x4f, 0x80, 0x8f, 0xdf, 0x1b, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x6f, 0x70, 0xde,
    0x3f, 0xdc, 0xf1, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x9f, 0x54, 0xf8, 0x7, 0xff, 0xf4,
    0x10, 0x0, 0x10, 0xc, 0xf7, 0xb, 0xbb, 0xff,
    0x2e, 0xf1, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf1,
    0x6, 0xc0, 0x9, 0xff, 0xe6, 0xa, 0x50, 0x0,
    0x1, 0x69, 0xbb, 0xbb, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+670D "服" */
    0x0, 0xdf, 0xdd, 0xde, 0xf9, 0x1, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xb0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xa0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xfc, 0x0, 0x2, 0xfa, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0xf, 0xc0,
    0x0, 0x2f, 0xa0, 0x1f, 0xc0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0xfc, 0x0, 0x2, 0xfa, 0x1,
    0xfc, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0xf,
    0xc0, 0x0, 0x2f, 0xa0, 0x1f, 0xc0, 0x0, 0x45,
    0x56, 0xfe, 0x0, 0x0, 0xff, 0xcc, 0xcd, 0xfa,
    0x1, 0xfc, 0x0, 0x6, 0xff, 0xff, 0x90, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xa0, 0x1f, 0xc0, 0x0,
    0x4, 0x33, 0x10, 0x0, 0x0, 0xfc, 0x0, 0x3,
    0xfa, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x2f, 0xa0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0xfc, 0x0,
    0x2, 0xfa, 0x1, 0xff, 0xef, 0xdd, 0xdd, 0xde,
    0xf6, 0x0, 0xf, 0xc0, 0x0, 0x2f, 0xa0, 0x1f,
    0xc5, 0xf5, 0x0, 0x0, 0xaf, 0x20, 0x0, 0xff,
    0xcc, 0xcd, 0xfa, 0x1, 0xfc, 0xf, 0xb0, 0x0,
    0xf, 0xe0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xa0,
    0x1f, 0xc0, 0x9f, 0x20, 0x6, 0xf8, 0x0, 0x2,
    0xfa, 0x0, 0x3, 0xfa, 0x1, 0xfc, 0x2, 0xfa,
    0x0, 0xdf, 0x20, 0x0, 0x4f, 0x80, 0x0, 0x2f,
    0xa0, 0x1f, 0xc0, 0xa, 0xf3, 0x5f, 0xa0, 0x0,
    0x6, 0xf6, 0x0, 0x2, 0xfa, 0x1, 0xfc, 0x0,
    0x2f, 0xde, 0xf2, 0x0, 0x0, 0x8f, 0x40, 0x0,
    0x2f, 0xa0, 0x1f, 0xc0, 0x0, 0x8f, 0xf8, 0x0,
    0x0, 0xb, 0xf1, 0x0, 0x2, 0xfa, 0x1, 0xfc,
    0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0xee, 0x0,
    0x0, 0x2f, 0xa0, 0x1f, 0xc0, 0x7, 0xfe, 0xef,
    0x70, 0x0, 0x3f, 0xa0, 0x0, 0x2, 0xfa, 0x1,
    0xfc, 0x9, 0xfe, 0x23, 0xff, 0xa0, 0x9, 0xf5,
    0x2, 0xcc, 0xef, 0x90, 0x1f, 0xcc, 0xfd, 0x20,
    0x3, 0xef, 0xe2, 0x5c, 0x0, 0xe, 0xff, 0xc2,
    0x1, 0xfc, 0x39, 0x0, 0x0, 0x1, 0xa9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+676F "杯" */
    0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x80, 0x0,
    0x0, 0xd, 0xf1, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x1d, 0xf2, 0x11, 0x0, 0x0,
    0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0xb, 0xdd, 0xdf, 0xfd, 0xdd,
    0x70, 0x0, 0x3, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfc, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xbf, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0,
    0x7, 0xfe, 0xdf, 0x2a, 0xfd, 0x10, 0x0, 0x0,
    0x3, 0xff, 0xf7, 0xfb, 0x0, 0x6f, 0xf4, 0xcf,
    0x20, 0x9f, 0xd1, 0x0, 0x0, 0xa, 0xed, 0xf1,
    0x9f, 0x28, 0xff, 0x50, 0xcf, 0x20, 0xa, 0xfd,
    0x10, 0x0, 0x1f, 0x8d, 0xf1, 0x5, 0xbf, 0xf5,
    0x0, 0xcf, 0x20, 0x0, 0xbf, 0xb0, 0x0, 0x9f,
    0x2d, 0xf1, 0xc, 0xfe, 0x40, 0x0, 0xcf, 0x20,
    0x0, 0xd, 0xa0, 0x2, 0xfb, 0xd, 0xf1, 0x1,
    0xa2, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x1, 0x0,
    0xb, 0xf3, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x0, 0x0, 0xc, 0x20, 0xd, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x0, 0x0,

    /* U+6B62 "止" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0xee,
    0xee, 0xee, 0xee, 0x80, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xae, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xea,

    /* U+6B65 "步" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9d, 0x20, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0xb, 0xfc, 0xcc,
    0xcc, 0xcc, 0xc0, 0x0, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xee, 0xff, 0xee, 0xee, 0xef, 0xfe, 0xee,
    0xee, 0xee, 0xee, 0xe5, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x0, 0x2f, 0xe0, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x6f, 0xe1,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0xdf, 0x90, 0x0,
    0x0, 0x5, 0xff, 0x40, 0x0, 0x2f, 0xe0, 0x0,
    0xa, 0xfe, 0x10, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x2f, 0xe0, 0x0, 0x8f, 0xf3, 0x0, 0x0,
    0x8, 0xff, 0x60, 0x0, 0x0, 0x2f, 0xe0, 0xa,
    0xff, 0x50, 0x0, 0x0, 0x2, 0xe5, 0x0, 0x0,
    0x0, 0x2f, 0xe3, 0xdf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xdf, 0xff, 0xa2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x47, 0xbe, 0xff,
    0xfe, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xd9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xb8, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C23 "氣" */
    0x0, 0x0, 0x4, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf8, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x70, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x39, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x7f, 0xf5, 0x4, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x70, 0x0,
    0x4f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x28, 0x88, 0x88, 0x99, 0x88, 0x88, 0x88,
    0x8a, 0xfa, 0x0, 0x0, 0x0, 0x7, 0x70, 0x0,
    0xee, 0x0, 0x5, 0x71, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0xa, 0xf5, 0x0, 0xee, 0x0, 0x1e, 0xe1,
    0x4, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xee, 0x0,
    0xee, 0x0, 0xbf, 0x30, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x7d, 0x20, 0xee, 0x6, 0xf6, 0x0,
    0x3, 0xfb, 0x0, 0x0, 0x7, 0x88, 0x88, 0x88,
    0xff, 0x88, 0xb8, 0x88, 0x62, 0xfb, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc2, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0xee, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xd1, 0xee, 0x7, 0xc2, 0x0,
    0x0, 0xef, 0x0, 0x31, 0x0, 0x0, 0x6f, 0xa0,
    0xee, 0x6, 0xfe, 0x30, 0x0, 0xcf, 0x20, 0x6d,
    0x0, 0x7, 0xfd, 0x0, 0xee, 0x0, 0x3e, 0xf5,
    0x0, 0x9f, 0x60, 0x7e, 0x0, 0xaf, 0xd1, 0x0,
    0xee, 0x0, 0x2, 0xef, 0x60, 0x4f, 0xd0, 0xac,
    0x1e, 0xfc, 0x10, 0x0, 0xee, 0x0, 0x0, 0x2e,
    0xb0, 0xc, 0xff, 0xf9, 0x6, 0x80, 0x0, 0x0,
    0xee, 0x0, 0x0, 0x2, 0x0, 0x1, 0xbf, 0xd2,

    /* U+6C34 "水" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xa9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x2, 0xff, 0x0, 0x0, 0x1,
    0xdf, 0x90, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0x32, 0xff, 0x40, 0x0, 0xb, 0xfd, 0x10, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x22, 0xff, 0xc0,
    0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x2, 0xff, 0xf4, 0x9, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x2,
    0xff, 0xfd, 0x9f, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf5, 0x2, 0xfe, 0x8f, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf0, 0x2, 0xfe, 0xe, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x2, 0xfe,
    0x5, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x2, 0xfe, 0x0, 0xaf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0,
    0x2, 0xfe, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe1, 0x0, 0x2, 0xfe, 0x0,
    0x2, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x50, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x3f, 0xfc,
    0x10, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x3, 0xef, 0xe6, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xc0, 0x7, 0xf9, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x40, 0x0, 0x30, 0x0, 0x0, 0x21, 0x14, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C96 "沖" */
    0x0, 0x8, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdb, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xe1, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbd, 0xdd, 0xdd, 0xdf, 0xfd,
    0xdd, 0xdd, 0xd1, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x37, 0x10, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x2f,
    0xd0, 0x0, 0xd, 0xf1, 0xc, 0xff, 0xa3, 0x0,
    0xd, 0xf0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0xdf,
    0x10, 0x6, 0xdf, 0xf9, 0x0, 0xdf, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x6e,
    0x70, 0xd, 0xf0, 0x0, 0x1, 0xfd, 0x0, 0x0,
    0xdf, 0x10, 0x0, 0x0, 0x10, 0x0, 0xdf, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0xd, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xdd, 0xdd, 0xdf, 0xfd, 0xdd, 0xdf, 0xf1, 0x0,
    0x0, 0x0, 0xa6, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x3f, 0xd0,
    0xdf, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0xb, 0xf6, 0x7, 0x90, 0x0, 0x1,
    0xfd, 0x0, 0x0, 0x56, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0,

    /* U+6CE1 "泡" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xd3, 0x0, 0x0, 0x5f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf,
    0xf8, 0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x2, 0xff,
    0xba, 0xaa, 0xaa, 0xaa, 0xab, 0xa0, 0x0, 0x0,
    0x0, 0x30, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x22, 0x22, 0x22, 0x22, 0x2f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x27, 0x10, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0xb, 0xff, 0x80, 0x4, 0xff, 0xec, 0xcc, 0xcc,
    0xcc, 0x90, 0x0, 0xfe, 0x0, 0x19, 0xff, 0xe4,
    0x4e, 0x8f, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xf,
    0xe0, 0x0, 0x2, 0xbf, 0x50, 0x10, 0xfe, 0x0,
    0x0, 0xf, 0xd0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x40, 0x0, 0xf, 0xe0, 0x0, 0x0, 0xfd, 0x0,
    0x2f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0xf, 0xd0, 0x3, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x11, 0x11, 0xfd,
    0x0, 0x4f, 0xa0, 0x0, 0x0, 0x1, 0xa1, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0x7f, 0x80, 0xf, 0xfa, 0xaa, 0xaa,
    0xa8, 0x0, 0xaf, 0x70, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0xcc, 0xdf, 0xf3,
    0x0, 0x0, 0x7, 0xfa, 0x0, 0xf, 0xe0, 0x0,
    0x0, 0xa, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xef,
    0x30, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xad, 0x20, 0x1f,
    0xf3, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x9, 0xfa, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x1,
    0xff, 0x20, 0x0, 0x0, 0xbf, 0xfd, 0xcc, 0xcc,
    0xcc, 0xcd, 0xff, 0x90, 0x2, 0x70, 0x0, 0x0,
    0x1, 0xae, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x90,
    0x0,

    /* U+6D88 "消" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x1, 0xdd, 0x40, 0x0,
    0x18, 0x30, 0x0, 0x1f, 0xe0, 0x0, 0x5, 0xa3,
    0x1, 0xaf, 0xfa, 0x10, 0x4f, 0xd0, 0x0, 0x1f,
    0xe0, 0x0, 0xd, 0xf6, 0x0, 0x4, 0xdf, 0xf3,
    0xa, 0xf7, 0x0, 0x1f, 0xe0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x9, 0xc0, 0x1, 0xff, 0x10, 0x1f,
    0xe0, 0x1, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x80, 0x1f, 0xe0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x20, 0x1f,
    0xe0, 0x3, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x43, 0x33, 0x4f, 0xe3, 0x33, 0x33, 0x20,
    0x4f, 0x81, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x5e, 0xff, 0x70, 0x0,
    0xf, 0xf9, 0x99, 0x99, 0x99, 0x99, 0xaf, 0xd0,
    0x0, 0x8f, 0xfc, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x2, 0xc5, 0x0,
    0xf, 0xd0, 0x87, 0x30, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xd1, 0xcf, 0xfe,
    0xa4, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xd0, 0x0, 0x4a, 0xff, 0xe2, 0x1f, 0xd0,
    0x0, 0x0, 0x2, 0x50, 0xf, 0xd0, 0x0, 0x0,
    0x6, 0x90, 0x1f, 0xd0, 0x0, 0x0, 0xb, 0xf3,
    0xf, 0xd0, 0x0, 0x0, 0x0, 0x36, 0xaf, 0xd0,
    0x0, 0x0, 0x3f, 0xd0, 0xf, 0xd0, 0x2, 0x58,
    0xcf, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0x60,
    0xf, 0xc8, 0xef, 0xff, 0xea, 0x73, 0x1f, 0xd0,
    0x0, 0x6, 0xfd, 0x0, 0x2f, 0xb5, 0xda, 0x62,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x1e, 0xf4, 0x0,
    0x4f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x9f, 0xb0, 0x0, 0x7f, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x4, 0xff, 0x20, 0x0,
    0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0xb, 0xf8, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0xb, 0xcc, 0xdf, 0xb0, 0x1, 0x90, 0x0, 0x1,
    0xc7, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+6E05 "清" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xfc, 0x12, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x1b,
    0xfe, 0x27, 0x77, 0x77, 0x77, 0xff, 0x87, 0x77,
    0x77, 0x72, 0x0, 0x0, 0x7, 0x90, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x37,
    0x77, 0x77, 0x7f, 0xf8, 0x77, 0x77, 0x74, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xd, 0xe5, 0x0, 0x5,
    0x55, 0x55, 0x55, 0x5e, 0xf6, 0x55, 0x55, 0x55,
    0x40, 0x8f, 0xfb, 0x20, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x2b, 0xfc,
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x20, 0x0, 0x5, 0x20, 0x0, 0x27, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x7, 0x40, 0x4, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0xee, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x6f, 0x80, 0x4, 0xfc, 0x66, 0x66,
    0x66, 0x66, 0x6f, 0xf0, 0x0, 0x0, 0xd, 0xf1,
    0x0, 0x5f, 0x90, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x6, 0xfc, 0x77,
    0x77, 0x77, 0x77, 0x7f, 0xf0, 0x0, 0x0, 0xef,
    0x20, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x1e,
    0xf2, 0x0, 0x2, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x6, 0xf9, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x3, 0xa9, 0xaf, 0xe0, 0x0,
    0x6, 0x10, 0x0, 0x4, 0xe2, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6F54 "潔" */
    0x0, 0x73, 0x0, 0x0, 0x0, 0x54, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0x25, 0x55, 0x55, 0x56,
    0x50, 0x0, 0x3f, 0xf7, 0x1f, 0xff, 0xff, 0xff,
    0xc6, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x3e,
    0xf1, 0x66, 0x6f, 0xd6, 0x65, 0x2, 0x4f, 0x92,
    0x2e, 0xe0, 0x0, 0x0, 0x35, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0x4, 0xf5, 0x0, 0xee, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf7, 0x0, 0x8f,
    0x20, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x4, 0x66,
    0xfd, 0x66, 0x30, 0xd, 0xe0, 0x0, 0xfb, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x2f, 0xd5, 0x77, 0x4,
    0xf8, 0x0, 0x3f, 0xa0, 0xb, 0xf6, 0x0, 0x2f,
    0xff, 0xff, 0xfe, 0xc1, 0xee, 0x13, 0x39, 0xf7,
    0x0, 0x4e, 0xfa, 0x0, 0x86, 0x5f, 0xc0, 0x0,
    0xdf, 0x40, 0xaf, 0xfe, 0x10, 0x0, 0x1c, 0xfa,
    0x0, 0x0, 0xb9, 0x7, 0x74, 0x30, 0x2, 0x43,
    0x0, 0x0, 0x0, 0xa, 0x20, 0x0, 0x0, 0x2c,
    0xfc, 0x20, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x9f, 0xe6, 0x0, 0x3d, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xfe, 0xff, 0xff, 0xe6, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x61, 0x0, 0x99, 0x76, 0x6c, 0xff, 0x81,
    0x5, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xb0, 0x0,
    0x1, 0x7e, 0xf9, 0x10, 0x0, 0xa, 0xf6, 0x0,
    0x0, 0x5, 0xf8, 0x0, 0x49, 0xff, 0xfa, 0xaa,
    0xbc, 0xde, 0xff, 0xf3, 0x0, 0x0, 0xbf, 0x30,
    0x8f, 0xff, 0xfe, 0xdf, 0xfb, 0x98, 0x76, 0x6f,
    0xe0, 0x0, 0x1f, 0xd0, 0x1, 0x31, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x65, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0xa, 0xb1, 0xb, 0xf1, 0xa, 0xd3,
    0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x1c, 0xfb,
    0x0, 0xbf, 0x10, 0x5f, 0xf7, 0x0, 0x0, 0x4f,
    0xc0, 0x0, 0x5e, 0xf9, 0x0, 0xb, 0xf1, 0x0,
    0x2c, 0xfb, 0x10, 0xb, 0xf6, 0x0, 0x6f, 0xe4,
    0x1, 0x66, 0xdf, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x4d, 0x0, 0x0, 0x60, 0x0, 0xe, 0xff, 0x90,
    0x0, 0x0, 0x7, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6FC3 "濃" */
    0x0, 0x5, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb0,
    0x7, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd4,
    0x0, 0x3, 0x33, 0x3c, 0xe3, 0x3a, 0xf4, 0x33,
    0x33, 0x0, 0x0, 0x19, 0xff, 0x90, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x3e, 0xf4, 0xe, 0xc1, 0x1b, 0xd1, 0x19,
    0xf2, 0x11, 0xde, 0x0, 0x0, 0x0, 0x1, 0x70,
    0xe, 0xc1, 0x1b, 0xd1, 0x19, 0xf2, 0x11, 0xde,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xc2, 0x2b, 0xe2, 0x29, 0xf4,
    0x22, 0xde, 0x0, 0x2, 0x30, 0x0, 0x0, 0xe,
    0xc0, 0xa, 0xd0, 0x8, 0xf1, 0x0, 0xde, 0x0,
    0xc, 0xfb, 0x20, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x3, 0xcf, 0xf7,
    0x0, 0x5, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x65, 0x0, 0x0, 0x5, 0xeb, 0x0, 0x38, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x50, 0x0,
    0x0, 0x12, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x95, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x50, 0x0, 0x0, 0x0,
    0x3, 0x50, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xa, 0xf0, 0x7f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xb0, 0x8f, 0xba, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0x90, 0x0, 0x0, 0xaf,
    0x30, 0xaf, 0xee, 0xff, 0xee, 0xff, 0xee, 0xee,
    0xee, 0xd0, 0x0, 0x2, 0xfb, 0x0, 0xde, 0x0,
    0xfb, 0x0, 0x9f, 0x30, 0x1, 0xac, 0x0, 0x0,
    0xb, 0xf4, 0x1, 0xfb, 0x0, 0xfb, 0x0, 0x1e,
    0xe2, 0x8f, 0xe6, 0x0, 0x0, 0x3f, 0xc0, 0x7,
    0xf6, 0x0, 0xfb, 0x0, 0x3, 0xff, 0xe7, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0xe, 0xf0, 0x0, 0xfb,
    0x26, 0xa3, 0x3f, 0xf8, 0x10, 0x0, 0x3, 0xfb,
    0x0, 0xaf, 0x70, 0x7, 0xff, 0xff, 0xd4, 0x2,
    0xcf, 0xfa, 0x50, 0x0, 0x63, 0x1, 0xcc, 0x0,
    0xa, 0xfc, 0x72, 0x0, 0x0, 0x5, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+71B1 "熱" */
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x10, 0x0, 0x0, 0x0, 0xbf, 0x10, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0xb, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x77,
    0xdf, 0x87, 0x77, 0x1, 0x11, 0xcf, 0x11, 0x32,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x4, 0x99,
    0x99, 0xef, 0x99, 0x99, 0x86, 0xaa, 0xff, 0xaa,
    0xef, 0x0, 0x0, 0x6e, 0xef, 0xfe, 0xef, 0xfe,
    0xec, 0x0, 0xe, 0xd0, 0xc, 0xf0, 0x0, 0x0,
    0x0, 0x9f, 0x0, 0xdb, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0xcf, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0xc,
    0xb0, 0x1, 0x7c, 0x3f, 0xa0, 0xc, 0xf0, 0x0,
    0x2, 0x8f, 0xe1, 0x0, 0x9f, 0xff, 0xe7, 0xff,
    0xf8, 0x0, 0xcf, 0x0, 0x0, 0x7f, 0x91, 0xb,
    0xf1, 0x23, 0x32, 0x3, 0xef, 0xb0, 0xc, 0xf0,
    0x0, 0x0, 0x22, 0x22, 0xbf, 0x32, 0x22, 0x0,
    0xa, 0xff, 0xd2, 0xcf, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0xee, 0x8f, 0xcc,
    0xf0, 0x1a, 0x10, 0x45, 0x55, 0xcf, 0x65, 0x55,
    0x0, 0x5f, 0x90, 0x54, 0xcf, 0x2, 0xf3, 0x0,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0xd, 0xf2, 0x0,
    0xc, 0xf0, 0x2f, 0x20, 0x0, 0x13, 0xcf, 0x89,
    0xbc, 0xc9, 0xf9, 0x0, 0x0, 0xbf, 0x4, 0xf1,
    0x9e, 0xff, 0xff, 0xff, 0xfe, 0xcf, 0xfd, 0x10,
    0x0, 0xa, 0xfe, 0xfe, 0x7, 0xba, 0x86, 0x53,
    0x10, 0x0, 0x5d, 0x20, 0x0, 0x0, 0x19, 0xba,
    0x30, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0xee,
    0x10, 0x19, 0x70, 0x0, 0x7d, 0x20, 0x5, 0xfc,
    0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0xfe, 0x0,
    0x6, 0xf9, 0x0, 0xb, 0xfa, 0x0, 0x0, 0x2f,
    0xf2, 0x0, 0xd, 0xf0, 0x0, 0x1f, 0xe0, 0x0,
    0x1e, 0xf7, 0x0, 0x1d, 0xf6, 0x0, 0x0, 0xcf,
    0x20, 0x0, 0xbf, 0x40, 0x0, 0x5f, 0xf2, 0x5,
    0xe9, 0x0, 0x0, 0x9, 0xb2, 0x0, 0x6, 0xa3,
    0x0, 0x0, 0xbd, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+71D9 "燙" */
    0x0, 0x39, 0x20, 0x0, 0x2, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x32, 0x0, 0x0, 0x8, 0xff, 0xa2,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x1, 0xaf, 0xf1, 0xe, 0xe0, 0x0,
    0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x36, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x2, 0x0, 0x0, 0x0, 0xe, 0xe2,
    0x22, 0x22, 0x22, 0x24, 0xfb, 0x0, 0x2, 0xfe,
    0x70, 0x0, 0x0, 0xee, 0x55, 0x55, 0x55, 0x55,
    0x7f, 0xb0, 0x0, 0x4, 0xcf, 0xe5, 0x0, 0xc,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd9, 0x0, 0x0,
    0x0, 0x4d, 0x50, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x40, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x1, 0x90, 0x0, 0x1d, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x6f, 0x70, 0x3e, 0xf7, 0x6b,
    0xf9, 0x69, 0xfa, 0x68, 0xf8, 0x0, 0x0, 0x2f,
    0xc0, 0x5f, 0xe4, 0x3, 0xfc, 0x0, 0xce, 0x10,
    0x6f, 0x60, 0x0, 0x1d, 0xf2, 0x0, 0x81, 0x3,
    0xee, 0x10, 0x8f, 0x60, 0x9, 0xf3, 0x0, 0xb,
    0xf7, 0x0, 0x0, 0x6, 0xfe, 0x20, 0x6f, 0xa4,
    0x88, 0xfe, 0x0, 0x0, 0x5b, 0x0, 0x0, 0x0,
    0x1a, 0x25, 0x7f, 0xc0, 0x3e, 0xec, 0x40, 0x0,
    0x0, 0x0, 0x51, 0x0, 0x0, 0x4, 0xfa, 0x20,
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x3f, 0xc1,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0x1, 0xcf, 0x50,
    0x0, 0x0, 0x0, 0x5f, 0xc1, 0x0, 0x3f, 0xf1,
    0x0, 0x1, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x2e, 0xff, 0xb0, 0x3, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x74, 0x7f, 0xf6,
    0x7f, 0xd5, 0x6, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xef, 0xe4, 0x0, 0x4e, 0xfd, 0x84,
    0x10, 0x0, 0x0, 0x28, 0xad, 0xff, 0xfd, 0x60,
    0x0, 0x0, 0x7, 0xdf, 0xff, 0xec, 0xb8, 0x0,
    0xef, 0xda, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x9c, 0xef, 0x40, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7A0D "稍" */
    0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0,
    0x1d, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5a,
    0xff, 0x20, 0x88, 0x0, 0x2, 0xfb, 0x0, 0x4,
    0xb4, 0x3, 0x9c, 0xff, 0xfe, 0x92, 0xd, 0xf4,
    0x0, 0x2f, 0xb0, 0x0, 0xbf, 0x50, 0x3f, 0xeb,
    0xfd, 0x0, 0x0, 0x4f, 0xd0, 0x2, 0xfb, 0x0,
    0x3f, 0xd0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x0,
    0xcf, 0x60, 0x2f, 0xb0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0xed, 0x0, 0x0, 0x4, 0xfe, 0x2, 0xfb,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0x0, 0xa, 0x40, 0x2f, 0xb0, 0x29, 0x10, 0x0,
    0x22, 0x22, 0xfe, 0x22, 0x20, 0x24, 0x33, 0x34,
    0xfc, 0x33, 0x34, 0x20, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x8a, 0xac, 0xff, 0xaa, 0xa0, 0xdf, 0x88,
    0x88, 0x88, 0x88, 0x8f, 0xd0, 0x0, 0x0, 0x9f,
    0xe0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0xf, 0xff, 0xb0, 0x0, 0xdf,
    0x9, 0x72, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x6,
    0xff, 0xef, 0x90, 0xd, 0xf3, 0xcf, 0xfe, 0x93,
    0x0, 0xfd, 0x0, 0x0, 0xcc, 0xed, 0x6f, 0x60,
    0xdf, 0x0, 0x15, 0xbf, 0xfd, 0xf, 0xd0, 0x0,
    0x4f, 0x6e, 0xd0, 0xca, 0xd, 0xf0, 0x0, 0x0,
    0x17, 0x80, 0xfd, 0x0, 0xc, 0xf0, 0xed, 0x2,
    0x10, 0xdf, 0x0, 0x0, 0x0, 0x4, 0x7f, 0xd0,
    0x6, 0xf9, 0xe, 0xd0, 0x0, 0xd, 0xf0, 0x2,
    0x69, 0xdf, 0xff, 0xfd, 0x0, 0xdf, 0x10, 0xed,
    0x0, 0x0, 0xee, 0xaf, 0xff, 0xfd, 0xa6, 0x2f,
    0xd0, 0x6, 0x80, 0xe, 0xd0, 0x0, 0xf, 0xd8,
    0xd9, 0x51, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0xed, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xd0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xed, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0xef, 0x0, 0x0, 0x0, 0x6c, 0xcc, 0xfc, 0x0,
    0x0, 0x0, 0xed, 0x0, 0x19, 0x80, 0x0, 0x0,
    0x3, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7B49 "等" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x80,
    0x0, 0x0, 0x0, 0x3, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x1, 0xef, 0xcc, 0xec, 0xcc, 0xcb,
    0x8, 0xfd, 0xcc, 0xdc, 0xcc, 0xcc, 0x0, 0xcf,
    0x90, 0x3f, 0xc1, 0x0, 0x3, 0xfe, 0x0, 0xde,
    0x20, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x6f, 0xd1,
    0x0, 0xef, 0x50, 0x5, 0xfd, 0x10, 0x0, 0x1,
    0xa1, 0x0, 0x0, 0x6e, 0x20, 0xae, 0x90, 0x0,
    0x6, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0xf, 0xf1, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x5, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaf, 0xfa, 0xaa, 0xaa, 0xaa, 0xaa,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x29, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x50, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x5a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xef, 0xba, 0xaa, 0xa4, 0x0, 0x0, 0x0,
    0x9e, 0x30, 0x0, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0x80, 0x0, 0x0, 0xa, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0x70, 0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x90, 0x8, 0x99, 0x9e,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xe9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7D61 "絡" */
    0x0, 0x0, 0x2, 0xa3, 0x0, 0x0, 0x0, 0x2,
    0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf7, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x9f, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x93, 0x0, 0x8, 0xfd, 0xaa, 0xaa, 0xad, 0xfa,
    0x0, 0x0, 0xc, 0xf3, 0x5, 0xfe, 0x0, 0x6f,
    0xfb, 0x0, 0x0, 0x1e, 0xf2, 0x0, 0x0, 0x7f,
    0x80, 0xd, 0xf4, 0x6, 0xff, 0xcf, 0x60, 0x0,
    0xaf, 0x80, 0x0, 0x4, 0xfe, 0x45, 0xaf, 0xa0,
    0x6f, 0xf6, 0xe, 0xf3, 0x7, 0xfd, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xfe, 0x10, 0x9, 0x50, 0x3,
    0xfe, 0x7f, 0xe2, 0x0, 0x0, 0x7, 0x86, 0x4e,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x98, 0xd0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x2c, 0xfe,
    0x8f, 0xf9, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x2,
    0xf9, 0x0, 0x18, 0xff, 0xb1, 0x3, 0xef, 0xe6,
    0x0, 0x3, 0xef, 0xdd, 0xff, 0xfe, 0x18, 0xff,
    0xf6, 0x0, 0x0, 0x19, 0xff, 0xe3, 0xa, 0xff,
    0xdb, 0x86, 0x9f, 0x3c, 0xff, 0xba, 0xaa, 0xaa,
    0xaa, 0xdf, 0xa0, 0x4, 0x41, 0x0, 0x0, 0x38,
    0x21, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x31, 0x2, 0x31, 0xc5, 0x0, 0x4f, 0xa1,
    0x11, 0x11, 0x14, 0xfb, 0x0, 0x0, 0xec, 0xd,
    0xb0, 0xdb, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0xfa, 0xa, 0xe0, 0x9f, 0x10,
    0x4f, 0x90, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x3,
    0xf7, 0x8, 0xf0, 0x4f, 0x50, 0x4f, 0x90, 0x0,
    0x0, 0x2, 0xfb, 0x0, 0x6, 0xf4, 0x7, 0xf2,
    0xf, 0x90, 0x4f, 0x90, 0x0, 0x0, 0x2, 0xfb,
    0x0, 0xa, 0xf1, 0x5, 0xf3, 0x5, 0x10, 0x4f,
    0xeb, 0xbb, 0xbb, 0xbb, 0xfb, 0x0, 0xe, 0xd0,
    0x3, 0x92, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x90, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7E2E "縮" */
    0x0, 0x0, 0x34, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x30,
    0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x1, 0x11, 0x11, 0x19,
    0xf8, 0x11, 0x11, 0x11, 0x0, 0x9, 0xf4, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x2f, 0xb0, 0x8, 0xb, 0xf9, 0x99, 0x99,
    0x99, 0x99, 0x9b, 0xfb, 0x0, 0xaf, 0x20, 0x7f,
    0x7b, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfb,
    0x4, 0xf9, 0x0, 0xee, 0xb, 0xf1, 0x76, 0x0,
    0x0, 0x0, 0x3, 0xd9, 0x1d, 0xe2, 0x39, 0xf5,
    0x2, 0x30, 0xfc, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,
    0xbf, 0xff, 0xff, 0xb0, 0x0, 0x4, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x8a, 0x86, 0xef, 0x20,
    0x0, 0x9, 0xf2, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0x0, 0x6, 0xf7, 0x9a, 0x0, 0xf, 0xd0, 0x0,
    0xa, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xc0, 0x7f,
    0x10, 0x6f, 0x80, 0x59, 0x9e, 0xf9, 0x99, 0x91,
    0x0, 0xde, 0x10, 0x1f, 0x60, 0xdf, 0x70, 0xaf,
    0xff, 0xff, 0xff, 0xf2, 0xa, 0xfb, 0xac, 0xff,
    0xb6, 0xff, 0x70, 0xaf, 0x10, 0x0, 0xa, 0xf2,
    0x8f, 0xff, 0xfc, 0x9b, 0xef, 0xff, 0x70, 0xaf,
    0x0, 0x0, 0x9, 0xf2, 0x59, 0x51, 0x0, 0x0,
    0x2f, 0x7f, 0x70, 0xaf, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x0, 0x0, 0x19, 0x22, 0x2f, 0x70, 0xaf,
    0xaa, 0xaa, 0xae, 0xf2, 0xd, 0x70, 0xf7, 0xe,
    0x70, 0x2f, 0x70, 0xaf, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0x70, 0xe9, 0xa, 0xd0, 0x2f, 0x70, 0xaf,
    0x0, 0x0, 0x9, 0xf2, 0x2f, 0x40, 0xcb, 0x5,
    0xf1, 0x2f, 0x70, 0xaf, 0x0, 0x0, 0x9, 0xf2,
    0x4f, 0x10, 0xad, 0x1, 0xf6, 0x2f, 0x70, 0xaf,
    0x0, 0x0, 0x9, 0xf2, 0x8d, 0x0, 0x8f, 0x0,
    0x83, 0x2f, 0x70, 0xaf, 0xaa, 0xaa, 0xad, 0xf2,
    0xc9, 0x0, 0x59, 0x0, 0x0, 0x2f, 0x70, 0xaf,
    0xff, 0xff, 0xff, 0xf2, 0x33, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x70, 0xaf, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x12,
    0x0, 0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xff, 0x77, 0x77,
    0xfe, 0x77, 0x77, 0xef, 0x77, 0x77, 0xdf, 0x40,
    0x0, 0xfe, 0x0, 0x0, 0xfd, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0xfe, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xaf, 0x40,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x66, 0x66, 0x66,
    0x66, 0x9f, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x17, 0x77, 0x77, 0x77, 0x77, 0xdf, 0x97, 0x77,
    0x77, 0x77, 0x77, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0xff, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0xef, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0x1, 0xff, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0xff, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0xef, 0x0, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x74,

    /* U+7F8E "美" */
    0x0, 0x0, 0x4, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x19, 0x40, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0x40, 0x0, 0x0, 0x2,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x7, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcf, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11,
    0x11, 0x2f, 0xf1, 0x11, 0x11, 0x11, 0x10, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x19, 0x99, 0x99,
    0x99, 0x9f, 0xf9, 0x99, 0x99, 0x99, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0x66, 0x66, 0x66,
    0x66, 0x7f, 0xf6, 0x66, 0x66, 0x66, 0x66, 0x64,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x25, 0x55, 0x55, 0x55,
    0x55, 0x8f, 0xd5, 0x55, 0x55, 0x55, 0x55, 0x53,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xbb, 0xbb, 0xbb,
    0xbb, 0xef, 0xdb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x28, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xf5, 0xaf, 0xfa,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xb0, 0x4, 0xbf, 0xfc, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8f, 0xfc, 0x0, 0x0, 0x3,
    0xbf, 0xfd, 0x50, 0x0, 0x0, 0x36, 0xbf, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xfc, 0x30,
    0x8f, 0xff, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xf6, 0x2e, 0xb8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+806F "聯" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x10,
    0x0, 0x0, 0x51, 0x0, 0x0, 0xb, 0xcc, 0xcc,
    0xcc, 0xc8, 0x0, 0x9f, 0x50, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xfb, 0x1,
    0xfb, 0x0, 0x0, 0xa, 0xe1, 0x0, 0x0, 0x0,
    0xaf, 0x0, 0xf, 0xa0, 0xa, 0xf2, 0x16, 0x0,
    0x3f, 0x60, 0x60, 0x0, 0x0, 0xaf, 0x0, 0xf,
    0xa0, 0x5f, 0x70, 0x9f, 0x30, 0xdc, 0x5, 0xf5,
    0x0, 0x0, 0xaf, 0x0, 0xf, 0xa3, 0xfc, 0x25,
    0xf9, 0xb, 0xf6, 0x5d, 0xd0, 0x0, 0x0, 0xaf,
    0x0, 0x1f, 0xa4, 0xff, 0xff, 0xd0, 0xc, 0xff,
    0xff, 0x30, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0,
    0x85, 0xdf, 0x20, 0x3, 0x44, 0xf9, 0x0, 0x0,
    0x0, 0xaf, 0x99, 0x9f, 0xa0, 0x7, 0xf6, 0x17,
    0x0, 0xc, 0xe1, 0x88, 0x0, 0x0, 0xaf, 0x0,
    0xf, 0xa0, 0x5f, 0x90, 0x1f, 0x40, 0x8f, 0x40,
    0x6f, 0x0, 0x0, 0xaf, 0x0, 0xf, 0xa6, 0xfe,
    0x78, 0xaf, 0xa7, 0xfd, 0xab, 0xdf, 0x70, 0x0,
    0xaf, 0x0, 0xf, 0xa7, 0xff, 0xdb, 0xab, 0xe8,
    0xfd, 0xa8, 0x6b, 0xd0, 0x0, 0xaf, 0xff, 0xff,
    0xa1, 0x20, 0x0, 0x2, 0xa2, 0x10, 0x0, 0x3,
    0x60, 0x0, 0xaf, 0xbb, 0xbf, 0xa0, 0x5f, 0x40,
    0x4f, 0x70, 0xfb, 0x0, 0xd8, 0x0, 0x0, 0xaf,
    0x0, 0xf, 0xa0, 0x5f, 0x40, 0x4f, 0x70, 0xfb,
    0x0, 0xfa, 0x0, 0x0, 0xaf, 0x0, 0xf, 0xa0,
    0x5f, 0x40, 0x4f, 0x70, 0xfb, 0x0, 0xfa, 0x0,
    0x0, 0xaf, 0x0, 0xf, 0xa0, 0x5f, 0x40, 0x4f,
    0x70, 0xfb, 0x0, 0xfa, 0x0, 0x0, 0xaf, 0x24,
    0x7f, 0xa0, 0x5f, 0xff, 0xff, 0x60, 0xff, 0xff,
    0xfa, 0x0, 0x1a, 0xef, 0xff, 0xff, 0xa0, 0x4c,
    0xbb, 0xdf, 0x40, 0xfe, 0xbb, 0xfa, 0x0, 0xf,
    0xfc, 0x96, 0x4f, 0xa0, 0x0, 0x0, 0xbf, 0x10,
    0xfb, 0x0, 0xb7, 0x0, 0x2, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x4, 0xfa, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xa0, 0x0, 0x4f,
    0xf2, 0x0, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xa0, 0x19, 0xff, 0x40, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xa0,
    0xd, 0xc2, 0x0, 0x0, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+81F3 "至" */
    0x3, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x20, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xa, 0xaa, 0xaa, 0xab, 0xff, 0xca, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xfc, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xd1, 0x0, 0x0, 0x3e,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x20, 0x0, 0x0, 0x7, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x8, 0xff, 0xa7,
    0x88, 0x9a, 0xab, 0xcc, 0xdf, 0xff, 0xb0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xed, 0xce, 0xfc, 0x0, 0x0, 0x6b, 0x87, 0x55,
    0x43, 0x22, 0x10, 0x0, 0x0, 0x1, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xaa, 0xaa,
    0xaa, 0xaf, 0xfa, 0xaa, 0xaa, 0xaa, 0xa4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x3, 0x33, 0x33,
    0x33, 0x3f, 0xf3, 0x33, 0x33, 0x33, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x8e, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xe9,

    /* U+84B8 "蒸" */
    0x0, 0x0, 0x3, 0xd8, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x20, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x6a, 0xaa, 0xac, 0xfe,
    0xaa, 0xa2, 0x3a, 0xaa, 0xef, 0xba, 0xaa, 0xa5,
    0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0x64,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xdf, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x6, 0x99, 0x99, 0x99,
    0x82, 0xa, 0xff, 0x81, 0x0, 0x2c, 0x70, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xfe, 0x20,
    0x4, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0xf, 0xfe, 0xe3, 0x8f, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x30, 0xf, 0xe3, 0xef,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x0, 0xf, 0xe0, 0x2d, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0x50, 0x44, 0x4f, 0xe0, 0x0,
    0x9f, 0xfe, 0x82, 0x0, 0x39, 0xff, 0xd3, 0x0,
    0x9f, 0xff, 0x90, 0x0, 0x2, 0xaf, 0xff, 0xe3,
    0x5f, 0xe6, 0x0, 0x0, 0x24, 0x42, 0x0, 0x0,
    0x0, 0x1, 0x7d, 0xa0, 0x4, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x10, 0x0, 0x1, 0x50, 0x0, 0x29, 0x20, 0x0,
    0x0, 0x3f, 0xc0, 0x6, 0xf8, 0x0, 0xa, 0xf5,
    0x0, 0x6f, 0xe3, 0x0, 0x1, 0xef, 0x30, 0x2,
    0xfd, 0x0, 0x2, 0xfd, 0x0, 0x5, 0xff, 0x30,
    0xb, 0xf8, 0x0, 0x0, 0xff, 0x0, 0x0, 0xaf,
    0x50, 0x0, 0x6f, 0xf2, 0x4e, 0xb0, 0x0, 0x0,
    0xbc, 0x20, 0x0, 0x3b, 0x40, 0x0, 0x8, 0xc3,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x4c, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x79, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x97, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x79, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x97, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x1, 0xff, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0xef, 0x20, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x20, 0x0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x20, 0x0, 0x0, 0x1, 0xff, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0x20, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+8A71 "話" */
    0x0, 0x6, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x57, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x59, 0xcf, 0xff, 0x70,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x38, 0xad, 0xff,
    0xff, 0xfc, 0x84, 0x0, 0x0, 0x0, 0x6d, 0x30,
    0x0, 0x3f, 0xec, 0xa7, 0xfd, 0x0, 0x0, 0x0,
    0x89, 0x99, 0x99, 0x99, 0x95, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xc0, 0x8d, 0xdd, 0xdd,
    0xff, 0xdd, 0xdd, 0xdc, 0x8, 0x99, 0x99, 0x99,
    0x60, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x7, 0x99, 0x99, 0x99,
    0x60, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x7, 0x99, 0x99,
    0xfe, 0x99, 0x99, 0x60, 0xe, 0xff, 0xff, 0xff,
    0xf0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xe, 0xf9, 0x99, 0x9e, 0xf0, 0xc, 0xf2, 0x22,
    0x22, 0x22, 0x4f, 0xc0, 0xe, 0xd0, 0x0, 0xd,
    0xf0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xc0,
    0xe, 0xd0, 0x0, 0xd, 0xf0, 0xc, 0xf0, 0x0,
    0x0, 0x0, 0x2f, 0xc0, 0xe, 0xd0, 0x0, 0xd,
    0xf0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xc0,
    0xe, 0xe2, 0x22, 0x2d, 0xf0, 0xc, 0xf0, 0x0,
    0x0, 0x0, 0x2f, 0xc0, 0xe, 0xff, 0xff, 0xff,
    0xf0, 0xc, 0xfc, 0xcc, 0xcc, 0xcc, 0xcf, 0xc0,
    0xe, 0xe7, 0x77, 0x77, 0x70, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xe, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+8A9E "語" */
    0x0, 0x5, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0x9, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x60, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x4,
    0xe5, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x89, 0x99, 0x99, 0x99, 0x95, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x88, 0x8e, 0xfa, 0x88,
    0x88, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x5f, 0xc3,
    0x33, 0x5f, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x5, 0xf9, 0x0, 0x2, 0xfb, 0x0,
    0x0, 0x79, 0x99, 0x99, 0x98, 0x0, 0x0, 0x8f,
    0x50, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x79, 0x99, 0x99, 0x98, 0x5d, 0xdd,
    0xff, 0xdd, 0xdd, 0xef, 0xfd, 0xd2, 0xd, 0xff,
    0xff, 0xff, 0xf5, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x4, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x20, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xd, 0xf9, 0x99, 0x9d, 0xf2, 0xf, 0xf7, 0x77,
    0x77, 0x77, 0xaf, 0x80, 0x0, 0xde, 0x0, 0x0,
    0xaf, 0x20, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xf8,
    0x0, 0xd, 0xe0, 0x0, 0xa, 0xf2, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0xde, 0x0,
    0x0, 0xaf, 0x20, 0xfe, 0x0, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0xd, 0xf2, 0x22, 0x2a, 0xf2, 0xf,
    0xe0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x20, 0xff, 0xcc, 0xcc, 0xcc,
    0xcd, 0xf8, 0x0, 0xd, 0xf8, 0x88, 0x88, 0x81,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xde, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8ABF "調" */
    0x0, 0x8, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x7c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc2,
    0x0, 0x2, 0xfb, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xad, 0x10,
    0x0, 0x9f, 0x0, 0x1, 0xf9, 0x0, 0x7, 0xf3,
    0x99, 0x99, 0xa9, 0x99, 0x80, 0x9f, 0x0, 0x1,
    0xf9, 0x0, 0x7, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x9f, 0x44, 0x45, 0xfb, 0x44, 0x4a, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x66, 0x67, 0xfc, 0x66, 0x6b, 0xf3,
    0xe, 0xff, 0xff, 0xff, 0x20, 0x9f, 0x0, 0x1,
    0xf9, 0x0, 0x7, 0xf3, 0x8, 0x99, 0x99, 0x99,
    0x10, 0x9f, 0x0, 0x1, 0xf9, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xee, 0xee,
    0xff, 0xee, 0xef, 0xf3, 0x8, 0x99, 0x99, 0x99,
    0x0, 0x9f, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xf3,
    0xe, 0xff, 0xff, 0xff, 0x10, 0xaf, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x7, 0x88, 0x88, 0x88, 0x7, 0xf3,
    0x0, 0x10, 0x0, 0x0, 0x0, 0xbe, 0xd, 0xff,
    0xff, 0xff, 0x17, 0xf3, 0xf, 0xff, 0xff, 0xff,
    0x20, 0xcd, 0xd, 0xa0, 0x0, 0x7f, 0x17, 0xf3,
    0xf, 0xe9, 0x99, 0xcf, 0x20, 0xdc, 0xd, 0xa0,
    0x0, 0x7f, 0x17, 0xf3, 0xf, 0xa0, 0x0, 0x7f,
    0x20, 0xfa, 0xd, 0xa0, 0x0, 0x7f, 0x17, 0xf3,
    0xf, 0xa0, 0x0, 0x7f, 0x22, 0xf8, 0xd, 0xff,
    0xff, 0xff, 0x17, 0xf3, 0xf, 0xa0, 0x0, 0x7f,
    0x25, 0xf5, 0xd, 0xd8, 0x88, 0x88, 0x7, 0xf3,
    0xf, 0xb0, 0x0, 0x7f, 0x29, 0xf1, 0x9, 0x70,
    0x0, 0x0, 0x7, 0xf3, 0xf, 0xff, 0xff, 0xff,
    0x2e, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf3,
    0xf, 0xe9, 0x99, 0x99, 0x6f, 0x80, 0x0, 0x0,
    0x0, 0x68, 0x8c, 0xf2, 0xf, 0xa0, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x20, 0x0,

    /* U+8ACB "請" */
    0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xa2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfb, 0x0, 0x5, 0x99, 0x99, 0x9e,
    0xfa, 0x99, 0x99, 0x93, 0x0, 0x0, 0xdf, 0x10,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x45, 0x55, 0x86, 0x55, 0x50, 0x0, 0x0, 0xb,
    0xf3, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf1, 0x67, 0x77, 0x7d, 0xf9, 0x77, 0x77, 0x60,
    0x44, 0x44, 0x44, 0x44, 0x40, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x44, 0x55, 0x55, 0x5c,
    0xf7, 0x55, 0x55, 0x53, 0x28, 0x88, 0x88, 0x88,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x3b, 0xbb, 0xbb, 0xbb,
    0x30, 0x7, 0x87, 0x77, 0x77, 0x77, 0x77, 0x10,
    0x4d, 0xdd, 0xdd, 0xdd, 0x40, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0x30,
    0x1, 0x11, 0x11, 0x11, 0x0, 0xf, 0xc0, 0x0,
    0x0, 0x0, 0xaf, 0x30, 0x3f, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x3f, 0xc9, 0x99, 0xcf, 0x40, 0xf, 0xe7, 0x77,
    0x77, 0x77, 0xcf, 0x30, 0x3f, 0x80, 0x0, 0x7f,
    0x40, 0xf, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0x30,
    0x3f, 0x80, 0x0, 0x7f, 0x40, 0x1f, 0xd6, 0x66,
    0x66, 0x66, 0xcf, 0x30, 0x3f, 0x80, 0x0, 0x7f,
    0x40, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x3f, 0x80, 0x0, 0x7f, 0x40, 0x7f, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0x30, 0x3f, 0xff, 0xff, 0xff,
    0x40, 0xcf, 0x10, 0x0, 0x0, 0x0, 0xaf, 0x30,
    0x3f, 0xc9, 0x99, 0x99, 0x25, 0xfa, 0x0, 0x0,
    0x4, 0x99, 0xdf, 0x20, 0x3f, 0x80, 0x0, 0x0,
    0x7, 0xf2, 0x0, 0x0, 0x3, 0xff, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+9078 "選" */
    0x0, 0x67, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x6f, 0xff, 0xff, 0xf8, 0xe, 0xfe, 0xee,
    0xee, 0x0, 0x0, 0x1e, 0xf4, 0x0, 0x7f, 0x76,
    0x69, 0xf8, 0xf, 0xc6, 0x66, 0xdf, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x7f, 0x10, 0x4, 0xf8, 0xf,
    0xa0, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x6d, 0x20,
    0x7f, 0xff, 0xff, 0xf8, 0xf, 0xfe, 0xee, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x76, 0x66,
    0x63, 0xf, 0xd7, 0x77, 0x77, 0x0, 0xb, 0xee,
    0xef, 0xd1, 0x7f, 0x10, 0x0, 0x12, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0xb, 0xee, 0xff, 0xb0, 0xaf,
    0x79, 0xbe, 0xfb, 0xd, 0xe7, 0x77, 0x77, 0x30,
    0x0, 0x0, 0xbf, 0x30, 0xdf, 0xfc, 0xa7, 0x52,
    0x5, 0xef, 0xff, 0xff, 0x60, 0x0, 0x4, 0xf9,
    0x0, 0x22, 0x0, 0x9d, 0x10, 0x0, 0x7d, 0x20,
    0x0, 0x0, 0x0, 0xd, 0xe1, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x9f, 0x30, 0x0, 0x0, 0x0,
    0x8f, 0x60, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x7, 0xff, 0x89, 0x81,
    0x39, 0x99, 0xef, 0x99, 0x99, 0xdf, 0xa9, 0x98,
    0x0, 0xb, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xbf,
    0x10, 0x0, 0x9f, 0x30, 0x0, 0x0, 0x3, 0x32,
    0x2a, 0xf1, 0x0, 0x0, 0xbf, 0x10, 0x0, 0x9f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xc, 0xe5, 0xdd,
    0xdd, 0xff, 0xdd, 0xdd, 0xef, 0xdd, 0xdd, 0x80,
    0x0, 0x0, 0x1f, 0xa4, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x70, 0x0, 0x0, 0x6f,
    0x60, 0x0, 0x0, 0x7e, 0x70, 0x0, 0xad, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x3c,
    0xfd, 0x10, 0x0, 0x4d, 0xfb, 0x20, 0x0, 0x0,
    0x3, 0xff, 0x70, 0x3a, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x8f, 0xf5, 0x0, 0x0, 0xc, 0xfc, 0xf6,
    0x4f, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xea,
    0x0, 0x0, 0x8f, 0xa0, 0xcf, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe,
    0x10, 0x9, 0xff, 0xfe, 0xcb, 0xaa, 0xaa, 0xab,
    0xbc, 0xcd, 0xe0, 0xc, 0xf3, 0x0, 0x0, 0x17,
    0xbd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x11, 0x11, 0x0, 0x0, 0x0,

    /* U+9215 "鈕" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x38,
    0x88, 0x88, 0x88, 0x88, 0x80, 0x0, 0x0, 0x8,
    0xff, 0xf6, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x4, 0xfd, 0x4e, 0xfa, 0x0,
    0x25, 0x59, 0xfa, 0x55, 0x5f, 0xe0, 0x0, 0x3,
    0xff, 0x20, 0x1c, 0xfc, 0x10, 0x0, 0x7f, 0x60,
    0x0, 0xfe, 0x0, 0x4, 0xff, 0x50, 0x0, 0xa,
    0xf3, 0x0, 0x8, 0xf4, 0x0, 0xf, 0xd0, 0x0,
    0xff, 0xd6, 0x66, 0x66, 0x55, 0x0, 0x0, 0x9f,
    0x30, 0x0, 0xfd, 0x0, 0x9, 0x9f, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0xa, 0xf2, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x33, 0xbf, 0x43, 0x20, 0x0, 0x0,
    0xcf, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0xa,
    0xf0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x2, 0xfb, 0x0, 0x5, 0xbb,
    0xbe, 0xfb, 0xbb, 0x73, 0xdd, 0xdf, 0xfd, 0xdd,
    0xdf, 0xfd, 0xc0, 0x7f, 0xff, 0xff, 0xff, 0xf9,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xa, 0xf0, 0x0, 0x0, 0x0, 0x3f, 0xa0,
    0x0, 0x4f, 0x90, 0x0, 0x7, 0x30, 0xaf, 0x3,
    0xc2, 0x0, 0x4, 0xf8, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0xc9, 0xa, 0xf0, 0x6f, 0x10, 0x0, 0x6f,
    0x60, 0x0, 0x5f, 0x70, 0x0, 0x8, 0xd0, 0xaf,
    0xa, 0xb0, 0x0, 0x7, 0xf5, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x5f, 0xa, 0xf0, 0xe7, 0x0, 0x0,
    0x9f, 0x30, 0x0, 0x7f, 0x60, 0x0, 0x2, 0xf3,
    0xaf, 0x2f, 0x10, 0x0, 0xb, 0xf1, 0x0, 0x8,
    0xf5, 0x0, 0x0, 0x4, 0xa, 0xf0, 0x47, 0x50,
    0x0, 0xdf, 0x0, 0x0, 0x9f, 0x40, 0x0, 0x0,
    0x36, 0xdf, 0xff, 0xfa, 0x0, 0xf, 0xe0, 0x0,
    0xa, 0xf3, 0x0, 0x9, 0xff, 0xff, 0xd9, 0x52,
    0x4c, 0xcc, 0xff, 0xcc, 0xcc, 0xff, 0xdc, 0xc0,
    0x7c, 0x84, 0x10, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x10,

    /* U+9223 "鈣" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x9, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xa0, 0x0, 0x1,
    0xef, 0xfa, 0x10, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0xbf, 0x9b, 0xfd, 0x30,
    0x0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x7, 0xff, 0x50, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x4,
    0xff, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0,
    0xdf, 0xf4, 0x22, 0x22, 0x25, 0x58, 0xc1, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0xa, 0xde, 0xff, 0xff,
    0xff, 0xa0, 0xbf, 0x10, 0xd, 0xff, 0xff, 0xff,
    0x40, 0x21, 0x68, 0x8f, 0xd8, 0x85, 0xb, 0xf1,
    0x0, 0xdf, 0xbb, 0xbb, 0xb3, 0x0, 0x0, 0x1,
    0xfa, 0x0, 0x0, 0xbf, 0x10, 0xd, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0xb,
    0xf1, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x3, 0x88,
    0x88, 0xfd, 0x88, 0x84, 0xbf, 0x10, 0xd, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0x7b, 0xf4, 0x22, 0xef, 0x22, 0x22, 0x32, 0x1,
    0x33, 0x34, 0xfb, 0x33, 0x31, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x1, 0x20, 0x1f, 0xa0,
    0x46, 0x6, 0xa9, 0x99, 0x99, 0x99, 0x99, 0xfd,
    0x0, 0xac, 0x1, 0xfa, 0x9, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x6, 0xf1, 0x1f,
    0xa0, 0xda, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfb, 0x0, 0x2f, 0x51, 0xfa, 0x1f, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0, 0xf8,
    0x1f, 0xa5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf8, 0x0, 0x9, 0x51, 0xfa, 0x14, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0x0, 0x3f, 0xdb, 0xef, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf4, 0x4, 0x9c, 0xff, 0xff, 0xfb,
    0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x8f, 0xfc, 0x95, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xbb, 0xdf, 0xb0, 0x1, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+9664 "除" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x0, 0x0,
    0xcc, 0xcc, 0xcd, 0xa0, 0x0, 0x0, 0x0, 0xdf,
    0x30, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xa, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x8f, 0xa8,
    0xfb, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x6f, 0x50,
    0x0, 0x7, 0xfc, 0x0, 0xaf, 0xb1, 0x0, 0x0,
    0xfc, 0x0, 0xce, 0x0, 0x0, 0x9f, 0xc1, 0x0,
    0xb, 0xfd, 0x20, 0x0, 0xfc, 0x2, 0xf9, 0x0,
    0x1c, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0,
    0xfc, 0x8, 0xf2, 0x6, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xc0, 0xfc, 0xe, 0xc0, 0x8,
    0xe7, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x4c, 0x30,
    0xfc, 0xa, 0xf4, 0x0, 0x11, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xfc, 0x0, 0xde, 0x10,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0xfc, 0x0, 0xe, 0xd0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0xa, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xfc, 0x0, 0x9, 0xfa,
    0xdd, 0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdd, 0x70,
    0xfc, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0xfc, 0x6b, 0xdf, 0xc0,
    0x0, 0x84, 0x0, 0xbf, 0x21, 0x97, 0x0, 0x0,
    0xfc, 0x4f, 0xeb, 0x20, 0x7, 0xf7, 0x0, 0xbf,
    0x20, 0xcf, 0x40, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0xbf, 0x20, 0x1e, 0xe2, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0xaf, 0x40, 0x0, 0xbf,
    0x20, 0x4, 0xfc, 0x0, 0xfc, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0xbf, 0x20, 0x0, 0xaf, 0x60,
    0xfc, 0x0, 0x0, 0x1e, 0xc0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x1f, 0x80, 0xfc, 0x0, 0x0, 0x1,
    0x10, 0x1b, 0xbb, 0xff, 0x10, 0x0, 0x1, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+96FB "電" */
    0x0, 0x79, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x98, 0x0, 0x0, 0xb, 0xdd, 0xdd,
    0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0xf, 0xe7, 0x77, 0x77, 0x77, 0x7f,
    0xf7, 0x77, 0x77, 0x78, 0x7e, 0xf1, 0x0, 0xfd,
    0xc, 0xd9, 0x51, 0x0, 0xfe, 0x0, 0x3, 0x8e,
    0xc0, 0xef, 0x10, 0xf, 0xd0, 0x26, 0xaf, 0xf5,
    0xf, 0xe0, 0x5e, 0xfd, 0x83, 0xe, 0xf1, 0x0,
    0xfd, 0x0, 0x0, 0x5, 0x10, 0xfe, 0x1, 0x51,
    0x0, 0x0, 0xef, 0x10, 0xa, 0x90, 0x24, 0x79,
    0xc7, 0xf, 0xe0, 0xce, 0xc8, 0x51, 0x9, 0xa0,
    0x0, 0x0, 0x8f, 0xff, 0xc9, 0x30, 0xfe, 0x3,
    0x6a, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x1, 0x63,
    0x0, 0x0, 0x6, 0x50, 0x0, 0x0, 0x16, 0x20,
    0x0, 0x0, 0x0, 0x68, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x76, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x1,
    0xfd, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x33, 0x33, 0x4f, 0xd3, 0x33, 0x33,
    0x3f, 0xe0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xc, 0xf2, 0x11, 0x11, 0x2f, 0xd1, 0x11,
    0x11, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0xcf, 0x32,
    0x22, 0x23, 0xfd, 0x22, 0x22, 0x22, 0xfe, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x76, 0x0, 0x0, 0xcf,
    0x54, 0x44, 0x45, 0xfe, 0x44, 0x44, 0x44, 0x44,
    0xc, 0xe0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xb9, 0x99, 0x99,
    0x99, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,

    /* U+FF0C "，" */
    0x3, 0xbb, 0x30, 0xd, 0xff, 0xe0, 0xe, 0xff,
    0xf2, 0x5, 0xdf, 0xf3, 0x0, 0xa, 0xf1, 0x0,
    0x1f, 0xc0, 0x1, 0xcf, 0x40, 0x5e, 0xf6, 0x0,
    0x8c, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+FF1C "＜" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xf8, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdf, 0xf9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xa2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xfa, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xcf, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0xff, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdf, 0xf9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xf8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 93, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 134, .box_w = 4, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 40, .adv_w = 197, .box_w = 8, .box_h = 8, .ofs_x = 2, .ofs_y = 13},
    {.bitmap_index = 72, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 202, .adv_w = 231, .box_w = 12, .box_h = 26, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 358, .adv_w = 383, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 578, .adv_w = 283, .box_w = 18, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 758, .adv_w = 116, .box_w = 3, .box_h = 8, .ofs_x = 2, .ofs_y = 13},
    {.bitmap_index = 770, .adv_w = 141, .box_w = 6, .box_h = 29, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 857, .adv_w = 141, .box_w = 6, .box_h = 29, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 944, .adv_w = 194, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 989, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1087, .adv_w = 116, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 1112, .adv_w = 144, .box_w = 7, .box_h = 2, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1119, .adv_w = 116, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1129, .adv_w = 163, .box_w = 10, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 1259, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1389, .adv_w = 231, .box_w = 11, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1499, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1629, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1759, .adv_w = 231, .box_w = 14, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1899, .adv_w = 231, .box_w = 14, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2039, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2169, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2299, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2429, .adv_w = 231, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2559, .adv_w = 116, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2597, .adv_w = 116, .box_w = 5, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 2650, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2748, .adv_w = 231, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 2804, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2902, .adv_w = 197, .box_w = 10, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3002, .adv_w = 394, .box_w = 23, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3278, .adv_w = 253, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3438, .adv_w = 273, .box_w = 14, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3578, .adv_w = 265, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3728, .adv_w = 286, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3878, .adv_w = 245, .box_w = 12, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3998, .adv_w = 230, .box_w = 12, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4118, .adv_w = 287, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4268, .adv_w = 303, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4418, .adv_w = 122, .box_w = 4, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4458, .adv_w = 223, .box_w = 12, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4578, .adv_w = 269, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4728, .adv_w = 226, .box_w = 12, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4848, .adv_w = 338, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5018, .adv_w = 301, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5168, .adv_w = 309, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5338, .adv_w = 263, .box_w = 14, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5478, .adv_w = 309, .box_w = 18, .box_h = 25, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5703, .adv_w = 264, .box_w = 14, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5843, .adv_w = 248, .box_w = 14, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5983, .adv_w = 249, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6133, .adv_w = 300, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6283, .adv_w = 239, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6433, .adv_w = 365, .box_w = 23, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6663, .adv_w = 238, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6813, .adv_w = 221, .box_w = 15, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6963, .adv_w = 251, .box_w = 14, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7103, .adv_w = 141, .box_w = 6, .box_h = 27, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 7184, .adv_w = 163, .box_w = 10, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7314, .adv_w = 141, .box_w = 7, .box_h = 27, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 7409, .adv_w = 231, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 7481, .adv_w = 233, .box_w = 15, .box_h = 2, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7496, .adv_w = 252, .box_w = 7, .box_h = 7, .ofs_x = 3, .ofs_y = 17},
    {.bitmap_index = 7521, .adv_w = 234, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7611, .adv_w = 257, .box_w = 13, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7748, .adv_w = 212, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7838, .adv_w = 258, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7975, .adv_w = 230, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8073, .adv_w = 135, .box_w = 10, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8183, .adv_w = 235, .box_w = 14, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 8330, .adv_w = 253, .box_w = 12, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8456, .adv_w = 114, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8498, .adv_w = 114, .box_w = 8, .box_h = 27, .ofs_x = -2, .ofs_y = -6},
    {.bitmap_index = 8606, .adv_w = 230, .box_w = 13, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8743, .adv_w = 118, .box_w = 5, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8796, .adv_w = 385, .box_w = 20, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8946, .adv_w = 254, .box_w = 12, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9036, .adv_w = 252, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9141, .adv_w = 258, .box_w = 13, .box_h = 21, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 9278, .adv_w = 258, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 9415, .adv_w = 161, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9483, .adv_w = 195, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9573, .adv_w = 157, .box_w = 10, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9668, .adv_w = 253, .box_w = 12, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9758, .adv_w = 217, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9863, .adv_w = 334, .box_w = 21, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10021, .adv_w = 207, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10119, .adv_w = 217, .box_w = 14, .box_h = 21, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 10266, .adv_w = 198, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10356, .adv_w = 141, .box_w = 8, .box_h = 27, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 10464, .adv_w = 112, .box_w = 3, .box_h = 30, .ofs_x = 2, .ofs_y = -8},
    {.bitmap_index = 10509, .adv_w = 141, .box_w = 8, .box_h = 27, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 10617, .adv_w = 231, .box_w = 13, .box_h = 4, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 10643, .adv_w = 416, .box_w = 24, .box_h = 3, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 10679, .adv_w = 416, .box_w = 24, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10943, .adv_w = 416, .box_w = 22, .box_h = 24, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 11207, .adv_w = 416, .box_w = 24, .box_h = 23, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11483, .adv_w = 416, .box_w = 25, .box_h = 24, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11783, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12096, .adv_w = 416, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12421, .adv_w = 416, .box_w = 25, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12721, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13021, .adv_w = 416, .box_w = 22, .box_h = 24, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 13285, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13585, .adv_w = 416, .box_w = 26, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13884, .adv_w = 416, .box_w = 20, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 14104, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14404, .adv_w = 416, .box_w = 24, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14680, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14993, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15306, .adv_w = 416, .box_w = 26, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15644, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15969, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16294, .adv_w = 416, .box_w = 25, .box_h = 24, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 16594, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16907, .adv_w = 416, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17219, .adv_w = 416, .box_w = 25, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 17507, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17807, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18120, .adv_w = 416, .box_w = 26, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18458, .adv_w = 416, .box_w = 25, .box_h = 24, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18758, .adv_w = 416, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19070, .adv_w = 416, .box_w = 24, .box_h = 23, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 19346, .adv_w = 416, .box_w = 24, .box_h = 24, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 19634, .adv_w = 416, .box_w = 24, .box_h = 24, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 19922, .adv_w = 416, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20234, .adv_w = 416, .box_w = 25, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20534, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20847, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 21147, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21460, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21773, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22098, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 22411, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 22724, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 23037, .adv_w = 416, .box_w = 25, .box_h = 26, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 23362, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 23687, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 23987, .adv_w = 416, .box_w = 24, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 24263, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 24563, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24888, .adv_w = 416, .box_w = 24, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 25152, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25452, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25752, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26052, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26365, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26665, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 26965, .adv_w = 416, .box_w = 26, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 27290, .adv_w = 416, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 27603, .adv_w = 416, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 27928, .adv_w = 416, .box_w = 24, .box_h = 26, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 28240, .adv_w = 416, .box_w = 25, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 28528, .adv_w = 416, .box_w = 6, .box_h = 10, .ofs_x = 10, .ofs_y = 4},
    {.bitmap_index = 28558, .adv_w = 416, .box_w = 21, .box_h = 21, .ofs_x = 2, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xb, 0x2d, 0x3b, 0x14d, 0x219, 0x25c, 0x274,
    0x348, 0x3fa, 0x4a0, 0x5d6, 0x5e3, 0x5f3, 0x7ae, 0x874,
    0xb27, 0xda2, 0xe07, 0xe0f, 0x110f, 0x1185, 0x119e, 0x11c3,
    0x144b, 0x1787, 0x17cb, 0x190d, 0x196f, 0x1d62, 0x1d65, 0x1e23,
    0x1e34, 0x1e96, 0x1ee1, 0x1f88, 0x2005, 0x2154, 0x21c3, 0x23b1,
    0x23d9, 0x2c0d, 0x2d49, 0x2f61, 0x302e, 0x316e, 0x318e, 0x326f,
    0x33f3, 0x36b8, 0x3c00, 0x3c71, 0x3c9e, 0x3cbf, 0x3ccb, 0x4278,
    0x4415, 0x4423, 0x4864, 0x48fb, 0xb10c, 0xb11c
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 19968, .range_length = 45341, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -54, 0, -54, 0,
    0, 0, 0, -26, 0, -45, -6, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    -15, 0, 0, 0, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 36, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -45, 0, -64,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -47, -10, -31, -17, 0,
    -44, 0, 0, 0, -7, 0, 0, 0,
    11, 0, 0, -22, 0, -17, -11, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    -9, -23, 0, -10, -6, -14, -32, -10,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, -5, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -20, -6, -38, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    -16, 0, -6, 11, 11, 0, 0, 3,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -26, 0, -45,
    0, 0, 0, 0, 0, 0, -13, -4,
    -6, 0, 0, -26, -8, -7, 0, 1,
    -7, -5, -20, 10, 0, -6, 0, 0,
    0, 0, 10, -7, -4, -5, -3, -3,
    -5, 0, 0, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    -7, -12, 0, -4, -3, -3, -7, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -7, -6, -6, -7, 0,
    0, 0, 0, 0, 0, -13, 0, 0,
    0, 0, 0, 0, -14, -6, -12, -9,
    -7, -3, -3, -3, -5, -6, 0, 0,
    0, 0, -10, 0, 0, 0, 0, -14,
    -6, -7, -6, 0, -7, 0, 0, 0,
    0, -17, 0, 0, 0, -9, 0, 0,
    0, -6, 0, -20, 0, -12, 0, -6,
    -4, -9, -10, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, -12, 0, -6, 0, -15,
    -6, 0, 0, 0, 0, 0, -35, 0,
    -35, -34, 0, 0, 0, -19, -6, -66,
    -10, 0, 0, 1, 1, -12, 0, -15,
    0, -17, -7, 0, -12, 0, 0, -10,
    -10, -6, -8, -10, -9, -13, -9, -15,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, -10,
    0, -7, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, -12, 0, 0, 0,
    0, 0, 0, -20, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, -20,
    0, -15, 0, 0, 0, 0, -5, -6,
    -10, 0, -5, -9, -7, -7, -6, 0,
    -9, 0, 0, 0, -5, 0, 0, 0,
    -6, 0, 0, -17, -8, -10, -9, -9,
    -10, -7, 0, -41, 0, -72, 0, -26,
    0, 0, 0, 0, -16, 0, -13, 0,
    -12, -57, -14, -37, -27, 0, -36, 0,
    -38, 0, -7, -7, -3, 0, 0, 0,
    0, -10, -6, -18, -17, 0, -18, 0,
    0, 0, 0, 0, -53, -17, -53, -37,
    0, 0, 0, -25, 0, -69, -6, -12,
    0, 0, 0, -12, -6, -39, 0, -21,
    -12, 0, -15, 0, 0, 0, -6, 0,
    0, 0, 0, -7, 0, -10, 0, 0,
    0, -6, 0, -15, 0, 0, 0, 0,
    0, -3, 0, -9, -7, -7, 0, 1,
    2, -3, -2, -6, 0, -3, -6, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -5, 0, 0, 0, -9,
    0, 5, 0, 0, 0, 0, 0, 0,
    0, -7, -7, -10, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -50, -35,
    -50, -43, -10, -10, 0, -20, -12, -60,
    -20, 0, 0, 0, 0, -10, -7, -26,
    0, -35, -32, -9, -35, 0, 0, -23,
    -28, -9, -23, -17, -17, -20, -17, -36,
    0, 0, 0, 0, -9, 0, -9, -16,
    0, 0, 0, -9, 0, -23, -6, 0,
    0, -3, 0, -6, -7, 0, 0, -3,
    0, 0, -6, 0, 0, 0, -3, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    0, 0, -31, -10, -31, -23, 0, 0,
    0, -7, -6, -35, -6, 0, -6, 3,
    0, 0, 0, -10, 0, -11, -8, 0,
    -11, 0, 0, -10, -7, 0, -15, -5,
    -5, -8, -5, -13, 0, 0, 0, 0,
    -17, -6, -17, -15, 0, 0, 0, 0,
    -4, -32, -4, 0, 0, 0, 0, 0,
    0, -4, 0, -9, 0, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -6, 0, -6, 0, -14,
    0, 0, 0, 0, 0, 0, -9, -2,
    -7, -10, -6, 0, 0, 0, 0, 0,
    0, -6, -5, -9, 0, 0, 0, 0,
    0, -9, -6, -9, -7, -6, -9, -7,
    0, 0, 0, 0, -43, -32, -43, -32,
    -12, -12, -5, -7, -7, -47, -8, -7,
    -6, 0, 0, 0, 0, -13, 0, -32,
    -20, 0, -29, 0, 0, -20, -20, -14,
    -17, -7, -12, -17, -7, -23, 0, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    0, -4, -10, -17, -15, 0, -6, -4,
    -4, 0, -7, -9, 0, -9, -11, -10,
    -8, 0, 0, 0, 0, -7, -12, -9,
    -9, -12, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -41, -15, -26, -15, 0,
    -35, 0, 0, 0, 0, 0, 14, 0,
    33, 0, 0, 0, 0, -10, -6, 0,
    5, 0, 0, 0, 0, -26, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, -12, 0, -9, -3, 0, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, -15, 0, -13, -6, 2, -6, 0,
    0, 0, -7, 0, 0, 0, 0, -28,
    0, -10, 0, -3, -22, 0, -13, -8,
    0, -2, 0, 0, 0, 0, -2, -9,
    0, -3, -3, -9, -3, -4, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -10, 0, -7,
    0, 0, -12, 0, 0, -6, -11, 0,
    -6, 0, 0, 0, 0, -6, 0, 2,
    2, 2, 2, 0, 0, 0, 0, -17,
    0, 3, 0, 0, 0, 0, -5, 0,
    0, -10, -10, -12, 0, -9, -6, 0,
    -13, 0, -10, -8, 0, -2, -6, 0,
    0, 0, 0, -6, 0, 1, 1, -5,
    1, -1, 5, 17, 22, 0, -25, -7,
    -25, -8, 0, 0, 11, 0, 0, 0,
    0, 20, 0, 30, 20, 14, 27, 0,
    28, -10, -6, 0, -8, 0, -6, 0,
    -3, 0, 0, 5, 0, -3, 0, -7,
    0, 0, 5, -17, 0, 0, 0, 21,
    0, 0, -18, 0, 0, 0, 0, -13,
    0, 0, 0, 0, -7, 0, 0, -8,
    -7, 0, 0, 0, 16, 0, 0, 0,
    0, -3, -3, 0, 6, -7, 0, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -12, 0, -6,
    0, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    5, -20, 5, 0, 5, 5, -7, 0,
    0, 0, 0, -17, 0, 0, 0, 0,
    -6, 0, 0, -6, -10, 0, -6, 0,
    -6, 0, 0, -11, -7, 0, 0, -5,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, -7, 0, 0, -15, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -27, -12, -27, -17, 11, 11,
    0, -7, 0, -26, 0, 0, 0, 0,
    0, 0, 0, -6, 5, -12, -6, 0,
    -6, 0, 0, 0, -3, 0, 0, 11,
    8, 0, 11, -3, 0, 0, 0, -25,
    0, 3, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -12, 0, -6, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 2, -13,
    2, 3, 5, 5, -13, 0, 0, 0,
    0, -7, 0, 0, 0, 0, -3, 0,
    0, -11, -7, 0, -6, 0, 0, 0,
    -6, -10, 0, 0, 0, -9, 0, 0,
    0, 0, 0, -7, -17, -5, -17, -10,
    0, 0, 0, -6, 0, -20, 0, -10,
    0, -5, 0, 0, -7, -6, 0, -10,
    -3, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -12, 0, 0,
    0, -7, -19, 0, -19, -5, 0, 0,
    0, -3, 0, -15, 0, -12, 0, -5,
    0, -7, -12, 0, 0, -6, -3, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, -9, -7, 0, 0, -12,
    2, -7, -5, 0, 0, 2, 0, 0,
    -6, 0, -3, -17, 0, -8, 0, -6,
    -17, 0, 0, -6, -9, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -17, 0,
    -17, -8, 0, 0, 0, 0, 0, -20,
    0, -10, 0, -3, 0, -3, -5, 0,
    0, -10, -3, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, -7, 0, -12,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    0, 0, 0, 0, -15, 0, 0, -12,
    -6, 0, -4, 0, 0, 0, 0, 0,
    -6, -3, 0, 0, -3, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_NS26 = {
#else
lv_font_t ui_font_NS26 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 32,          /*The maximum line height required by the font*/
    .base_line = 8,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_NS26*/


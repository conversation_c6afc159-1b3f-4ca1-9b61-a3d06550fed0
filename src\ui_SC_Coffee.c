// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Coffee_screen_init(void)
{
    ui_SC_Coffee = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Coffee, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Coffee, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_Coffee1 = lv_label_create(ui_SC_Coffee);
    lv_obj_set_width(ui_LAB_Coffee1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_Coffee1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_Coffee1, 0);
    lv_obj_set_y(ui_LAB_Coffee1, -134);
    lv_obj_set_align(ui_LAB_Coffee1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_Coffee1, "咖啡");
    lv_obj_set_style_text_color(ui_LAB_Coffee1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_Coffee1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_Coffee1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_BACK_MENU1 = lv_label_create(ui_SC_Coffee);
    lv_obj_set_width(ui_LAB_BACK_MENU1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_BACK_MENU1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_BACK_MENU1, -172);
    lv_obj_set_y(ui_LAB_BACK_MENU1, -133);
    lv_obj_set_align(ui_LAB_BACK_MENU1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_BACK_MENU1, "＜主選單");
    lv_obj_add_flag(ui_LAB_BACK_MENU1, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_clear_flag(ui_LAB_BACK_MENU1,
                      LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE);     /// Flags
    lv_obj_set_style_text_color(ui_LAB_BACK_MENU1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_BACK_MENU1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_BACK_MENU1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel3 = lv_obj_create(ui_SC_Coffee);
    lv_obj_set_width(ui_Panel3, 480);
    lv_obj_set_height(ui_Panel3, 240);
    lv_obj_set_x(ui_Panel3, 0);
    lv_obj_set_y(ui_Panel3, 80);
    lv_obj_set_flex_flow(ui_Panel3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_Panel3, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_Panel3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Panel3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel3, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_SMALL_Coffee = lv_imgbtn_create(ui_Panel3);
    lv_imgbtn_set_src(ui_BTN_SMALL_Coffee, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_coffee_s_btn_png, NULL);
    lv_imgbtn_set_src(ui_BTN_SMALL_Coffee, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_coffee_s_btn_active_png, NULL);
    lv_obj_set_width(ui_BTN_SMALL_Coffee, 189);
    lv_obj_set_height(ui_BTN_SMALL_Coffee, 189);
    lv_obj_set_align(ui_BTN_SMALL_Coffee, LV_ALIGN_CENTER);

    ui_Lab_Small_Coffee = lv_label_create(ui_BTN_SMALL_Coffee);
    lv_obj_set_width(ui_Lab_Small_Coffee, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Small_Coffee, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Small_Coffee, 0);
    lv_obj_set_y(ui_Lab_Small_Coffee, 75);
    lv_obj_set_align(ui_Lab_Small_Coffee, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Small_Coffee, "濃縮");
    lv_obj_set_style_text_color(ui_Lab_Small_Coffee, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Small_Coffee, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Small_Coffee, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_BIG_Coffee = lv_imgbtn_create(ui_Panel3);
    lv_imgbtn_set_src(ui_BTN_BIG_Coffee, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_coffee_btn_png, NULL);
    lv_imgbtn_set_src(ui_BTN_BIG_Coffee, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_coffee_btn_active_png, NULL);
    lv_obj_set_width(ui_BTN_BIG_Coffee, 189);
    lv_obj_set_height(ui_BTN_BIG_Coffee, 189);
    lv_obj_set_align(ui_BTN_BIG_Coffee, LV_ALIGN_CENTER);

    ui_Lab_Big_Coffee = lv_label_create(ui_BTN_BIG_Coffee);
    lv_obj_set_width(ui_Lab_Big_Coffee, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Big_Coffee, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Big_Coffee, 0);
    lv_obj_set_y(ui_Lab_Big_Coffee, 75);
    lv_obj_set_align(ui_Lab_Big_Coffee, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Big_Coffee, "美式");
    lv_obj_set_style_text_color(ui_Lab_Big_Coffee, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Big_Coffee, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Big_Coffee, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_LAB_BACK_MENU1, ui_event_LAB_BACK_MENU1, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_SMALL_Coffee, ui_event_BTN_SMALL_Coffee, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_BIG_Coffee, ui_event_BTN_BIG_Coffee, LV_EVENT_ALL, NULL);
    uic_BTN_SMALL_Coffee = ui_BTN_SMALL_Coffee;
    uic_Lab_Small_Coffee = ui_Lab_Small_Coffee;
    uic_BTN_BIG_Coffee = ui_BTN_BIG_Coffee;
    uic_Lab_Big_Coffee = ui_Lab_Big_Coffee;

}

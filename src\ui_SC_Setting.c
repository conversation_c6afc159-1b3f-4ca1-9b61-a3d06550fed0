// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Setting_screen_init(void)
{
    ui_SC_Setting = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Setting, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Setting, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Setting1 = lv_label_create(ui_SC_Setting);
    lv_obj_set_width(ui_Lab_Setting1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Setting1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Setting1, 0);
    lv_obj_set_y(ui_Lab_Setting1, -134);
    lv_obj_set_align(ui_Lab_Setting1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Setting1, "設定");
    lv_obj_set_style_text_color(ui_Lab_Setting1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Setting1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Setting1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_BACK_MENU2 = lv_label_create(ui_SC_Setting);
    lv_obj_set_width(ui_LAB_BACK_MENU2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_BACK_MENU2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_BACK_MENU2, -172);
    lv_obj_set_y(ui_LAB_BACK_MENU2, -133);
    lv_obj_set_align(ui_LAB_BACK_MENU2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_BACK_MENU2, "＜主選單");
    lv_obj_add_flag(ui_LAB_BACK_MENU2, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_clear_flag(ui_LAB_BACK_MENU2,
                      LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE);     /// Flags
    lv_obj_set_style_text_color(ui_LAB_BACK_MENU2, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_BACK_MENU2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_BACK_MENU2, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel4 = lv_obj_create(ui_SC_Setting);
    lv_obj_set_width(ui_Panel4, 480);
    lv_obj_set_height(ui_Panel4, 234);
    lv_obj_set_x(ui_Panel4, 0);
    lv_obj_set_y(ui_Panel4, 83);
    lv_obj_set_flex_flow(ui_Panel4, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_Panel4, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_Panel4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Panel4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel4, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_DECA = lv_imgbtn_create(ui_Panel4);
    lv_imgbtn_set_src(ui_BTN_DECA, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_clean_png, NULL);
    lv_imgbtn_set_src(ui_BTN_DECA, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_clean_active_png, NULL);
    lv_obj_set_width(ui_BTN_DECA, 126);
    lv_obj_set_height(ui_BTN_DECA, 126);
    lv_obj_set_align(ui_BTN_DECA, LV_ALIGN_CENTER);

    ui_Lab_DeCa1 = lv_label_create(ui_BTN_DECA);
    lv_obj_set_width(ui_Lab_DeCa1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_DeCa1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_DeCa1, 0);
    lv_obj_set_y(ui_Lab_DeCa1, 45);
    lv_obj_set_align(ui_Lab_DeCa1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_DeCa1, "除鈣");
    lv_obj_set_style_text_color(ui_Lab_DeCa1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_DeCa1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_DeCa1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_set_Language = lv_imgbtn_create(ui_Panel4);
    lv_imgbtn_set_src(ui_BTN_set_Language, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_language_png, NULL);
    lv_imgbtn_set_src(ui_BTN_set_Language, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_language_active_png, NULL);
    lv_obj_set_width(ui_BTN_set_Language, 126);
    lv_obj_set_height(ui_BTN_set_Language, 126);
    lv_obj_set_align(ui_BTN_set_Language, LV_ALIGN_CENTER);

    ui_Lab_Langauge1 = lv_label_create(ui_BTN_set_Language);
    lv_obj_set_width(ui_Lab_Langauge1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Langauge1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Langauge1, 0);
    lv_obj_set_y(ui_Lab_Langauge1, 45);
    lv_obj_set_align(ui_Lab_Langauge1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Langauge1, "語言");
    lv_obj_set_style_text_color(ui_Lab_Langauge1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Langauge1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Langauge1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_BIG_TEA1 = lv_imgbtn_create(ui_Panel4);
    lv_imgbtn_set_src(ui_BTN_BIG_TEA1, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_service_png, NULL);
    lv_imgbtn_set_src(ui_BTN_BIG_TEA1, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_service_active_png, NULL);
    lv_obj_set_width(ui_BTN_BIG_TEA1, 126);
    lv_obj_set_height(ui_BTN_BIG_TEA1, 126);
    lv_obj_set_align(ui_BTN_BIG_TEA1, LV_ALIGN_CENTER);

    ui_Lab_Service1 = lv_label_create(ui_BTN_BIG_TEA1);
    lv_obj_set_width(ui_Lab_Service1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Service1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Service1, 0);
    lv_obj_set_y(ui_Lab_Service1, 45);
    lv_obj_set_align(ui_Lab_Service1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Service1, "聯絡客服");
    lv_obj_set_style_text_color(ui_Lab_Service1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Service1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Service1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_LAB_BACK_MENU2, ui_event_LAB_BACK_MENU2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_DECA, ui_event_BTN_DECA, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_set_Language, ui_event_BTN_set_Language, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_BIG_TEA1, ui_event_BTN_BIG_TEA1, LV_EVENT_ALL, NULL);
    uic_Lab_Setting1 = ui_Lab_Setting1;
    uic_Lab_DeCa1 = ui_Lab_DeCa1;

}

// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Small_Coffee_screen_init(void)
{
    ui_SC_Small_Coffee = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Small_Coffee, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Small_Coffee, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB_Coffee2 = lv_label_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_LAB_Coffee2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB_Coffee2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB_Coffee2, 0);
    lv_obj_set_y(ui_LAB_Coffee2, -134);
    lv_obj_set_align(ui_LAB_Coffee2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB_Coffee2, "咖啡");
    lv_obj_set_style_text_color(ui_LAB_Coffee2, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB_Coffee2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB_Coffee2, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Small_Coffee1 = lv_label_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_Lab_Small_Coffee1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Small_Coffee1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Small_Coffee1, 1);
    lv_obj_set_y(ui_Lab_Small_Coffee1, 3);
    lv_obj_set_align(ui_Lab_Small_Coffee1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Small_Coffee1, "濃縮");
    lv_obj_set_style_text_color(ui_Lab_Small_Coffee1, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Small_Coffee1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Small_Coffee1, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image3 = lv_img_create(ui_SC_Small_Coffee);
    lv_img_set_src(ui_Image3, &ui_img_coffee_s_active_png);
    lv_obj_set_width(ui_Image3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Image3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Image3, 0);
    lv_obj_set_y(ui_Image3, -46);
    lv_obj_set_align(ui_Image3, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_BAR_Small_Coffee = lv_bar_create(ui_SC_Small_Coffee);
    lv_bar_set_value(ui_BAR_Small_Coffee, 100, LV_ANIM_OFF);
    lv_bar_set_start_value(ui_BAR_Small_Coffee, 0, LV_ANIM_OFF);
    lv_obj_set_width(ui_BAR_Small_Coffee, 332);
    lv_obj_set_height(ui_BAR_Small_Coffee, 24);
    lv_obj_set_x(ui_BAR_Small_Coffee, 0);
    lv_obj_set_y(ui_BAR_Small_Coffee, 37);
    lv_obj_set_align(ui_BAR_Small_Coffee, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_BAR_Small_Coffee, lv_color_hex(0x3A342C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Small_Coffee, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_BAR_Small_Coffee, lv_color_hex(0xFDBA25), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BAR_Small_Coffee, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BAR_Small_Coffee, lv_color_hex(0xFD7C00), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BAR_Small_Coffee, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    ui_Lab_Brewing2 = lv_label_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_Lab_Brewing2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Brewing2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Brewing2, -263);
    lv_obj_set_y(ui_Lab_Brewing2, 216);
    lv_obj_set_align(ui_Lab_Brewing2, LV_ALIGN_TOP_RIGHT);
    lv_label_set_text(ui_Lab_Brewing2, "沖泡中...");
    lv_obj_set_style_text_color(ui_Lab_Brewing2, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Brewing2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Brewing2, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LAB__Small_Coffee_progress = lv_label_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_LAB__Small_Coffee_progress, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_LAB__Small_Coffee_progress, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_LAB__Small_Coffee_progress, 6);
    lv_obj_set_y(ui_LAB__Small_Coffee_progress, 72);
    lv_obj_set_align(ui_LAB__Small_Coffee_progress, LV_ALIGN_CENTER);
    lv_label_set_text(ui_LAB__Small_Coffee_progress, "10%");
    lv_obj_set_style_text_color(ui_LAB__Small_Coffee_progress, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_LAB__Small_Coffee_progress, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_LAB__Small_Coffee_progress, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Wait3 = lv_label_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_Lab_Wait3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Wait3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Wait3, 278);
    lv_obj_set_y(ui_Lab_Wait3, 215);
    lv_label_set_text(ui_Lab_Wait3, "，請稍候");
    lv_obj_set_style_text_color(ui_Lab_Wait3, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Wait3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Wait3, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_Cancel3 = lv_btn_create(ui_SC_Small_Coffee);
    lv_obj_set_width(ui_BTN_Cancel3, 100);
    lv_obj_set_height(ui_BTN_Cancel3, 45);
    lv_obj_set_x(ui_BTN_Cancel3, 0);
    lv_obj_set_y(ui_BTN_Cancel3, 119);
    lv_obj_set_align(ui_BTN_Cancel3, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Cancel3, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_Cancel3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BTN_Cancel3, lv_color_hex(0xFDBA25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_Cancel3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_Cancel3, lv_color_hex(0xFD7C00), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_Cancel3, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Cancel3 = lv_label_create(ui_BTN_Cancel3);
    lv_obj_set_width(ui_Lab_Cancel3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Cancel3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_Cancel3, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Cancel3, "取消");
    lv_obj_set_style_text_color(ui_Lab_Cancel3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Cancel3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Cancel3, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_SC_Small_Coffee = ui_SC_Small_Coffee;
    uic_BAR_Small_Coffee = ui_BAR_Small_Coffee;
    uic_LAB__Small_Coffee_progress = ui_LAB__Small_Coffee_progress;

}

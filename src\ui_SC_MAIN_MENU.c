// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_MAIN_MENU_screen_init(void)
{
    ui_SC_MAIN_MENU = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_MAIN_MENU, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_MAIN_MENU, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label2 = lv_label_create(ui_SC_MAIN_MENU);
    lv_obj_set_width(ui_Label2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label2, 0);
    lv_obj_set_y(ui_Label2, -134);
    lv_obj_set_align(ui_Label2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label2, "主選單");
    lv_obj_set_style_text_color(ui_Label2, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label2, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel1 = lv_obj_create(ui_SC_MAIN_MENU);
    lv_obj_set_width(ui_Panel1, 440);
    lv_obj_set_height(ui_Panel1, 286);
    lv_obj_set_x(ui_Panel1, 15);
    lv_obj_set_y(ui_Panel1, 32);
    lv_obj_set_flex_flow(ui_Panel1, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_Panel1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_Panel1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Panel1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel1, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_TEA = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_TEA, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_tea_png, NULL);
    lv_imgbtn_set_src(ui_BTN_TEA, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_tea_active_png, NULL);
    lv_obj_set_width(ui_BTN_TEA, 128);
    lv_obj_set_height(ui_BTN_TEA, 126);
    lv_obj_set_x(ui_BTN_TEA, -110);
    lv_obj_set_y(ui_BTN_TEA, -22);
    lv_obj_set_align(ui_BTN_TEA, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_TEA, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_TEA, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Tea = lv_label_create(ui_BTN_TEA);
    lv_obj_set_width(ui_Lab_Tea, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Tea, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Tea, 0);
    lv_obj_set_y(ui_Lab_Tea, 51);
    lv_obj_set_align(ui_Lab_Tea, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Tea, "沖茶");
    lv_obj_set_style_text_color(ui_Lab_Tea, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Tea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Tea, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Tea, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Tea, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_BTN_Coffee = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_Coffee, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_coffee_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Coffee, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_coffee_active_png, NULL);
    lv_obj_set_width(ui_BTN_Coffee, 128);
    lv_obj_set_height(ui_BTN_Coffee, 126);
    lv_obj_set_x(ui_BTN_Coffee, 32);
    lv_obj_set_y(ui_BTN_Coffee, -21);
    lv_obj_set_align(ui_BTN_Coffee, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_Coffee, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_Coffee, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Coffee = lv_label_create(ui_BTN_Coffee);
    lv_obj_set_width(ui_Lab_Coffee, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Coffee, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Coffee, 0);
    lv_obj_set_y(ui_Lab_Coffee, 51);
    lv_obj_set_align(ui_Lab_Coffee, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Coffee, "咖啡");
    lv_obj_set_style_text_color(ui_Lab_Coffee, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Coffee, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Coffee, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Coffee, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Coffee, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_BTN_SETTING = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_SETTING, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_settings_png, NULL);
    lv_imgbtn_set_src(ui_BTN_SETTING, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_settings_active_png, NULL);
    lv_obj_set_width(ui_BTN_SETTING, 128);
    lv_obj_set_height(ui_BTN_SETTING, 126);
    lv_obj_set_x(ui_BTN_SETTING, 31);
    lv_obj_set_y(ui_BTN_SETTING, -21);
    lv_obj_set_align(ui_BTN_SETTING, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_SETTING, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_SETTING, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Setting = lv_label_create(ui_BTN_SETTING);
    lv_obj_set_width(ui_Lab_Setting, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Setting, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Setting, 0);
    lv_obj_set_y(ui_Lab_Setting, 51);
    lv_obj_set_align(ui_Lab_Setting, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Setting, "設定");
    lv_obj_set_style_text_color(ui_Lab_Setting, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Setting, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Setting, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Setting, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Setting, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_BTN_WATER = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_WATER, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_water_png, NULL);
    lv_imgbtn_set_src(ui_BTN_WATER, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_water_active_png, NULL);
    lv_obj_set_width(ui_BTN_WATER, 128);
    lv_obj_set_height(ui_BTN_WATER, 126);
    lv_obj_set_x(ui_BTN_WATER, 31);
    lv_obj_set_y(ui_BTN_WATER, -21);
    lv_obj_set_align(ui_BTN_WATER, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_WATER, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_WATER, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Water = lv_label_create(ui_BTN_WATER);
    lv_obj_set_width(ui_Lab_Water, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Water, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Water, 0);
    lv_obj_set_y(ui_Lab_Water, 51);
    lv_obj_set_align(ui_Lab_Water, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Water, "熱水");
    lv_obj_set_style_text_color(ui_Lab_Water, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Water, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Water, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Water, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Water, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_BTN_Steam = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_Steam, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_steam_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Steam, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_steam_active_png, NULL);
    lv_obj_set_width(ui_BTN_Steam, 128);
    lv_obj_set_height(ui_BTN_Steam, 126);
    lv_obj_set_x(ui_BTN_Steam, 31);
    lv_obj_set_y(ui_BTN_Steam, -21);
    lv_obj_set_align(ui_BTN_Steam, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_Steam, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_Steam, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Steam = lv_label_create(ui_BTN_Steam);
    lv_obj_set_width(ui_Lab_Steam, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Steam, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Steam, 0);
    lv_obj_set_y(ui_Lab_Steam, 51);
    lv_obj_set_align(ui_Lab_Steam, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Steam, "奶泡蒸氣");
    lv_obj_set_style_text_color(ui_Lab_Steam, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Steam, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Steam, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Steam, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Steam, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_BTN_Clean = lv_imgbtn_create(ui_Panel1);
    lv_imgbtn_set_src(ui_BTN_Clean, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_clean_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Clean, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_clean_active_png, NULL);
    lv_obj_set_width(ui_BTN_Clean, 128);
    lv_obj_set_height(ui_BTN_Clean, 126);
    lv_obj_set_x(ui_BTN_Clean, 31);
    lv_obj_set_y(ui_BTN_Clean, -21);
    lv_obj_set_align(ui_BTN_Clean, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_BTN_Clean, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BTN_Clean, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Clean = lv_label_create(ui_BTN_Clean);
    lv_obj_set_width(ui_Lab_Clean, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Clean, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Clean, 0);
    lv_obj_set_y(ui_Lab_Clean, 51);
    lv_obj_set_align(ui_Lab_Clean, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Clean, "清潔");
    lv_obj_set_style_text_color(ui_Lab_Clean, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Clean, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Clean, &ui_font_NS_19, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui_Lab_Clean, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui_Lab_Clean, 255, LV_PART_MAIN | LV_STATE_PRESSED);

    lv_obj_add_event_cb(ui_BTN_TEA, ui_event_BTN_TEA, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_Coffee, ui_event_BTN_Coffee, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_SETTING, ui_event_BTN_SETTING, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_WATER, ui_event_BTN_WATER, LV_EVENT_ALL, NULL);
    uic_SC_MAIN_MENU = ui_SC_MAIN_MENU;
    uic_Lab_Tea = ui_Lab_Tea;
    uic_Lab_Setting = ui_Lab_Setting;
    uic_BTN_WATER = ui_BTN_WATER;
    uic_Lab_Water = ui_Lab_Water;
    uic_BTN_Steam = ui_BTN_Steam;
    uic_Lab_Steam = ui_Lab_Steam;
    uic_BTN_Clean = ui_BTN_Clean;
    uic_Lab_Clean = ui_Lab_Clean;

}

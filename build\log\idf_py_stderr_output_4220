CMake Warning (dev) in CMakeLists.txt:
  No project() command is present.  The top-level CMakeLists.txt file must
  contain a literal, direct call to the project() command.  Add a line of
  code such as

    project(ProjectName)

  near the top of the file, but after cmake_minimum_required().

  C<PERSON>ake is pretending there is a "project(Project)" command on the first
  line.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) in CMakeLists.txt:
  cmake_minimum_required() should be called prior to this top-level project()
  call.  Please see the cmake-commands(7) manual for usage documentation of
  both commands.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Error at D:/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:67 (message):
  The C compiler

    "D:/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang.exe"

  is not able to compile a simple test program.

  It fails with the following output:

    Change Dir: 'D:/Arduino/Project/PK200_Teaffic/build/CMakeFiles/CMakeScratch/TryCompile-jkrxr6'
    
    Run Build Command(s): D:/Espressif/tools/ninja/1.12.1/ninja.exe -v cmTC_5b031
    [1/2] D:\Espressif\tools\esp-clang\esp-18.1.2_20240912\esp-clang\bin\clang.exe    -MD -MT CMakeFiles/cmTC_5b031.dir/testCCompiler.c.obj -MF CMakeFiles\cmTC_5b031.dir\testCCompiler.c.obj.d -o CMakeFiles/cmTC_5b031.dir/testCCompiler.c.obj -c D:/Arduino/Project/PK200_Teaffic/build/CMakeFiles/CMakeScratch/TryCompile-jkrxr6/testCCompiler.c
    [2/2] C:\WINDOWS\system32\cmd.exe /C "cd . && D:\Espressif\tools\esp-clang\esp-18.1.2_20240912\esp-clang\bin\clang.exe   CMakeFiles/cmTC_5b031.dir/testCCompiler.c.obj -o cmTC_5b031.exe -Wl,--out-implib,libcmTC_5b031.dll.a -Wl,--major-image-version,0,--minor-image-version,0  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 && cd ."
    FAILED: cmTC_5b031.exe 
    C:\WINDOWS\system32\cmd.exe /C "cd . && D:\Espressif\tools\esp-clang\esp-18.1.2_20240912\esp-clang\bin\clang.exe   CMakeFiles/cmTC_5b031.dir/testCCompiler.c.obj -o cmTC_5b031.exe -Wl,--out-implib,libcmTC_5b031.dll.a -Wl,--major-image-version,0,--minor-image-version,0  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 && cd ."
    ld.lld: error: unknown argument '--major-image-version'
    ld.lld: error: unknown argument '--minor-image-version'
    ld.lld: error: cannot open 0: No such file or directory
    ld.lld: error: cannot open 0: No such file or directory
    ld.lld: error: unable to find library -lkernel32
    ld.lld: error: unable to find library -luser32
    ld.lld: error: unable to find library -lgdi32
    ld.lld: error: unable to find library -lwinspool
    ld.lld: error: unable to find library -lshell32
    ld.lld: error: unable to find library -lole32
    ld.lld: error: unable to find library -loleaut32
    ld.lld: error: unable to find library -luuid
    ld.lld: error: unable to find library -lcomdlg32

    ld.lld: error: unable to find library -ladvapi32

    clang: error: ld command failed with exit code 1 (use -v to see invocation)

    ninja: build stopped: subcommand failed.
    
    

  

  CMake will not be able to correctly generate this project.
Call Stack (most recent call first):
  CMakeLists.txt



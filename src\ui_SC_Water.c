// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: Teaffic

#include "ui.h"

void ui_SC_Water_screen_init(void)
{
    ui_SC_Water = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_Water, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_Water, &ui_img_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Water1 = lv_label_create(ui_SC_Water);
    lv_obj_set_width(ui_Lab_Water1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Water1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Water1, 0);
    lv_obj_set_y(ui_Lab_Water1, -134);
    lv_obj_set_align(ui_Lab_Water1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Water1, "熱水");
    lv_obj_set_style_text_color(ui_Lab_Water1, lv_color_hex(0xAAA49F), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Water1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Water1, &ui_font_NS_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Water2 = lv_label_create(ui_SC_Water);
    lv_obj_set_width(ui_Lab_Water2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Water2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Water2, 1);
    lv_obj_set_y(ui_Lab_Water2, 3);
    lv_obj_set_align(ui_Lab_Water2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Water2, "熱水");
    lv_obj_set_style_text_color(ui_Lab_Water2, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Water2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Water2, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image8 = lv_img_create(ui_SC_Water);
    lv_img_set_src(ui_Image8, &ui_img_water_active_png);
    lv_obj_set_width(ui_Image8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Image8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Image8, 0);
    lv_obj_set_y(ui_Image8, -46);
    lv_obj_set_align(ui_Image8, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image8, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Lab_Water_ing = lv_label_create(ui_SC_Water);
    lv_obj_set_width(ui_Lab_Water_ing, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Water_ing, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Lab_Water_ing, -247);
    lv_obj_set_y(ui_Lab_Water_ing, 193);
    lv_obj_set_align(ui_Lab_Water_ing, LV_ALIGN_TOP_RIGHT);
    lv_label_set_text(ui_Lab_Water_ing, "出水中....");
    lv_obj_set_style_text_color(ui_Lab_Water_ing, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Water_ing, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Water_ing, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label40 = lv_label_create(ui_SC_Water);
    lv_obj_set_width(ui_Label40, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label40, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label40, 230);
    lv_obj_set_y(ui_Label40, 191);
    lv_label_set_text(ui_Label40, "，小心燙手");
    lv_obj_set_style_text_color(ui_Label40, lv_color_hex(0xD2934C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label40, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label40, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_Cancel6 = lv_btn_create(ui_SC_Water);
    lv_obj_set_width(ui_BTN_Cancel6, 100);
    lv_obj_set_height(ui_BTN_Cancel6, 45);
    lv_obj_set_x(ui_BTN_Cancel6, 0);
    lv_obj_set_y(ui_BTN_Cancel6, 119);
    lv_obj_set_align(ui_BTN_Cancel6, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Cancel6, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_Cancel6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BTN_Cancel6, lv_color_hex(0xFDBA25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_Cancel6, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_Cancel6, lv_color_hex(0xFD7C00), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_Cancel6, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Lab_Stop = lv_label_create(ui_BTN_Cancel6);
    lv_obj_set_width(ui_Lab_Stop, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_Stop, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_Stop, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_Stop, "停止");
    lv_obj_set_style_text_color(ui_Lab_Stop, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Lab_Stop, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Lab_Stop, &ui_font_NS26, LV_PART_MAIN | LV_STATE_DEFAULT);

    uic_SC_Water = ui_SC_Water;
    uic_Lab_Water1 = ui_Lab_Water1;
    uic_Lab_Water_ing = ui_Lab_Water_ing;

}
